---
trigger: always_on
---

# Docker Deployment Rules

## Comprehensive Docker-Only Deployment Guidelines

### Core Principles
- **ALL services MUST be deployed as Docker containers**
- **Use Traefik reverse proxy for all routing (development port 80 only)**
- **All data persistence via .data folder (NO Docker volumes)**
- **Local Docker network for direct service communication**
- **All testing through reverse proxy**

## 1. Project Structure for Docker Deployment

```
project-root/
├── .data/                     # Persistence (NO Docker volumes)
│   ├── postgres/              # Database data
│   ├── redis/                 # Cache data
│   ├── minio/                 # Object storage
│   ├── uploads/               # File uploads
│   └── logs/                  # Application logs
├── docker/                    # Docker configurations
│   ├── traefik/               # Reverse proxy config
│   │   ├── traefik.yml        # Main Traefik config
│   │   ├── dynamic/           # Dynamic configurations
│   │   └── certs/             # SSL certificates
│   ├── services/              # Service-specific configs
│   │   ├── api/
│   │   │   ├── Dockerfile
│   │   │   ├── Dockerfile.dev
│   │   │   └── healthcheck.sh
│   │   ├── frontend/
│   │   │   ├── Dockerfile
│   │   │   ├── Dockerfile.dev
│   │   │   └── nginx.conf
│   │   └── database/
│   │       ├── init.sql
│   │       └── backup.sh
│   └── scripts/               # Docker utility scripts
├── services/                  # Microservices
│   ├── api/                   # Backend API
│   ├── frontend/              # React frontend
│   └── shared/                # Shared libraries
├── docker-compose.yml         # Production
├── docker-compose.dev.yml     # Development
├── docker-compose.test.yml    # Testing
└── Makefile                   # Docker commands
```

## 2. Traefik Reverse Proxy Configuration

### Main Traefik Configuration
```yaml
# docker/traefik/traefik.yml
global:
  checkNewVersion: false
  sendAnonymousUsage: false

api:
  dashboard: true
  debug: true

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: "app-network"
  file:
    directory: "/etc/traefik/dynamic"
    watch: true

certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /letsencrypt/acme.json
      httpChallenge:
        entryPoint: web

log:
  level: INFO
  filePath: "/var/log/traefik/traefik.log"

accessLog:
  filePath: "/var/log/traefik/access.log"
```

### Development Environment Rules

#### Docker Compose Structure Requirements
- **MUST** use single Docker network for all services
- **MUST** implement health checks for all database and cache services
- **MUST** use restart policies (unless-stopped) for all services
- **MUST** mount .data folder for all persistent data
- **MUST** configure Traefik labels for all web-accessible services
- **MUST** use environment variables for all configuration
- **MUST** implement proper service dependencies with health conditions

#### Service Configuration Standards
- **Traefik**: MUST expose port 80 only, dashboard on separate subdomain
- **Database**: MUST use Alpine images, implement health checks, persist to .data
- **Cache**: MUST use Redis Alpine, implement health checks, persist to .data
- **Storage**: MUST use MinIO, separate console access, persist to .data
- **Application Services**: MUST use development Dockerfiles, implement health checks

#### Network and Routing Rules
- **ALL** services MUST communicate via internal Docker network
- **ALL** external access MUST go through Traefik reverse proxy
- **ALL** services MUST use localhost subdomains (api.localhost, minio.localhost)
- **NO** direct port exposure except Traefik port 80

## 3. Dockerfile Standards

### Development Dockerfile Rules
- **MUST** use Alpine Linux base images for smaller footprint
- **MUST** create and use non-root user (nodejs:1001)
- **MUST** implement health checks for all application services
- **MUST** use multi-stage builds for production
- **MUST** copy package files before source code for better caching
- **MUST** use npm ci for reproducible builds
- **MUST** expose only necessary ports
- **MUST** use proper signal handling (dumb-init for production)

### Security Requirements
- **NEVER** run containers as root user
- **MUST** use specific image tags, never 'latest' in production
- **MUST** implement proper file permissions
- **MUST** minimize attack surface with minimal base images
- **MUST** scan images for vulnerabilities before deployment

## 4. Production Deployment Rules

### Multi-Stage Build Requirements
- **MUST** use multi-stage builds for production images
- **MUST** separate build stage from runtime stage
- **MUST** use --only=production for npm installs
- **MUST** clean npm cache after installation
- **MUST** copy only necessary files to production stage

### Production Security Standards
- **MUST** install dumb-init for proper signal handling
- **MUST** use non-root user in production containers
- **MUST** implement proper file ownership and permissions
- **MUST** use ENTRYPOINT with dumb-init for Node.js services
- **MUST** use nginx:alpine for frontend static serving

### Health Check Requirements
- **MUST** implement health checks for all production services
- **MUST** use appropriate intervals (30s) and timeouts (3s)
- **MUST** provide proper health check endpoints
- **MUST** fail fast with appropriate retry counts (3)

## 5. Testing Environment Rules

### Test Infrastructure Requirements
- **MUST** use separate test network isolated from development
- **MUST** use tmpfs for test database and cache (no persistence)
- **MUST** use test-specific environment variables
- **MUST** run tests in containers matching development environment
- **MUST** implement proper test service dependencies

### Test Execution Standards
- **MUST** support unit, integration, and e2e testing in containers
- **MUST** use CI=true environment for frontend tests
- **MUST** provide test-specific database and cache instances
- **MUST** clean up test containers after execution
- **MUST** support parallel test execution when possible

### Test Data Management
- **MUST** use ephemeral storage (tmpfs) for test data
- **MUST** reset test environment between test runs
- **MUST** provide test fixtures and seed data
- **MUST** isolate test data from development data

## 6. Automation and Scripting Rules

### Makefile Requirements
- **MUST** provide standardized commands for all Docker operations
- **MUST** implement setup, start, stop, logs, shell access commands
- **MUST** support testing commands (unit, integration, e2e)
- **MUST** provide build and deployment commands
- **MUST** implement cleanup and reset functionality
- **MUST** support backup and restore operations
- **MUST** provide monitoring and health check commands

### Command Standards
- **MUST** use .PHONY declarations for all targets
- **MUST** provide informative echo messages for all operations
- **MUST** support SERVICE parameter for service-specific operations
- **MUST** implement proper error handling and cleanup
- **MUST** use consistent naming conventions

### Automation Requirements
- **MUST** automate .data folder creation and permissions
- **MUST** automate Docker network creation
- **MUST** provide one-command environment setup
- **MUST** support environment-specific deployments
- **MUST** implement automated backup with timestamps

## 7. Environment Configuration Rules

### Environment Variable Standards
- **MUST** use separate .env files for each environment (dev, staging, production)
- **MUST** use NODE_ENV to distinguish environments
- **MUST** use container service names for internal URLs (postgres:5432, redis:6379)
- **MUST** use localhost subdomains for development external URLs
- **MUST** use HTTPS URLs for production external access
- **MUST** use environment variable substitution for sensitive values in production

### Required Environment Variables
- **MUST** define DATABASE_URL with proper connection strings
- **MUST** define REDIS_URL for cache connections
- **MUST** define storage service endpoints (MINIO_ENDPOINT)
- **MUST** define JWT secrets with minimum 32 characters
- **MUST** define CORS_ALLOWED_ORIGINS for security
- **MUST** use strong, unique secrets for each environment

### Security Requirements
- **NEVER** commit .env files with actual secrets to version control
- **MUST** provide .env.example templates for each environment
- **MUST** use secrets management systems in production
- **MUST** rotate secrets regularly
- **MUST** validate all required environment variables on startup

## 8. Security Standards

### Container Security Requirements
- **MUST** run all containers as non-root users
- **MUST** use minimal base images (Alpine Linux preferred)
- **MUST** implement health checks for all services
- **MUST** scan all images for vulnerabilities before deployment
- **MUST** use secrets management for sensitive data
- **MUST** implement proper file permissions and ownership
- **NEVER** include secrets or credentials in Docker images

### Network Security Requirements
- **MUST** use internal Docker networks for service communication
- **MUST** expose only necessary ports through Traefik reverse proxy
- **MUST** implement proper CORS policies for web services
- **MUST** use HTTPS in production environments
- **MUST** implement network segmentation between environments
- **NEVER** expose database or cache ports directly to host

### Data Security
- Encrypt data at rest in .data folder
- Implement proper backup strategies
- Use secure database connections
- Implement audit logging

## 9. Monitoring and Logging

### Container Monitoring
```yaml
# Add to docker-compose.yml
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./docker/prometheus:/etc/prometheus
      - ./.data/prometheus:/prometheus
    networks:
      - app-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.localhost`)"

  grafana:
    image: grafana/grafana:latest
    volumes:
      - ./.data/grafana:/var/lib/grafana
    networks:
      - app-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.localhost`)"
```

This comprehensive Docker deployment ruleset ensures all services are containerized, properly orchestrated, and follow security best practices while maintaining development efficiency.
