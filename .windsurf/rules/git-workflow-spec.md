---
trigger: always_on
---

# Git Workflow Rules

## Branch Hierarchy Standards

### Branch Types
- **Default Branch** (`main` or `master`): Production-ready code only
- **Feature Branches** (`feat-XXX`): Persistent branches for feature development  
- **Change Branches** (`change-N`): Temporary branches for individual changes

### Branch Structure
```
main
├── feat-001 (persistent)
│   ├── change-1 (temporary)
│   ├── change-2 (temporary)
│   └── change-N (temporary)
├── feat-002 (persistent)
└── feat-XXX (persistent)
```

## Branch Rules

### Default Branch Rules
- **NEVER** commit directly to main/master
- **MUST** merge only from feature branches
- **MUST** require integration testing before merge
- **MUST** maintain linear history

### Feature Branch Rules
- **MUST** use naming: `feat-001`, `feat-002` (zero-padded 3 digits)
- **MUST** branch from default branch
- **MUST** be persistent (never delete)
- **MUST** merge only from change branches
- **MUST** correspond to todo.txt feature entries

### Change Branch Rules
- **MUST** use naming: `change-1`, `change-2` within feature context
- **MUST** branch from parent feature branch
- **MUST** be temporary (delete after merge)
- **MUST** contain focused, single-purpose changes
- **MUST** pass all tests before merge

## Feature Tracking Rules

### todo.txt Format
- **MUST** use format: `FEAT-XXX: [STATUS] Description`
- **MUST** use status: PLANNED | IN_PROGRESS | TESTING | INTEGRATION | COMPLETE | BLOCKED
- **MUST** maintain in project root

### Feature Number Rules
- **MUST** be immutable once assigned
- **MUST** be sequential (001, 002, 003...)
- **MUST** be zero-padded 3 digits
- **MUST** persist even if feature cancelled

## Workflow Process Rules

### Feature Creation
- **MUST** use `create-feature.sh XXX "Description"`
- **MUST** update todo.txt status to IN_PROGRESS
- **MUST** create feat-XXX branch from main

### Development Cycle
- **MUST** use `create-change.sh N` for new work
- **MUST** make focused changes in change branch
- **MUST** test thoroughly before merge
- **MUST** use `merge-change.sh N` to merge back

### Quality Gates
- **MUST** pass all tests before change merge
- **MUST** pass integration tests before feature merge
- **MUST** update todo.txt status appropriately
- **MUST** clean up temporary branches after merge

## Commit Message Standards

### Format
- **MUST** use: `<type>(<scope>): <description>`
- **MUST** reference feature number in scope when applicable

### Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `refactor`: Code refactoring
- `test`: Test additions/modifications
- `chore`: Maintenance tasks
- `merge`: Branch merges

### Examples
- `feat(FEAT-007): add user dashboard component`
- `fix(FEAT-007): resolve authentication timeout`
- `merge: change-2 into feat-007`

## Emergency Procedures

### Hotfix Rules
- **MUST** create hotfix branch from main
- **MUST** make minimal fix only
- **MUST** merge directly to main
- **MUST** merge back to active feature branches
- **MUST** delete hotfix branch after merge

### Rollback Rules
- **MUST** create rollback branch from main
- **MUST** use git revert for specific commits
- **MUST** test rollback thoroughly
- **MUST** merge rollback to main if needed

## AI Assistant Integration

### Required Actions
- **MUST** check current branch before changes
- **MUST** reference feature numbers in commits
- **MUST** update todo.txt when changing feature state
- **MUST** use workflow scripts for branch operations
- **NEVER** bypass workflow automation
