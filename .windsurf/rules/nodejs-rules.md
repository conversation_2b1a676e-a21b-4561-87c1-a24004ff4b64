---
trigger: always_on
---

# Node.js Development Rules

## Complete Guidelines for Node.js/TypeScript Development with Docker

### 1. Docker-First Development Environment

#### Container Commands Requirements
- **MUST** execute all npm commands within Docker containers
- **MUST** route all services through <PERSON><PERSON><PERSON><PERSON> reverse proxy
- **MUST** use docker-compose for all development workflows
- **NEVER** run npm directly on host system
- **NEVER** bypass container environment for development

#### Environment Management Standards
- **MUST** use separate .env files for each environment (dev, staging, production)
- **MUST** validate all required environment variables on startup
- **MUST** implement proper secrets management
- **MUST** monitor container health and resource usage
- **NEVER** commit secrets or credentials to version control

### 2. Project Architecture & Structure

#### Domain-Driven Design (DDD) Requirements
- **MUST** organize code by business domains (bounded contexts)
- **MUST** separate domains, shared code, and infrastructure layers
- **MUST** implement 3-layer architecture: Controller-Service-Repository
- **MUST** use dependency injection for service layer
- **NEVER** mix business logic with HTTP concerns
- **NEVER** allow direct database access from controllers

#### Directory Structure Standards
- **MUST** use `src/domains/` for business logic organization
- **MUST** use `src/shared/` for cross-domain utilities
- **MUST** use `src/infrastructure/` for external integrations
- **MUST** separate concerns: controllers, services, repositories, models, schemas
- **MUST** co-locate domain-specific tests with domain code

### 3. Database Integration Standards

#### ORM Requirements
- **MUST** use Drizzle ORM for type-safe database operations
- **MUST** define schemas with proper TypeScript types
- **MUST** use parameterized queries to prevent SQL injection
- **MUST** implement database migrations with version control
- **NEVER** use raw SQL strings without parameterization
- **NEVER** expose database implementation details to business logic

#### Schema Design Rules
- **MUST** use auto-generated Zod schemas from Drizzle schemas
- **MUST** implement proper foreign key relationships
- **MUST** use appropriate data types and constraints
- **MUST** implement soft deletes with `is_active` flags
- **MUST** include `created_at` and `updated_at` timestamps

### 4. Validation Standards

#### Input Validation Requirements
- **MUST** use Zod for all input validation
- **MUST** validate request body, query parameters, and path parameters
- **MUST** implement custom validation rules for business logic
- **MUST** provide clear, actionable error messages
- **NEVER** trust client input without validation
- **NEVER** expose internal validation errors to clients

#### Validation Middleware Standards
- **MUST** implement centralized validation middleware
- **MUST** handle validation errors consistently
- **MUST** support nested object validation
- **MUST** validate file uploads and multipart data

### 5. Authentication & Security Standards

#### Password Security Requirements
- **MUST** use bcrypt with minimum 12 salt rounds
- **MUST** implement password complexity requirements
- **MUST** hash passwords before database storage
- **NEVER** store plain text passwords
- **NEVER** log or expose password hashes

#### JWT Authentication Standards
- **MUST** implement access and refresh token pattern
- **MUST** use strong, unique secrets (minimum 32 characters)
- **MUST** implement token expiration and rotation
- **MUST** validate tokens on every protected request
- **MUST** implement proper token revocation
- **NEVER** store sensitive data in JWT payload

### 6. API Design Standards

#### REST API Requirements
- **MUST** follow RESTful conventions for resource naming
- **MUST** use proper HTTP methods (GET, POST, PUT, DELETE, PATCH)
- **MUST** implement consistent response formats
- **MUST** support pagination for list endpoints
- **MUST** implement proper HTTP status codes
- **NEVER** use verbs in URL paths
- **NEVER** expose internal IDs or implementation details

#### Response Format Standards
- **MUST** use consistent JSON response structure
- **MUST** include success indicators and metadata
- **MUST** implement proper content-type headers
- **MUST** support API versioning strategy

### 7. TypeScript Integration Standards

#### TypeScript Requirements
- **MUST** use strict TypeScript configuration
- **MUST** define proper interfaces for all props and data structures
- **MUST** avoid `any` types except for specific use cases
- **MUST** implement proper type guards for runtime validation
- **MUST** use explicit return types for public functions

#### Component Architecture (for React integration)
- **MUST** use functional components with hooks
- **MUST** implement proper component composition patterns
- **MUST** create reusable, configurable shared components
- **MUST** implement role-based component rendering
- **MUST** maintain proper state management patterns

### 8. Error Handling Standards

#### Error Classification Requirements
- **MUST** implement custom error classes with proper HTTP status codes
- **MUST** distinguish between operational and programming errors
- **MUST** implement global error handling middleware
- **MUST** provide consistent error response format
- **NEVER** expose stack traces in production
- **NEVER** leak internal implementation details in error messages

#### Error Response Standards
- **MUST** return standardized error objects with code, message, and details
- **MUST** implement proper HTTP status codes
- **MUST** provide actionable error messages for clients
- **MUST** log errors with sufficient context for debugging

### 9. Logging Standards

#### Logging Requirements
- **MUST** use Winston for structured logging
- **MUST** implement different log levels (error, warn, info, debug)
- **MUST** log all HTTP requests with response times
- **MUST** log authentication events and security incidents
- **MUST** implement log rotation and retention policies
- **NEVER** log sensitive data (passwords, tokens, PII)

#### Log Format Standards
- **MUST** use JSON format for structured logging
- **MUST** include timestamps, request IDs, and user context
- **MUST** implement correlation IDs for request tracing
- **MUST** separate application logs from access logs

### 10. Testing Standards

#### Testing Requirements
- **MUST** implement unit tests for all business logic
- **MUST** implement integration tests for API endpoints
- **MUST** achieve minimum 80% code coverage
- **MUST** use test databases separate from development
- **MUST** implement test fixtures and factories
- **NEVER** use production data in tests

#### Testing Framework Standards
- **MUST** use Vitest for unit and integration testing
- **MUST** implement proper test isolation and cleanup
- **MUST** use supertest for HTTP endpoint testing
- **MUST** mock external dependencies in unit tests
- **MUST** run all tests within Docker containers

### 11. Performance Standards

#### Performance Requirements
- **MUST** implement database connection pooling
- **MUST** use caching strategies for frequently accessed data
- **MUST** implement request rate limiting
- **MUST** optimize database queries and avoid N+1 problems
- **MUST** implement proper pagination for large datasets

#### Monitoring and Observability
- **MUST** implement health check endpoints
- **MUST** expose metrics for monitoring (response times, error rates)
- **MUST** implement distributed tracing for complex operations
- **MUST** monitor resource usage and performance bottlenecks

### 12. Code Quality Standards

#### Linting and Formatting Requirements
- **MUST** use ESLint with TypeScript-specific rules
- **MUST** use Prettier for consistent code formatting
- **MUST** implement pre-commit hooks for quality checks
- **MUST** enforce consistent import ordering and organization
- **NEVER** disable linting rules without justification

#### Development Workflow Standards
- **MUST** fix Fast Refresh issues immediately
- **MUST** maintain working state after each change
- **MUST** test components in isolation before integration
- **MUST** validate responsive design and accessibility
- **MUST** check container logs for errors before task completion

### 13. Docker Integration Standards

#### Container Requirements
- **MUST** use multi-stage Docker builds for production
- **MUST** run containers as non-root users
- **MUST** use Alpine Linux base images for smaller footprint
- **MUST** implement proper health checks
- **MUST** use .dockerignore to exclude unnecessary files
- **NEVER** include secrets or credentials in Docker images

#### Development Container Standards
- **MUST** support hot-reload in development containers
- **MUST** mount source code as volumes for development
- **MUST** use consistent port mapping and networking
- **MUST** implement proper signal handling for graceful shutdown

### 14. Configuration Management

#### Environment Configuration Requirements
- **MUST** use environment variables for all configuration
- **MUST** validate all required environment variables on startup
- **MUST** use different configurations for different environments
- **MUST** implement configuration schema validation with Zod
- **NEVER** hardcode configuration values in source code
- **NEVER** commit secrets or credentials to version control

#### Configuration Standards
- **MUST** provide .env.example templates
- **MUST** document all required environment variables
- **MUST** use secure defaults where possible
- **MUST** implement configuration hot-reloading for development

This comprehensive Node.js ruleset provides enforceable standards for building scalable, maintainable, and secure Node.js applications with Docker-first development, proper architecture patterns, and modern tooling practices.
