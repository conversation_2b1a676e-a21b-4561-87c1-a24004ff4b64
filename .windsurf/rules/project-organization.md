---
trigger: always_on
---

# Project Organization & File Structure Rules

## CRITICAL: Project Management Segregation

### MANDATORY: All project management files MUST be in `.project/` folder
- **NEVER** place project management files in the root directory
- **NEVER** mix project management scripts with application code
- **ALWAYS** use the `.project/` folder for all non-code project assets

### Required Folder Structure
```
project-root/
├── .project/                    # PROJECT MANAGEMENT ONLY
│   ├── charter.md              # Project charter & feature management
│   ├── changelog.md            # Version history & releases
│   ├── scripts/                # Automation scripts
│   │   ├── create-feature.sh   # Feature creation automation
│   │   ├── create-change.sh    # Change branch automation
│   │   ├── merge-change.sh     # Change merge automation
│   │   ├── complete-feature.sh # Feature completion automation
│   │   └── manage-project.sh   # Project management CLI
│   ├── templates/              # Project templates
│   │   ├── feature-template.md # Feature request template
│   │   └── bug-template.md     # Bug report template
│   ├── docs/                   # Project documentation
│   │   ├── architecture.md     # System architecture
│   │   ├── deployment.md       # Deployment procedures
│   │   └── onboarding.md       # Developer onboarding
│   └── data/                   # Project data files
│       ├── metrics.json        # Project metrics
│       └── backups/            # Data backups
├── .windsurf/rules/            # DEVELOPMENT RULES ONLY
│   ├── universal-principles.md
│   ├── nodejs-rules.md
│   ├── python-rules.md
│   ├── workflow-methodology.md
│   ├── git-workflow-spec.md
│   └── project-organization.md # This file
├── care/                       # PATIENT-FACING APP CODE
├── desk/                       # PROFESSIONAL APP CODE
├── api/                        # API CODE ONLY
├── docker-compose.yml          # Infrastructure
├── .env                        # Environment config
└── README.md                   # Project overview
```

## File Placement Rules

### ✅ CORRECT Placement
- **Project Charter**: `.project/charter.md`
- **Changelog**: `.project/changelog.md`
- **Scripts**: `.project/scripts/*.sh`
- **Documentation**: `.project/docs/*.md`
- **Templates**: `.project/templates/*.md`
- **Metrics**: `.project/data/metrics.json`

### ❌ FORBIDDEN Placement
- **Root directory**: `project-charter.md`, `changelog.md`, `*.sh`
- **Application folders**: `care/scripts/`, `desk/scripts/`, `api/project/`
- **Mixed concerns**: Any project management files in code directories

## AI Assistant Enforcement Rules

### Before ANY File Creation
1. **Check file type**: Is this project management or application code?
2. **Verify location**: Is the file in the correct folder?
3. **Enforce structure**: Move files to correct location if needed
4. **Update references**: Fix any broken paths after moving files

### Project Management File Types
- **Charter/Planning**: `.project/charter.md`, `.project/roadmap.md`
- **Automation**: `.project/scripts/*.sh`, `.project/scripts/*.py`
- **Documentation**: `.project/docs/*.md`
- **Templates**: `.project/templates/*.md`
- **Data/Metrics**: `.project/data/*.json`, `.project/data/*.csv`
- **Backups**: `.project/data/backups/*`

### Application Code File Types
- **Source Code**: `care/src/*`, `desk/src/*`, `api/src/*`
- **Tests**: `care/tests/*`, `desk/tests/*`, `api/tests/*`
- **Configuration**: `care/config/*`, `desk/config/*`, `api/config/*`
- **Assets**: `care/public/*`, `desk/public/*`, `care/assets/*`, `desk/assets/*`

## Script Path References

### Update All Script References
```bash
# ✅ CORRECT - Use .project/scripts/
./.project/scripts/create-feature.sh 026 "New Feature"
./.project/scripts/manage-project.sh request 027 "Another Feature"

# ❌ WRONG - Never use root directory
./create-feature.sh 026 "New Feature"
./manage-project.sh request 027 "Another Feature"
```

### Environment Variables
```bash
# Set project scripts path
export PROJECT_SCRIPTS_PATH=".project/scripts"
export PROJECT_DOCS_PATH=".project/docs"
export PROJECT_DATA_PATH=".project/data"

# Use in scripts
${PROJECT_SCRIPTS_PATH}/create-feature.sh
```

## Git Integration Rules

### .gitignore Updates
```gitignore
# Project management working files
.project/data/temp/
.project/data/cache/
.project/data/backups/*.bak

# Keep project structure
!.project/
!.project/scripts/
!.project/docs/
!.project/templates/
```

### Commit Message Standards
```bash
# ✅ CORRECT - Specify project management changes
git commit -m "project: add feature request template"
git commit -m "project: update automation scripts"
git commit -m "project: enhance project charter structure"

# ✅ CORRECT - Application code changes
git commit -m "feat(auth): implement role-based access"
git commit -m "fix(api): resolve authentication timeout"
```

## Documentation Standards

### Project Documentation Location
- **Architecture**: `.project/docs/architecture.md`
- **Deployment**: `.project/docs/deployment.md`
- **API Docs**: `.project/docs/api.md`
- **User Guides**: `.project/docs/user-guide.md`

### Code Documentation Location
- **Component Docs**: `care/src/components/README.md`, `desk/src/components/README.md`
- **API Docs**: `api/src/routes/README.md`
- **Technical Specs**: `care/docs/technical-specs.md`, `desk/docs/technical-specs.md`

## Automation Integration

### Script Execution Paths
```bash
# All project scripts must use relative paths from project root
cd /path/to/project-root
./.project/scripts/create-feature.sh

# Scripts must handle path resolution
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
```

### Cross-Script References
```bash
# ✅ CORRECT - Reference other project scripts
source "${PROJECT_ROOT}/.project/scripts/common-functions.sh"

# ✅ CORRECT - Reference project data
CHARTER_FILE="${PROJECT_ROOT}/.project/charter.md"
CHANGELOG_FILE="${PROJECT_ROOT}/.project/changelog.md"
```

## Quality Assurance

### Pre-Commit Checks
- [ ] All project management files in `.project/` folder
- [ ] No project management files in root directory
- [ ] All script paths updated to use `.project/scripts/`
- [ ] Documentation properly categorized
- [ ] Git references updated

### File Organization Validation
```bash
# Check for misplaced files
find . -maxdepth 1 -name "*.sh" -not -path "./.project/*" | wc -l
# Should return 0

find . -maxdepth 1 -name "*charter*" -not -path "./.project/*" | wc -l
# Should return 0

find . -maxdepth 1 -name "*changelog*" -not -path "./.project/*" | wc -l
# Should return 0
```

## Migration Checklist

### When Implementing This Structure
1. **Create `.project/` folder structure**
2. **Move existing files**:
   - `project-charter.md` → `.project/charter.md`
   - `changelog.md` → `.project/changelog.md`
   - `.windsurf/rules/*.sh` → `.project/scripts/*.sh`
3. **Update all script references**
4. **Update documentation paths**
5. **Update git workflows**
6. **Test all automation scripts**
7. **Update README.md with new structure**

## Benefits of This Structure

### Clean Separation
- **Project management** concerns isolated from **application code**
- **Scripts and automation** don't clutter the main codebase
- **Documentation** properly organized by purpose
- **Version control** history cleaner with proper categorization

### Scalability
- **Easy to add** new project management tools
- **Simple to maintain** automation scripts
- **Clear ownership** of different file types
- **Better collaboration** with defined boundaries

### Professional Standards
- **Industry standard** project organization
- **Clear project structure** for new team members
- **Maintainable** long-term project evolution
- **Consistent** with enterprise development practices

## ENFORCEMENT: AI Assistant Must Follow

### Every File Operation Must:
1. **Identify file purpose**: Project management vs application code
2. **Verify correct location**: Check against folder structure rules
3. **Enforce placement**: Move files if in wrong location
4. **Update references**: Fix any broken paths
5. **Validate structure**: Ensure compliance with organization rules

### Never Allow:
- Project management files in root directory
- Scripts mixed with application code
- Documentation in wrong categories
- Broken path references after file moves

This structure ensures clean, professional, and maintainable project organization that scales with project growth and team expansion.
