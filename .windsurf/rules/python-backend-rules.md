---
trigger: always_on
---

# Python Backend Development Rules

## Industry-Standard Guidelines for FastAPI/Django Development

### 1. Project Architecture & Structure

#### Domain-Driven Design Requirements
- **MUST** organize code by business domains (bounded contexts)
- **MUST** implement clean architecture with clear layer separation
- **MUST** use dependency injection for service layer
- **MUST** separate domains, shared utilities, and infrastructure
- **NEVER** mix business logic with HTTP request handling
- **NEVER** allow direct database access from route handlers

#### Directory Structure Standards
- **MUST** use `src/domains/` for business logic organization
- **MUST** use `src/shared/` for cross-domain utilities
- **MUST** use `src/infrastructure/` for external integrations
- **MUST** co-locate domain-specific code (models, schemas, services, repositories)
- **MUST** separate concerns clearly with proper module boundaries

### 2. Database Integration Standards

#### ORM Requirements
- **MUST** use SQLAlchemy with Alembic for database operations
- **MUST** define models with proper relationships and constraints
- **MUST** implement database migrations with version control
- **MUST** use connection pooling for production deployments
- **NEVER** use raw SQL without parameterization
- **NEVER** expose ORM implementation details to business logic

#### Model Design Standards
- **MUST** inherit from base model with common fields (id, created_at, updated_at)
- **MUST** implement soft deletes with `is_active` flags
- **MUST** use appropriate field types and constraints
- **MUST** implement proper indexing for performance
- **MUST** define clear relationships with foreign keys

### 3. API Framework Standards

#### FastAPI Requirements
- **MUST** use FastAPI for high-performance async API development
- **MUST** implement proper dependency injection patterns
- **MUST** use Pydantic models for request/response validation
- **MUST** implement proper error handling and status codes
- **MUST** generate OpenAPI documentation automatically
- **NEVER** bypass Pydantic validation for performance

#### Route Organization Standards
- **MUST** organize routes by domain with clear prefixes
- **MUST** implement proper HTTP method usage (GET, POST, PUT, DELETE, PATCH)
- **MUST** use consistent response formats across all endpoints
- **MUST** implement proper pagination for list endpoints
- **MUST** support API versioning strategy

### 4. Validation Standards

#### Pydantic Schema Requirements
- **MUST** use Pydantic models for all input/output validation
- **MUST** implement custom validators for business logic
- **MUST** use proper field types and constraints
- **MUST** provide clear validation error messages
- **NEVER** trust client input without validation
- **NEVER** expose internal validation details to clients

#### Data Validation Standards
- **MUST** validate all request data at API boundaries
- **MUST** implement nested object validation
- **MUST** handle file upload validation
- **MUST** sanitize input data to prevent injection attacks

### 5. Authentication & Security Standards

#### Password Security Requirements
- **MUST** use bcrypt or Argon2 for password hashing
- **MUST** implement proper salt rounds (minimum 12 for bcrypt)
- **MUST** enforce password complexity requirements
- **NEVER** store plain text passwords
- **NEVER** log or expose password hashes

#### JWT Authentication Standards
- **MUST** implement access and refresh token pattern
- **MUST** use strong, unique secrets (minimum 32 characters)
- **MUST** implement proper token expiration and rotation
- **MUST** validate tokens on every protected endpoint
- **MUST** implement token revocation mechanisms

### 6. Repository Pattern Standards

#### Repository Requirements
- **MUST** implement repository pattern for data access abstraction
- **MUST** use generic base repository for common operations
- **MUST** implement domain-specific repositories for complex queries
- **MUST** handle database transactions properly
- **NEVER** expose SQLAlchemy sessions to business logic

#### Query Optimization Standards
- **MUST** implement proper query optimization and indexing
- **MUST** use eager loading to avoid N+1 query problems
- **MUST** implement query result caching where appropriate
- **MUST** monitor and optimize slow queries

### 7. Service Layer Standards

#### Business Logic Requirements
- **MUST** implement all business logic in service layer
- **MUST** use dependency injection for service dependencies
- **MUST** handle business rule validation
- **MUST** implement proper error handling and logging
- **NEVER** implement business logic in route handlers

#### Transaction Management
- **MUST** implement proper transaction boundaries
- **MUST** handle rollback scenarios gracefully
- **MUST** use context managers for transaction management
- **MUST** implement proper isolation levels

### 8. Testing Standards

#### Testing Requirements
- **MUST** implement unit tests for all business logic
- **MUST** implement integration tests for API endpoints
- **MUST** achieve minimum 80% code coverage
- **MUST** use pytest for testing framework
- **MUST** implement proper test isolation and cleanup

#### Testing Patterns
- **MUST** use test fixtures and factories for data setup
- **MUST** mock external dependencies in unit tests
- **MUST** use test databases separate from development
- **MUST** implement realistic test scenarios

### 9. Error Handling Standards

#### Exception Handling Requirements
- **MUST** implement custom exception classes with proper HTTP status codes
- **MUST** distinguish between operational and programming errors
- **MUST** implement global exception handlers
- **MUST** provide consistent error response format
- **NEVER** expose stack traces in production

#### Error Response Standards
- **MUST** return standardized error objects with code, message, and details
- **MUST** implement proper HTTP status codes
- **MUST** provide actionable error messages for clients
- **MUST** log errors with sufficient context for debugging

### 10. Logging Standards

#### Logging Requirements
- **MUST** implement structured logging with proper levels
- **MUST** log all HTTP requests with response times
- **MUST** log authentication events and security incidents
- **MUST** implement log rotation and retention policies
- **NEVER** log sensitive data (passwords, tokens, PII)

#### Log Format Standards
- **MUST** use JSON format for structured logging
- **MUST** include timestamps, request IDs, and user context
- **MUST** implement correlation IDs for request tracing
- **MUST** separate application logs from access logs

### 11. Async Programming Standards

#### Async/Await Requirements
- **MUST** use async/await for I/O operations
- **MUST** implement proper async context management
- **MUST** handle async exceptions properly
- **MUST** use async database operations
- **NEVER** block the event loop with synchronous operations

#### Background Task Standards
- **MUST** use Celery or similar for background job processing
- **MUST** implement proper task queuing and retry logic
- **MUST** monitor task execution and failures
- **MUST** implement task result storage and retrieval

### 12. Configuration Management

#### Environment Configuration Requirements
- **MUST** use Pydantic Settings for configuration management
- **MUST** validate all configuration values on startup
- **MUST** use environment variables for all configuration
- **MUST** implement different configurations for different environments
- **NEVER** hardcode configuration values in source code

#### Security Configuration Standards
- **MUST** use secure defaults for all configuration
- **MUST** implement proper secrets management
- **MUST** validate security-critical configuration
- **NEVER** commit secrets to version control

### 13. Performance Standards

#### Performance Requirements
- **MUST** implement database connection pooling
- **MUST** use caching strategies for frequently accessed data
- **MUST** implement request rate limiting
- **MUST** optimize database queries and indexing
- **MUST** implement proper pagination for large datasets

#### Monitoring and Observability
- **MUST** implement health check endpoints
- **MUST** expose metrics for monitoring (response times, error rates)
- **MUST** implement distributed tracing for complex operations
- **MUST** monitor resource usage and performance bottlenecks

### 14. Docker Integration Standards

#### Container Requirements
- **MUST** use multi-stage Docker builds for production
- **MUST** run containers as non-root users
- **MUST** use Alpine Linux base images for smaller footprint
- **MUST** implement proper health checks
- **NEVER** include secrets in Docker images

#### Production Deployment Standards
- **MUST** use WSGI/ASGI servers (Gunicorn, Uvicorn) for production
- **MUST** implement proper process management
- **MUST** use environment-specific configuration
- **MUST** implement graceful shutdown handling

### 15. Code Quality Standards

#### Python Standards
- **MUST** follow PEP 8 style guidelines
- **MUST** use type hints for all functions and methods
- **MUST** implement proper docstrings for public APIs
- **MUST** use Black for code formatting
- **MUST** use mypy for static type checking

#### Linting and Quality Tools
- **MUST** use flake8 or ruff for linting
- **MUST** implement pre-commit hooks for quality checks
- **MUST** use isort for import organization
- **MUST** enforce consistent code style across the project

This comprehensive Python Backend ruleset provides enforceable standards for building scalable, maintainable, and secure FastAPI applications with modern Python development practices.
