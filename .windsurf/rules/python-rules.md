---
trigger: always_on
---

# Python Development Rules

## Environment Management Rules

### Virtual Environment Standards
- **MUST** use virtual environments for all projects
- **MUST** use requirements.txt for dependencies
- **MUST** pin dependency versions in production
- **MUST** separate dev and production requirements
- **NEVER** install packages globally

### Docker Integration Requirements
- **MUST** use multi-stage builds for production
- **MUST** use Alpine Linux base images
- **MUST** run containers as non-root users
- **MUST** implement health checks
- **MUST** use .dockerignore appropriately

## Code Quality Standards

### PEP 8 Compliance Rules
- **MUST** follow PEP 8 style guidelines
- **MUST** use type hints for all functions
- **MUST** implement proper docstrings
- **MUST** use Black for code formatting
- **MUST** use isort for import organization

### Type Safety Requirements
- **MUST** use type hints everywhere
- **MUST** use mypy for static type checking
- **MUST** avoid Any types except specific cases
- **MUST** implement proper type guards
- **MUST** use dataclasses for data structures

## FastAPI/Web Framework Rules

### API Design Standards
- **MUST** use FastAPI for async API development
- **MUST** implement dependency injection
- **MUST** use Pydantic for validation
- **MUST** generate OpenAPI documentation
- **MUST** implement proper HTTP status codes

### Route Organization Requirements
- **MUST** organize routes by domain
- **MUST** use consistent response formats
- **MUST** implement proper pagination
- **MUST** support API versioning
- **MUST** use proper HTTP methods

## Database Integration Rules

### ORM Requirements
- **MUST** use SQLAlchemy with Alembic
- **MUST** define proper relationships
- **MUST** implement database migrations
- **MUST** use connection pooling
- **NEVER** use raw SQL without parameterization

### Model Design Standards
- **MUST** inherit from base model
- **MUST** implement soft deletes
- **MUST** use appropriate field types
- **MUST** implement proper indexing
- **MUST** define clear relationships

## Validation & Security Rules

### Input Validation Requirements
- **MUST** use Pydantic for all validation
- **MUST** implement custom validators
- **MUST** validate at API boundaries
- **MUST** sanitize input data
- **NEVER** trust client input

### Authentication Standards
- **MUST** use bcrypt for password hashing
- **MUST** implement JWT with refresh tokens
- **MUST** validate tokens on protected endpoints
- **MUST** implement proper session management
- **MUST** use strong secrets (32+ characters)

## Error Handling Rules

### Exception Management Requirements
- **MUST** implement custom exception classes
- **MUST** use proper HTTP status codes
- **MUST** provide consistent error formats
- **MUST** log errors with context
- **NEVER** expose stack traces in production

### Logging Standards
- **MUST** use structured logging
- **MUST** implement different log levels
- **MUST** log authentication events
- **MUST** implement log rotation
- **NEVER** log sensitive data

## Testing Requirements

### Testing Standards
- **MUST** implement unit tests for business logic
- **MUST** use pytest framework
- **MUST** achieve 80%+ test coverage
- **MUST** use test fixtures and factories
- **MUST** mock external dependencies

### Test Organization Rules
- **MUST** separate unit and integration tests
- **MUST** use realistic test data
- **MUST** test error conditions
- **MUST** implement API endpoint tests
- **MUST** use test databases

## Performance & Monitoring Rules

### Async Programming Standards
- **MUST** use async/await for I/O operations
- **MUST** implement proper async context management
- **MUST** handle async exceptions
- **NEVER** block the event loop

### Performance Requirements
- **MUST** implement database connection pooling
- **MUST** use caching strategies
- **MUST** implement request rate limiting
- **MUST** optimize database queries
- **MUST** implement proper pagination

### Monitoring Standards
- **MUST** implement health check endpoints
- **MUST** expose metrics for monitoring
- **MUST** implement distributed tracing
- **MUST** monitor resource usage
- **MUST** implement alerting

## Configuration Management Rules

### Environment Configuration Requirements
- **MUST** use Pydantic Settings
- **MUST** validate configuration on startup
- **MUST** use environment variables
- **MUST** implement different env configs
- **NEVER** hardcode configuration values

### Security Configuration Standards
- **MUST** use secure defaults
- **MUST** implement secrets management
- **MUST** validate security-critical config
- **NEVER** commit secrets to version control
