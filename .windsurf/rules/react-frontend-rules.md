---
trigger: always_on
---

# React Frontend Development Rules

## Industry-Standard Guidelines for React Applications

### 1. Project Architecture & Structure

#### Feature-Based Organization Requirements
- **MUST** organize components by feature, not by technical type
- **MUST** use feature-based directory structure with clear boundaries
- **MUST** separate reusable UI components from feature-specific components
- **MUST** co-locate related files (components, hooks, services, types)
- **NEVER** organize by technical layers (components/, services/, utils/)

#### Component Architecture Standards
- **MUST** use functional components with hooks
- **MUST** implement proper component composition patterns
- **MUST** use TypeScript for all components and props
- **MUST** implement proper prop interfaces with validation
- **NEVER** use class components except for error boundaries

### 2. Styling Standards

#### CSS Framework Requirements
- **MUST** use Tailwind CSS for consistent styling
- **MUST** implement centralized theming with CSS variables
- **MUST** use responsive design principles (mobile-first)
- **MUST** implement dark mode support
- **NEVER** use inline styles except for dynamic values
- **NEVER** use CSS-in-JS libraries that impact performance

#### Design System Standards
- **MUST** create reusable UI component library
- **MUST** use consistent spacing, typography, and color scales
- **MUST** implement proper component variants and sizes
- **MUST** document component usage patterns

### 3. State Management Standards

#### State Management Requirements
- **MUST** use React hooks for local component state
- **MUST** lift state up when shared between components
- **MUST** use Context API for application-wide state
- **MUST** implement proper state normalization for complex data
- **NEVER** use Redux unless absolutely necessary for complex state

#### Hook Standards
- **MUST** create custom hooks for reusable stateful logic
- **MUST** use proper dependency arrays in useEffect
- **MUST** implement cleanup functions for side effects
- **MUST** handle loading, error, and success states consistently

### 4. Routing Standards

#### Router Requirements
- **MUST** use React Router for client-side routing
- **MUST** implement lazy loading for route components
- **MUST** use protected routes for authentication
- **MUST** implement proper error boundaries for route errors
- **MUST** support deep linking and browser history

#### Navigation Standards
- **MUST** implement consistent navigation patterns
- **MUST** provide loading states during route transitions
- **MUST** handle 404 errors gracefully
- **MUST** implement breadcrumb navigation for complex apps

### 5. Form Handling Standards

#### Form Validation Requirements
- **MUST** use Zod for form validation schemas
- **MUST** implement client-side validation with server-side backup
- **MUST** provide real-time validation feedback
- **MUST** handle form submission states (loading, success, error)
- **NEVER** trust client-side validation alone

#### Form Component Standards
- **MUST** create reusable form components
- **MUST** implement proper accessibility (ARIA labels, focus management)
- **MUST** support keyboard navigation
- **MUST** provide clear error messages and recovery options

### 6. API Integration Standards

#### HTTP Client Requirements
- **MUST** use Axios or Fetch with proper error handling
- **MUST** implement request/response interceptors
- **MUST** handle authentication tokens automatically
- **MUST** implement proper timeout and retry logic
- **NEVER** make API calls directly in components

#### Data Fetching Standards
- **MUST** implement loading states for all async operations
- **MUST** handle error states with user-friendly messages
- **MUST** implement proper caching strategies
- **MUST** use custom hooks for data fetching logic

### 7. Performance Standards

#### Rendering Optimization Requirements
- **MUST** use React.memo for expensive components
- **MUST** implement proper key props for lists
- **MUST** use useMemo and useCallback for expensive computations
- **MUST** implement code splitting at route level
- **NEVER** create objects or functions in render methods

#### Bundle Optimization Standards
- **MUST** implement lazy loading for large components
- **MUST** use dynamic imports for code splitting
- **MUST** optimize images and assets
- **MUST** monitor bundle size and performance metrics

### 8. Accessibility Standards

#### WCAG Compliance Requirements
- **MUST** implement proper ARIA labels and roles
- **MUST** support keyboard navigation
- **MUST** provide alternative text for images
- **MUST** maintain proper color contrast ratios
- **MUST** implement focus management for dynamic content

#### Screen Reader Support
- **MUST** use semantic HTML elements
- **MUST** provide screen reader announcements for dynamic changes
- **MUST** implement proper heading hierarchy
- **MUST** test with actual screen readers

### 9. Testing Standards

#### Testing Requirements
- **MUST** implement unit tests for all components
- **MUST** use React Testing Library for component testing
- **MUST** test user interactions, not implementation details
- **MUST** achieve minimum 80% test coverage
- **NEVER** test internal component state directly

#### Testing Patterns
- **MUST** use realistic test data and scenarios
- **MUST** mock external dependencies and APIs
- **MUST** implement integration tests for critical user flows
- **MUST** use accessibility testing tools

### 10. Code Quality Standards

#### TypeScript Requirements
- **MUST** use strict TypeScript configuration
- **MUST** define proper interfaces for all props and state
- **MUST** avoid `any` types except for specific use cases
- **MUST** implement proper type guards for runtime validation

#### Linting and Formatting Standards
- **MUST** use ESLint with React-specific rules
- **MUST** use Prettier for consistent formatting
- **MUST** implement pre-commit hooks for quality checks
- **MUST** enforce consistent import ordering

### 11. Security Standards

#### XSS Prevention Requirements
- **MUST** sanitize all user input before rendering
- **MUST** use proper Content Security Policy headers
- **MUST** validate all data from external sources
- **NEVER** use dangerouslySetInnerHTML without sanitization

#### Authentication Standards
- **MUST** implement proper token storage (httpOnly cookies preferred)
- **MUST** handle token expiration gracefully
- **MUST** implement proper logout functionality
- **MUST** protect sensitive routes with authentication checks

### 12. Docker Integration Standards

#### Container Requirements
- **MUST** use multi-stage builds for production
- **MUST** serve static files with nginx in production
- **MUST** implement proper health checks
- **MUST** use non-root user in containers
- **NEVER** include source code in production images

#### Development Container Standards
- **MUST** support hot module replacement in development
- **MUST** mount source code as volumes for development
- **MUST** use consistent port mapping
- **MUST** implement proper build optimization

### 13. Internationalization Standards

#### i18n Requirements
- **MUST** implement internationalization from the start
- **MUST** externalize all user-facing strings
- **MUST** support right-to-left languages
- **MUST** implement proper date, number, and currency formatting

### 14. Progressive Web App Standards

#### PWA Requirements
- **MUST** implement service workers for offline support
- **MUST** provide app manifest for installation
- **MUST** implement proper caching strategies
- **MUST** support push notifications where appropriate

This comprehensive React Frontend ruleset provides enforceable standards for building scalable, maintainable, and accessible React applications with modern development practices.
