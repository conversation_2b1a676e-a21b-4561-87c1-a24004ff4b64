---
trigger: always_on
---

# Security Development Rules

## Authentication & Authorization Rules

### Password Security Requirements
- **MUST** use bcrypt with minimum 12 salt rounds
- **MUST** implement password complexity requirements
- **MUST** hash passwords before database storage
- **NEVER** store plain text passwords
- **NEVER** log or expose password hashes

### JWT Authentication Standards
- **MUST** implement access and refresh token pattern
- **MUST** use strong, unique secrets (minimum 32 characters)
- **MUST** implement token expiration and rotation
- **MUST** validate tokens on every protected request
- **MUST** implement proper token revocation

### Access Control Rules
- **MUST** implement role-based access control (RBAC)
- **MUST** validate user permissions on every request
- **MUST** use principle of least privilege
- **MUST** implement resource-based authorization
- **NEVER** trust client-side authorization

## Input Validation & Sanitization Rules

### SQL Injection Prevention
- **MUST** use parameterized queries only
- **MUST** validate all input data types
- **MUST** use ORM with proper escaping
- **NEVER** concatenate user input into SQL strings
- **NEVER** use dynamic SQL without parameterization

### XSS Prevention Requirements
- **MUST** sanitize all user input before rendering
- **MUST** use Content Security Policy headers
- **MUST** validate all data from external sources
- **MUST** escape output in templates
- **NEVER** use dangerouslySetInnerHTML without sanitization

### Input Validation Standards
- **MUST** validate all request data at API boundaries
- **MUST** implement server-side validation
- **MUST** use schema validation (Zod/Joi)
- **MUST** sanitize file uploads
- **MUST** implement rate limiting

## Data Protection Rules

### Encryption Standards
- **MUST** encrypt sensitive data at rest
- **MUST** use HTTPS for all communications
- **MUST** implement proper key management
- **MUST** use AES-256 for symmetric encryption
- **MUST** use secure random number generation

### Database Security Requirements
- **MUST** use connection pooling with limits
- **MUST** implement database access controls
- **MUST** encrypt database connections
- **MUST** implement audit logging
- **MUST** backup data securely

## Error Handling & Logging Rules

### Error Response Standards
- **MUST** implement custom error classes
- **MUST** provide consistent error response format
- **MUST** log errors with sufficient context
- **NEVER** expose stack traces in production
- **NEVER** leak internal implementation details

### Security Logging Requirements
- **MUST** log all authentication events
- **MUST** log authorization failures
- **MUST** implement audit trails
- **MUST** monitor for suspicious activity
- **NEVER** log sensitive data (passwords, tokens, PII)

## Configuration & Environment Rules

### Environment Security Standards
- **MUST** use environment variables for configuration
- **MUST** validate all configuration on startup
- **MUST** use secure defaults
- **MUST** implement secrets management
- **NEVER** hardcode secrets in source code

### Production Security Requirements
- **MUST** use HTTPS only in production
- **MUST** implement security headers
- **MUST** disable debug modes
- **MUST** implement proper CORS policies
- **MUST** use secure session configuration

## Dependency & Infrastructure Rules

### Dependency Security Standards
- **MUST** regularly audit dependencies
- **MUST** use dependency scanning tools
- **MUST** keep dependencies updated
- **MUST** pin dependency versions
- **MUST** remove unused dependencies

### Container Security Requirements
- **MUST** run containers as non-root users
- **MUST** use minimal base images
- **MUST** scan images for vulnerabilities
- **MUST** implement proper secrets management
- **NEVER** include secrets in Docker images

## Monitoring & Incident Response Rules

### Security Monitoring Standards
- **MUST** implement real-time monitoring
- **MUST** set up security alerts
- **MUST** monitor failed login attempts
- **MUST** track privilege escalations
- **MUST** implement intrusion detection

### Incident Response Requirements
- **MUST** have incident response plan
- **MUST** implement security incident logging
- **MUST** provide security contact information
- **MUST** implement breach notification procedures
- **MUST** conduct security reviews
