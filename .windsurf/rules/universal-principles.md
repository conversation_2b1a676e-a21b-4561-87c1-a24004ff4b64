---
trigger: always_on
---

# Universal Development Principles

## Core Methodology for AI-Assisted Development

### 1. Fix-First, Feature-Later Approach
- **When fixing critical errors**: Create minimal working version first
- **Then incrementally add back features** while maintaining stability
- **Never sacrifice core functionality** for quick fixes
- **Always preserve existing user workflows**
- **Document all existing features** before major changes

### 2. Defensive Programming Standards
- **Always handle edge cases**, null values, and error states
- **Use proper null checks and type guards** throughout
- **Implement graceful fallbacks** for missing data
- **Assume APIs can return unexpected formats**
- **Add robust loading/error states** before main render logic

### 3. Clean Code Fundamentals
- **Use descriptive, deterministic naming** (avoid `temp`, `data1`, `foo`)
- **Implement error handling with context** - no silent failures
- **Add inline comments** for non-obvious logic
- **Ensure functions, classes, and files are self-contained and testable**
- **Keep imports clean and minimal** - remove unused immediately

### 4. Incremental Development Process
- **Build features in small, testable chunks**
- **Each commit should maintain working state**
- **Never break existing features** while adding new ones
- **Test each change before proceeding** to the next
- **Use feature flags** for incomplete functionality

### 5. Component Preservation Strategy
- **Document all existing features** before major changes
- **Restore features incrementally**, testing each addition
- **Maintain all existing props and interfaces**
- **Preserve user experience patterns**
- **Never oversimplify complex components** that need rich functionality

### 6. Quality Gates (Non-Negotiable)
- **Remove lint errors immediately** before concluding work
- **Never commit broken code** to any branch
- **Test all user flows** before considering work complete
- **Validate responsive design** and accessibility
- **Check logs for errors** before closing tasks

### 7. Error Handling Hierarchy
- **Loading States**: Implement proper loading indicators for all async operations
- **Error Boundaries**: Provide meaningful error messages with recovery options
- **Type Safety**: Use proper TypeScript typing, avoid `any` types
- **Graceful Degradation**: Components work even with missing data
- **API Resilience**: Handle multiple response formats defensively

### 8. Testing Philosophy
- **Test early, test often** - validate each change before adding complexity
- **Test systematically** across all user flows and roles
- **Use realistic data scenarios** - no hardcoded test data
- **Screenshot critical pages** for visual validation
- **Run full regression tests** on critical paths

### 9. Performance Considerations
- **Avoid unnecessary re-renders** in React components
- **Use proper key props** in lists
- **Implement lazy loading** when appropriate
- **Monitor bundle size** and remove unused dependencies
- **Cache frequently accessed data** appropriately

### 10. Maintainability Principles
- **Keep functions and components focused and small**
- **Use consistent patterns** across the codebase
- **Document architectural decisions** and reasoning
- **Maintain backwards compatibility** when possible
- **Refactor continuously** - leave code cleaner than you found it

## AI-Specific Guidelines

### Memory Integration
- **Store important context** in AI memory system
- **Track component relationships** and dependencies
- **Remember testing outcomes** and quality metrics
- **Maintain feature requirements** and acceptance criteria

### Systematic Approach
- **Always check current state** before making changes
- **Reference feature numbers** in all commits
- **Update tracking systems** when changing feature state
- **Use structured workflows** for all significant modifications

### Error Recovery Patterns
- **Identify root cause** before applying fixes
- **Create minimal reproduction** of the issue
- **Apply targeted fixes** without breaking other functionality
- **Validate fixes across** all affected components
- **Document lessons learned** for future reference

## Quality Assurance Checklist

### Before Any Commit
- [ ] Code builds successfully
- [ ] No lint errors present
- [ ] All imports are used and necessary
- [ ] Proper error handling implemented
- [ ] Loading states handled appropriately

### Before Feature Completion
- [ ] All user roles tested
- [ ] Integration tests pass
- [ ] Performance benchmarks met
- [ ] Security considerations addressed
- [ ] Documentation updated
- [ ] Logs show no errors

### Before Production Deployment
- [ ] Full regression testing complete
- [ ] Cross-browser compatibility verified
- [ ] Mobile responsiveness validated
- [ ] Accessibility standards met
- [ ] Performance monitoring in place
