---
trigger: always_on
---

# Workflow Methodology for AI-Assisted Development

## Systematic Development Process for AI Coding

### 1. AI-First Development Approach

#### Core Methodology
- **Fix-First, Feature-Later**: When encountering critical errors, create minimal working version first
- **Incremental Restoration**: Add back features systematically while maintaining stability
- **Component Preservation**: Never oversimplify complex components that need rich functionality
- **Systematic Testing**: Validate each change across all user roles before proceeding

#### AI Assistant Integration
- **Memory Utilization**: Store important context in AI memory system for continuity
- **Feature Context**: Track component relationships and dependencies
- **Quality Metrics**: Remember testing outcomes and performance benchmarks
- **Requirements Tracking**: Maintain feature requirements and acceptance criteria

### 2. Component Recovery Methodology

#### When Components Break (React/Frontend)
```bash
# 1. Identify the Issue
- Understand root cause (naming conflicts, import issues, state problems)
- Document all existing features before making changes
- Screenshot current state for reference

# 2. Create Minimal Working Version
- Fix technical errors first (naming conflicts, broken imports)
- Remove problematic exports that break Fast Refresh
- Add robust loading/error states before main render logic
- Ensure basic component renders without errors

# 3. Restore Features Incrementally
- Add back one feature at a time
- Test each addition thoroughly
- Maintain all existing props and interfaces
- Preserve user experience patterns

# 4. Validate Across All Roles
- Test patient, doctor, admin, and agent workflows
- Verify role-based permissions and UI access
- Ensure data visibility and editing rights work correctly
- Screenshot critical pages for visual validation
```

#### Component Architecture Recovery
- **Tabbed Interfaces**: Always maintain tabbed interfaces for complex components
- **Structured Layouts**: Use proper spacing, typography, and visual hierarchy
- **Medical Data Display**: Keep symptoms, history, medications in readable sections
- **Integration Points**: Ensure Documents, Timeline, Messages sections remain accessible
- **State Management**: Preserve editing capabilities and status management

### 3. Git Workflow Integration

#### Feature Development Cycle
```bash
# 1. Plan Feature
./windsurf_rules/create-feature.sh 026 "AI-Powered Medical Insights"
# - Creates feat-026 branch
# - Updates todo.txt to IN_PROGRESS
# - Sets up development environment

# 2. Development Iterations
./windsurf_rules/create-change.sh 1
# - Creates change-1 branch for specific work
# - Make changes, commit frequently
# - Test thoroughly in change branch

# 3. Merge After Testing
./windsurf_rules/merge-change.sh 1
# - Runs quality gates (build, lint, tests)
# - Merges to feature branch
# - Deletes change branch
# - Pushes feature branch

# 4. Complete Feature
./windsurf_rules/complete-feature.sh
# - Runs integration tests
# - Merges to main branch
# - Updates todo.txt to COMPLETE
# - Preserves feature branch for reference
```

#### Quality Gates at Each Stage
- **Change Branch**: Unit tests, lint checks, build verification
- **Feature Branch**: Integration tests, cross-role validation, performance checks
- **Main Branch**: Full regression tests, security validation, deployment readiness

### 4. Testing Methodology

#### Systematic Testing Approach
```bash
# 1. Component-Level Testing
- Test components in isolation first
- Verify all props and state combinations
- Test with realistic data scenarios
- Validate error states and edge cases

# 2. Integration Testing
- Test component interactions systematically
- Verify data flow between components
- Test API integrations and error handling
- Check performance with large datasets

# 3. User Flow Testing
- Test complete user journeys end-to-end
- Validate all user roles (patient, doctor, admin, agent)
- Test responsive design on multiple screen sizes
- Verify accessibility standards compliance

# 4. Regression Testing
- Run full regression tests on critical paths
- Test existing functionality after any changes
- Validate that fixes don't break other features
- Maintain test coverage for core components
```

#### Role-Based Testing Protocol
- **Patient Flow**: Dashboard → Cases → Documents → Appointments → Health Journey
- **Doctor Flow**: Case management → Patient communication → Medical opinions → Editing
- **Admin Flow**: User management → System oversight → Data management → Configuration
- **Agent Flow**: AI assistance → Case processing → Automated workflows → Analytics

### 5. Error Recovery Patterns

#### Systematic Debugging Process
```python
# 1. Error Identification
def identify_error_pattern():
    """
    - Check logs for error messages and stack traces
    - Identify root cause vs symptoms
    - Document error reproduction steps
    - Determine scope of impact
    """
    pass

# 2. Minimal Reproduction
def create_minimal_reproduction():
    """
    - Isolate the specific failing component
    - Remove unnecessary complexity
    - Create simple test case that reproduces issue
    - Verify fix resolves the core problem
    """
    pass

# 3. Targeted Fix Application
def apply_targeted_fix():
    """
    - Fix only the specific issue identified
    - Avoid making unrelated changes
    - Maintain existing functionality
    - Test fix in isolation first
    """
    pass

# 4. Comprehensive Validation
def validate_fix_comprehensively():
    """
    - Test fix across all affected components
    - Verify no regression in other features
    - Test all user roles and workflows
    - Update documentation and tests
    """
    pass
```

#### Common Error Patterns & Solutions
- **React Fast Refresh Issues**: Fix naming conflicts between interfaces and components
- **API Response Handling**: Use defensive parsing for multiple response formats
- **State Management**: Implement proper loading/error/success state patterns
- **Component Integration**: Ensure proper prop interfaces and error boundaries
- **Docker Environment**: Check container logs and environment variables

### 6. Performance Optimization Workflow

#### Performance Monitoring Process
```javascript
// 1. Baseline Measurement
const measurePerformance = (componentName, operation) => {
  const startTime = performance.now();
  
  return {
    end: () => {
      const endTime = performance.now();
      console.log(`${componentName}.${operation}: ${endTime - startTime}ms`);
    }
  };
};

// 2. Optimization Implementation
const optimizeComponent = () => {
  // Use React.memo for expensive components
  // Implement proper key props for lists
  // Add lazy loading for large components
  // Cache expensive computations with useMemo
};

// 3. Performance Validation
const validateOptimization = () => {
  // Compare before/after metrics
  // Test with realistic data volumes
  // Verify no functionality regression
  // Monitor memory usage patterns
};
```

#### Performance Checklist
- [ ] Component render times under 100ms
- [ ] API response times under 500ms
- [ ] Bundle size within acceptable limits
- [ ] Memory usage stable over time
- [ ] No unnecessary re-renders

### 7. Documentation & Knowledge Management

#### AI Memory Integration
```typescript
interface DevelopmentContext {
  featureNumber: string;
  componentName: string;
  userRoles: string[];
  testingOutcomes: TestResult[];
  performanceMetrics: PerformanceData[];
  knownIssues: Issue[];
  integrationPoints: string[];
}

// Store context for AI continuity
const storeDevContext = (context: DevelopmentContext) => {
  // Store in AI memory system
  // Include component relationships
  // Track testing outcomes
  // Remember performance benchmarks
};
```

#### Documentation Standards
- **Component Documentation**: Purpose, props, usage patterns, integration points
- **API Documentation**: Endpoints, request/response formats, error codes
- **Workflow Documentation**: Step-by-step processes, decision trees, troubleshooting
- **Architecture Documentation**: System overview, data flow, security considerations

### 8. Security & Compliance Integration

#### Healthcare-Specific Considerations
```python
# HIPAA Compliance Checklist
def ensure_hipaa_compliance():
    """
    - Encrypt all medical data in transit and at rest
    - Implement proper access controls and audit trails
    - Validate user permissions for data access
    - Log all medical data interactions
    - Ensure secure session management
    """
    pass

# Data Privacy Validation
def validate_data_privacy():
    """
    - Check for data leakage in logs
    - Verify proper data anonymization
    - Test role-based data visibility
    - Validate secure data transmission
    """
    pass
```

#### Security Testing Integration
- **Authentication Testing**: Verify proper session management and role validation
- **Authorization Testing**: Test role-based access controls and permissions
- **Data Security**: Validate encryption, secure transmission, and storage
- **Input Validation**: Test for injection attacks and data sanitization

### 9. Deployment & Production Readiness

#### Pre-Deployment Checklist
```bash
# 1. Code Quality Gates
docker compose exec app npm run lint
docker compose exec app npm run build
docker compose exec app npm test

# 2. Security Validation
# - Check for hardcoded secrets
# - Validate environment variable usage
# - Test authentication and authorization
# - Verify HTTPS configuration

# 3. Performance Validation
# - Load testing with realistic data
# - Memory usage monitoring
# - Database query optimization
# - CDN and caching configuration

# 4. Monitoring Setup
# - Application performance monitoring
# - Error tracking and alerting
# - Health check endpoints
# - Log aggregation and analysis
```

#### Production Monitoring
- **Application Health**: Response times, error rates, availability
- **Infrastructure Health**: CPU, memory, disk usage, network
- **Business Metrics**: User engagement, feature usage, conversion rates
- **Security Metrics**: Failed login attempts, suspicious activity, data access patterns

### 10. Continuous Improvement Process

#### Retrospective Analysis
```python
def conduct_retrospective(feature_number: str):
    """
    Analyze completed feature for lessons learned.
    
    - What went well in the development process?
    - What challenges were encountered?
    - How can the workflow be improved?
    - What patterns should be documented for reuse?
    """
    
    lessons_learned = {
        "successful_patterns": [],
        "challenges_faced": [],
        "process_improvements": [],
        "reusable_solutions": []
    }
    
    # Store in AI memory for future reference
    return lessons_learned

def update_workflow_based_on_learnings(lessons: dict):
    """
    Update workflow methodology based on retrospective analysis.
    
    - Update rule files with new patterns
    - Improve automation scripts
    - Enhance testing procedures
    - Refine quality gates
    """
    pass
```

#### Workflow Evolution
- **Pattern Recognition**: Identify recurring successful patterns
- **Process Optimization**: Streamline repetitive tasks with automation
- **Quality Enhancement**: Improve testing and validation procedures
- **Knowledge Sharing**: Document solutions for common problems

This comprehensive workflow methodology ensures systematic, high-quality development with proper AI integration, testing, and continuous improvement.
