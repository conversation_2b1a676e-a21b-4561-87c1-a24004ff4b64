#!/bin/bash
# complete-feature.sh - Complete feature development and merge to main

# Set project paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
CHARTER_FILE="${PROJECT_ROOT}/.project/charter.md"
CHANGELOG_FILE="${PROJECT_ROOT}/.project/changelog.md"

# Helper function to get default branch
get_default_branch() {
    # Try to get default branch from remote
    if git remote show origin &>/dev/null; then
        DEFAULT_BRANCH=$(git remote show origin | grep 'HEAD branch' | cut -d' ' -f5)
        if [ -n "$DEFAULT_BRANCH" ]; then
            echo "$DEFAULT_BRANCH"
            return
        fi
    fi
    
    # Fallback: check what branches exist locally
    if git show-ref --verify --quiet refs/heads/main; then
        echo "main"
    elif git show-ref --verify --quiet refs/heads/master; then
        echo "master"
    else
        # Default to main for new repositories
        echo "main"
    fi
}

# Helper function to ensure we're on the default branch
ensure_default_branch() {
    DEFAULT_BRANCH=$(get_default_branch)
    
    # Create default branch if it doesn't exist
    if ! git show-ref --verify --quiet refs/heads/$DEFAULT_BRANCH; then
        git checkout -b $DEFAULT_BRANCH
        if git remote show origin &>/dev/null; then
            git push -u origin $DEFAULT_BRANCH
        fi
    else
        git checkout $DEFAULT_BRANCH
        if git remote show origin &>/dev/null; then
            git pull origin $DEFAULT_BRANCH
        fi
    fi
}

# Get current feature branch
CURRENT_BRANCH=$(git branch --show-current)
if [[ ! $CURRENT_BRANCH =~ ^feat-[0-9]{3}$ ]]; then
    echo "Error: Must be on a feature branch (feat-XXX)"
    echo "Current branch: $CURRENT_BRANCH"
    echo "Switch to a feature branch first: git checkout feat-XXX"
    exit 1
fi

# Extract feature number
FEAT_NUM=$(echo $CURRENT_BRANCH | sed 's/feat-//')

echo "Completing feature ${CURRENT_BRANCH}"
echo ""

# Run integration tests
echo "🧪 Running integration tests..."

if [ -f "docker-compose.yml" ]; then
    echo "📦 Running full test suite in Docker..."
    
    # Build
    if ! docker compose exec app npm run build; then
        echo "❌ Build failed. Fix errors before completing feature."
        exit 1
    fi
    
    # Lint
    if ! docker compose exec app npm run lint; then
        echo "❌ Lint errors found. Fix before completing feature."
        exit 1
    fi
    
    # Tests
    if docker compose exec app npm test --passWithNoTests; then
        echo "✅ All tests passed"
    else
        echo "⚠️  Some tests failed"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Check Docker logs
    echo "📋 Checking Docker logs for errors..."
    if docker compose logs app | grep -i error | tail -5; then
        echo "⚠️  Found recent errors in logs"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
else
    echo "⚠️  Not in Docker environment, skipping automated tests"
fi

# Update project charter to TESTING status
echo "📝 Updating feature status to TESTING..."
sed -i "s/FEAT-${FEAT_NUM}: \[IN_PROGRESS\]/FEAT-${FEAT_NUM}: [TESTING]/" "${CHARTER_FILE}"
git add "${CHARTER_FILE}"
git commit -m "feat(FEAT-${FEAT_NUM}): ready for integration testing"

# Merge to default branch
DEFAULT_BRANCH=$(get_default_branch)
echo "🔄 Switching to ${DEFAULT_BRANCH} branch..."
ensure_default_branch

echo "🔀 Merging ${CURRENT_BRANCH} to ${DEFAULT_BRANCH}..."
FEATURE_DESC=$(grep "FEAT-${FEAT_NUM}:" "${CHARTER_FILE}" | head -1 | sed 's/## FEAT-[0-9]*: \[.*\] //')
git merge ${CURRENT_BRANCH} --no-ff -m "feat: complete FEAT-${FEAT_NUM} - ${FEATURE_DESC}"

# Update project charter to COMPLETE
echo "✅ Updating feature status to COMPLETE..."
sed -i "s/FEAT-${FEAT_NUM}: \[TESTING\]/FEAT-${FEAT_NUM}: [COMPLETE]/" "${CHARTER_FILE}"

# Add completion date to project charter
COMPLETION_DATE=$(date +%Y-%m-%d)
sed -i "/## FEAT-${FEAT_NUM}:/a **Completed Date**: ${COMPLETION_DATE}" "${CHARTER_FILE}"

# Add to changelog
echo "📋 Adding to changelog..."
VERSION=$(date +"v1.%m.%d")
echo "" >> "${CHANGELOG_FILE}"
echo "## [${VERSION}] - ${COMPLETION_DATE} - ${FEATURE_DESC}" >> "${CHANGELOG_FILE}"
echo "" >> "${CHANGELOG_FILE}"
echo "### Added" >> "${CHANGELOG_FILE}"
echo "- **FEAT-${FEAT_NUM}**: ${FEATURE_DESC}" >> "${CHANGELOG_FILE}"
echo "  - Feature completed and deployed to main branch" >> "${CHANGELOG_FILE}"
echo "  - All acceptance criteria met and tested" >> "${CHANGELOG_FILE}"
echo "" >> "${CHANGELOG_FILE}"

git add "${CHARTER_FILE}" "${CHANGELOG_FILE}"
git commit -m "feat(FEAT-${FEAT_NUM}): feature complete and deployed"

# Push everything
if git remote show origin &>/dev/null; then
    git push origin $DEFAULT_BRANCH
    git push origin ${CURRENT_BRANCH}
fi

echo ""
echo "🎉 Feature FEAT-${FEAT_NUM} completed successfully!"
echo "📝 Status: COMPLETE"
echo "🌟 Merged to ${DEFAULT_BRANCH} branch"
echo "🔄 Feature branch ${CURRENT_BRANCH} preserved for reference"
echo ""
echo "Next steps:"
echo "1. Create new feature: ./.project/scripts/create-feature.sh XXX 'Description'"
echo "2. Or continue with existing feature: git checkout feat-XXX"
