#!/bin/bash
# create-change.sh - Create change branch for specific development work

CHANGE_NUM=$1

if [ -z "$CHANGE_NUM" ]; then
    echo "Usage: ./create-change.sh 1"
    echo "Example: ./create-change.sh 1"
    exit 1
fi

# Get current feature branch
CURRENT_BRANCH=$(git branch --show-current)
if [[ ! $CURRENT_BRANCH =~ ^feat-[0-9]{3}$ ]]; then
    echo "Error: Must be on a feature branch (feat-XXX)"
    echo "Current branch: $CURRENT_BRANCH"
    echo "Switch to a feature branch first: git checkout feat-XXX"
    exit 1
fi

# Check if change branch already exists
if git show-ref --verify --quiet refs/heads/change-${CHANGE_NUM}; then
    echo "Error: change-${CHANGE_NUM} already exists"
    echo "Use a different number or delete existing branch"
    exit 1
fi

echo "Creating change branch change-${CHANGE_NUM} from ${CURRENT_BRANCH}"

# Create change branch
git checkout -b change-${CHANGE_NUM}
git push -u origin change-${CHANGE_NUM}

echo "✅ Change branch change-${CHANGE_NUM} created from ${CURRENT_BRANCH}"
echo "📝 Branch: change-${CHANGE_NUM}"
echo "🔧 Ready for development"
echo ""
echo "Next steps:"
echo "1. Make your changes and commit frequently"
echo "2. Test your changes thoroughly"
echo "3. Merge back: ./merge-change.sh ${CHANGE_NUM}"
