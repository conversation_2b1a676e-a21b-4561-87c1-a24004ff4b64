#!/bin/bash
# create-feature.sh - Create new feature branch and update todo.txt

FEAT_NUM=$1
DESCRIPTION="$2"

if [ -z "$FEAT_NUM" ] || [ -z "$DESCRIPTION" ]; then
    echo "Usage: ./create-feature.sh 026 'Feature Description'"
    echo "Example: ./create-feature.sh 026 'AI-Powered Medical Insights'"
    exit 1
fi

# Ensure 3-digit format
FEAT_NUM=$(printf "%03d" $FEAT_NUM)

# Set project paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
CHARTER_FILE="${PROJECT_ROOT}/.project/charter.md"

# Helper function to get default branch
get_default_branch() {
    # Try to get default branch from remote
    if git remote show origin &>/dev/null; then
        DEFAULT_BRANCH=$(git remote show origin | grep 'HEAD branch' | cut -d' ' -f5)
        if [ -n "$DEFAULT_BRANCH" ]; then
            echo "$DEFAULT_BRANCH"
            return
        fi
    fi
    
    # Fallback: check what branches exist locally
    if git show-ref --verify --quiet refs/heads/main; then
        echo "main"
    elif git show-ref --verify --quiet refs/heads/master; then
        echo "master"
    else
        # Default to main for new repositories
        echo "main"
    fi
}

# Helper function to ensure we're on the default branch
ensure_default_branch() {
    DEFAULT_BRANCH=$(get_default_branch)
    
    # Create default branch if it doesn't exist
    if ! git show-ref --verify --quiet refs/heads/$DEFAULT_BRANCH; then
        git checkout -b $DEFAULT_BRANCH
        if git remote show origin &>/dev/null; then
            git push -u origin $DEFAULT_BRANCH
        fi
    else
        git checkout $DEFAULT_BRANCH
        if git remote show origin &>/dev/null; then
            git pull origin $DEFAULT_BRANCH
        fi
    fi
}

# Check if feature already exists
if grep -q "FEAT-${FEAT_NUM}:" "${CHARTER_FILE}"; then
    echo "Error: FEAT-${FEAT_NUM} already exists in .project/charter.md"
    exit 1
fi

echo "Creating feature FEAT-${FEAT_NUM}: ${DESCRIPTION}"

# Add to project charter as new planned feature
echo "" >> "${CHARTER_FILE}"
echo "## FEAT-${FEAT_NUM}: [PLANNED] ${DESCRIPTION}" >> "${CHARTER_FILE}"
echo "**Priority**: P2" >> "${CHARTER_FILE}"
echo "**Requestor**: Development Team" >> "${CHARTER_FILE}"
echo "**Request Date**: $(date +%Y-%m-%d)" >> "${CHARTER_FILE}"
echo "**Estimated Effort**: TBD" >> "${CHARTER_FILE}"
echo "" >> "${CHARTER_FILE}"
echo "**User Story**: As a user, I want ${DESCRIPTION} so that I can achieve my goals." >> "${CHARTER_FILE}"
echo "" >> "${CHARTER_FILE}"
echo "**Acceptance Criteria**:" >> "${CHARTER_FILE}"
echo "- [ ] TBD - Define specific acceptance criteria" >> "${CHARTER_FILE}"
echo "" >> "${CHARTER_FILE}"
echo "**Dependencies**: None" >> "${CHARTER_FILE}"
echo "**Blockers**: None" >> "${CHARTER_FILE}"
echo "**Notes**: Feature created via automation script" >> "${CHARTER_FILE}"

# Create branch from default branch
ensure_default_branch
git checkout -b feat-${FEAT_NUM}

# Update status to IN_PROGRESS in project charter
sed -i "s/FEAT-${FEAT_NUM}: \[PLANNED\]/FEAT-${FEAT_NUM}: [IN_PROGRESS]/" "${CHARTER_FILE}"
git add "${CHARTER_FILE}"
git commit -m "feat: start FEAT-${FEAT_NUM} - ${DESCRIPTION}"
if git remote show origin &>/dev/null; then
    git push -u origin feat-${FEAT_NUM}
fi

echo "✅ Feature FEAT-${FEAT_NUM} created and ready for development"
echo "📝 Branch: feat-${FEAT_NUM}"
echo "📋 Status: IN_PROGRESS"
echo ""
echo "Next steps:"
echo "1. Create change branch: ./.project/scripts/create-change.sh 1"
echo "2. Make your changes in the change branch"
echo "3. Test and merge: ./.project/scripts/merge-change.sh 1"
