#!/bin/bash
# manage-project.sh - Comprehensive project management workflow

# Set project paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
CHARTER_FILE="${PROJECT_ROOT}/.project/charter.md"
CHANGELOG_FILE="${PROJECT_ROOT}/.project/changelog.md"

COMMAND=$1
FEAT_NUM=$2
DESCRIPTION="$3"
PRIORITY=${4:-"P2"}
REQUESTOR=${5:-"Development Team"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to display usage
show_usage() {
    echo "Usage: ./manage-project.sh <command> [options]"
    echo ""
    echo "Commands:"
    echo "  request <feat_num> \"<description>\" [priority] [requestor]"
    echo "    - Add new feature request to project charter"
    echo "    - Priority: P0 (Critical), P1 (High), P2 (Medium), P3 (Low)"
    echo "    - Example: ./manage-project.sh request 026 \"AI Medical Insights\" P1 \"Medical Team\""
    echo ""
    echo "  status <feat_num> <new_status>"
    echo "    - Update feature status"
    echo "    - Status: REQUESTED, PLANNED, IN_PROGRESS, TESTING, INTEGRATION, COMPLETE, BLOCKED, CANCELLED"
    echo "    - Example: ./manage-project.sh status 026 TESTING"
    echo ""
    echo "  list [status]"
    echo "    - List features by status (optional filter)"
    echo "    - Example: ./manage-project.sh list IN_PROGRESS"
    echo ""
    echo "  metrics"
    echo "    - Show project metrics and statistics"
    echo ""
    echo "  next"
    echo "    - Show next features ready for development"
    echo ""
    echo "  block <feat_num> \"<reason>\""
    echo "    - Mark feature as blocked with reason"
    echo ""
    echo "  unblock <feat_num>"
    echo "    - Remove blocked status from feature"
}

# Function to add new feature request
add_request() {
    local feat_num=$1
    local description="$2"
    local priority=$3
    local requestor="$4"
    
    # Ensure 3-digit format
    feat_num=$(printf "%03d" $feat_num)
    
    # Check if feature already exists
    if grep -q "FEAT-${feat_num}:" "${CHARTER_FILE}"; then
        echo -e "${RED}Error: FEAT-${feat_num} already exists in project charter${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Adding new feature request: FEAT-${feat_num}${NC}"
    
    # Find the right section to add the feature
    if grep -q "# PLANNED FEATURES" "${CHARTER_FILE}"; then
        # Add to planned features section
        sed -i "/# PLANNED FEATURES/a\\
\\
## FEAT-${feat_num}: [REQUESTED] ${description}\\
**Priority**: ${priority}\\
**Requestor**: ${requestor}\\
**Request Date**: $(date +%Y-%m-%d)\\
**Estimated Effort**: TBD\\
\\
**User Story**: As a user, I want ${description} so that I can achieve my goals.\\
\\
**Acceptance Criteria**:\\
- [ ] TBD - Define specific acceptance criteria\\
\\
**Dependencies**: TBD\\
**Blockers**: None\\
**Notes**: Feature request added via project management system" "${CHARTER_FILE}"
    else
        # Add at end of file
        echo "" >> "${CHARTER_FILE}"
        echo "## FEAT-${feat_num}: [REQUESTED] ${description}" >> "${CHARTER_FILE}"
        echo "**Priority**: ${priority}" >> "${CHARTER_FILE}"
        echo "**Requestor**: ${requestor}" >> "${CHARTER_FILE}"
        echo "**Request Date**: $(date +%Y-%m-%d)" >> "${CHARTER_FILE}"
        echo "**Estimated Effort**: TBD" >> "${CHARTER_FILE}"
        echo "" >> "${CHARTER_FILE}"
        echo "**User Story**: As a user, I want ${description} so that I can achieve my goals." >> "${CHARTER_FILE}"
        echo "" >> "${CHARTER_FILE}"
        echo "**Acceptance Criteria**:" >> "${CHARTER_FILE}"
        echo "- [ ] TBD - Define specific acceptance criteria" >> "${CHARTER_FILE}"
        echo "" >> "${CHARTER_FILE}"
        echo "**Dependencies**: TBD" >> "${CHARTER_FILE}"
        echo "**Blockers**: None" >> "${CHARTER_FILE}"
        echo "**Notes**: Feature request added via project management system" >> "${CHARTER_FILE}"
    fi
    
    # Commit the change
    git add "${CHARTER_FILE}"
    git commit -m "feat: add FEAT-${feat_num} request - ${description}"
    
    echo -e "${GREEN}✅ Feature FEAT-${feat_num} added to project charter${NC}"
    echo -e "${YELLOW}📋 Status: REQUESTED${NC}"
    echo -e "${YELLOW}🎯 Priority: ${priority}${NC}"
    echo -e "${YELLOW}👤 Requestor: ${requestor}${NC}"
}

# Function to update feature status
update_status() {
    local feat_num=$1
    local new_status=$2
    
    # Ensure 3-digit format
    feat_num=$(printf "%03d" $feat_num)
    
    # Check if feature exists
    if ! grep -q "FEAT-${feat_num}:" "${CHARTER_FILE}"; then
        echo -e "${RED}Error: FEAT-${feat_num} not found in project charter${NC}"
        return 1
    fi
    
    # Get current status
    local current_status=$(grep "FEAT-${feat_num}:" "${CHARTER_FILE}" | sed 's/.*\[\(.*\)\].*/\1/')
    
    echo -e "${BLUE}Updating FEAT-${feat_num} status: ${current_status} → ${new_status}${NC}"
    
    # Update status in project charter
    sed -i "s/FEAT-${feat_num}: \[${current_status}\]/FEAT-${feat_num}: [${new_status}]/" "${CHARTER_FILE}"
    
    # Add timestamp for certain status changes
    case $new_status in
        "IN_PROGRESS")
            sed -i "/## FEAT-${feat_num}:/a **Started Date**: $(date +%Y-%m-%d)" "${CHARTER_FILE}"
            ;;
        "COMPLETE")
            sed -i "/## FEAT-${feat_num}:/a **Completed Date**: $(date +%Y-%m-%d)" "${CHARTER_FILE}"
            ;;
        "BLOCKED")
            sed -i "/## FEAT-${feat_num}:/a **Blocked Date**: $(date +%Y-%m-%d)" "${CHARTER_FILE}"
            ;;
    esac
    
    # Commit the change
    git add "${CHARTER_FILE}"
    git commit -m "feat(FEAT-${feat_num}): update status to ${new_status}"
    
    echo -e "${GREEN}✅ Status updated successfully${NC}"
}

# Function to list features by status
list_features() {
    local filter_status=$1
    
    echo -e "${BLUE}📋 Project Features${NC}"
    echo "===================="
    
    if [ -n "$filter_status" ]; then
        echo -e "${YELLOW}Filtering by status: ${filter_status}${NC}"
        echo ""
        grep -A 3 "\[${filter_status}\]" project-charter.md | grep -E "(FEAT-|Priority:|Requestor:)" || echo "No features found with status: ${filter_status}"
    else
        # Show summary by status
        echo -e "${GREEN}REQUESTED:${NC} $(grep -c "\[REQUESTED\]" project-charter.md)"
        echo -e "${YELLOW}PLANNED:${NC} $(grep -c "\[PLANNED\]" project-charter.md)"
        echo -e "${BLUE}IN_PROGRESS:${NC} $(grep -c "\[IN_PROGRESS\]" project-charter.md)"
        echo -e "${YELLOW}TESTING:${NC} $(grep -c "\[TESTING\]" project-charter.md)"
        echo -e "${BLUE}INTEGRATION:${NC} $(grep -c "\[INTEGRATION\]" project-charter.md)"
        echo -e "${GREEN}COMPLETE:${NC} $(grep -c "\[COMPLETE\]" project-charter.md)"
        echo -e "${RED}BLOCKED:${NC} $(grep -c "\[BLOCKED\]" project-charter.md)"
        echo -e "${RED}CANCELLED:${NC} $(grep -c "\[CANCELLED\]" project-charter.md)"
        echo ""
        echo -e "${BLUE}Recent Features:${NC}"
        grep -E "FEAT-[0-9]+:" project-charter.md | tail -10
    fi
}

# Function to show project metrics
show_metrics() {
    echo -e "${BLUE}📊 Project Metrics${NC}"
    echo "=================="
    
    local total_features=$(grep -c "FEAT-[0-9]*:" project-charter.md)
    local completed_features=$(grep -c "\[COMPLETE\]" project-charter.md)
    local in_progress_features=$(grep -c "\[IN_PROGRESS\]" project-charter.md)
    local blocked_features=$(grep -c "\[BLOCKED\]" project-charter.md)
    
    echo "Total Features: $total_features"
    echo "Completed: $completed_features"
    echo "In Progress: $in_progress_features"
    echo "Blocked: $blocked_features"
    
    if [ $total_features -gt 0 ]; then
        local completion_rate=$((completed_features * 100 / total_features))
        echo "Completion Rate: ${completion_rate}%"
    fi
    
    echo ""
    echo -e "${BLUE}Priority Breakdown:${NC}"
    echo "P0 (Critical): $(grep -c "Priority.*P0" project-charter.md)"
    echo "P1 (High): $(grep -c "Priority.*P1" project-charter.md)"
    echo "P2 (Medium): $(grep -c "Priority.*P2" project-charter.md)"
    echo "P3 (Low): $(grep -c "Priority.*P3" project-charter.md)"
    
    echo ""
    echo -e "${BLUE}Recent Activity:${NC}"
    git log --oneline --grep="feat" -10 | head -5
}

# Function to show next features ready for development
show_next() {
    echo -e "${BLUE}🚀 Next Features Ready for Development${NC}"
    echo "======================================"
    
    echo -e "${GREEN}PLANNED Features (Ready to Start):${NC}"
    grep -A 5 "\[PLANNED\]" project-charter.md | grep -E "(FEAT-|Priority:|Estimated)" | head -15
    
    echo ""
    echo -e "${YELLOW}REQUESTED Features (Need Analysis):${NC}"
    grep -A 5 "\[REQUESTED\]" project-charter.md | grep -E "(FEAT-|Priority:|Requestor:)" | head -9
}

# Function to block a feature
block_feature() {
    local feat_num=$1
    local reason="$2"
    
    # Ensure 3-digit format
    feat_num=$(printf "%03d" $feat_num)
    
    echo -e "${RED}🚫 Blocking FEAT-${feat_num}: ${reason}${NC}"
    
    # Update status to BLOCKED
    update_status $feat_num "BLOCKED"
    
    # Add blocker reason
    sed -i "/## FEAT-${feat_num}:/,/^## / s/\*\*Blockers\*\*:.*/\*\*Blockers\*\*: ${reason}/" project-charter.md
    
    git add project-charter.md
    git commit -m "feat(FEAT-${feat_num}): blocked - ${reason}"
    
    echo -e "${RED}⚠️  Feature blocked successfully${NC}"
}

# Function to unblock a feature
unblock_feature() {
    local feat_num=$1
    
    # Ensure 3-digit format
    feat_num=$(printf "%03d" $feat_num)
    
    echo -e "${GREEN}✅ Unblocking FEAT-${feat_num}${NC}"
    
    # Update status to PLANNED (ready for development)
    update_status $feat_num "PLANNED"
    
    # Clear blocker reason
    sed -i "/## FEAT-${feat_num}:/,/^## / s/\*\*Blockers\*\*:.*/\*\*Blockers\*\*: None/" project-charter.md
    
    git add project-charter.md
    git commit -m "feat(FEAT-${feat_num}): unblocked and ready for development"
    
    echo -e "${GREEN}🚀 Feature unblocked and ready for development${NC}"
}

# Main command processing
case $COMMAND in
    "request")
        if [ -z "$FEAT_NUM" ] || [ -z "$DESCRIPTION" ]; then
            echo -e "${RED}Error: Missing required parameters${NC}"
            echo "Usage: ./manage-project.sh request <feat_num> \"<description>\" [priority] [requestor]"
            exit 1
        fi
        add_request $FEAT_NUM "$DESCRIPTION" $PRIORITY "$REQUESTOR"
        ;;
    "status")
        if [ -z "$FEAT_NUM" ] || [ -z "$DESCRIPTION" ]; then
            echo -e "${RED}Error: Missing required parameters${NC}"
            echo "Usage: ./manage-project.sh status <feat_num> <new_status>"
            exit 1
        fi
        update_status $FEAT_NUM "$DESCRIPTION"
        ;;
    "list")
        list_features "$FEAT_NUM"
        ;;
    "metrics")
        show_metrics
        ;;
    "next")
        show_next
        ;;
    "block")
        if [ -z "$FEAT_NUM" ] || [ -z "$DESCRIPTION" ]; then
            echo -e "${RED}Error: Missing required parameters${NC}"
            echo "Usage: ./manage-project.sh block <feat_num> \"<reason>\""
            exit 1
        fi
        block_feature $FEAT_NUM "$DESCRIPTION"
        ;;
    "unblock")
        if [ -z "$FEAT_NUM" ]; then
            echo -e "${RED}Error: Missing feature number${NC}"
            echo "Usage: ./manage-project.sh unblock <feat_num>"
            exit 1
        fi
        unblock_feature $FEAT_NUM
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
