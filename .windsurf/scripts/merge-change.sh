#!/bin/bash
# merge-change.sh - Merge change branch back to feature after testing

CHANGE_NUM=$1

if [ -z "$CHANGE_NUM" ]; then
    echo "Usage: ./merge-change.sh 1"
    echo "Example: ./merge-change.sh 1"
    exit 1
fi

# Get current branch and validate
CURRENT_BRANCH=$(git branch --show-current)
if [[ $CURRENT_BRANCH != "change-${CHANGE_NUM}" ]]; then
    echo "Error: Must be on change-${CHANGE_NUM} branch"
    echo "Current branch: $CURRENT_BRANCH"
    echo "Switch to change branch first: git checkout change-${CHANGE_NUM}"
    exit 1
fi

# Find parent feature branch
FEATURE_BRANCH=$(git show-branch | grep '\*' | grep -v "$CURRENT_BRANCH" | head -n1 | sed 's/.*\[\(.*\)\].*/\1/' | sed 's/[\^~].*//')
if [[ ! $FEATURE_BRANCH =~ ^feat-[0-9]{3}$ ]]; then
    echo "Error: Cannot determine parent feature branch"
    echo "Please manually specify: git checkout feat-XXX && git merge change-${CHANGE_NUM}"
    exit 1
fi

echo "Merging change-${CHANGE_NUM} into ${FEATURE_BRANCH}"
echo ""

# Run quality gates
echo "🧪 Running quality gates..."

# Check if we're in Docker environment
if [ -f "docker-compose.yml" ]; then
    echo "📦 Running tests in Docker..."
    
    # Build and test
    if ! docker compose exec app npm run build; then
        echo "❌ Build failed. Fix errors before merging."
        exit 1
    fi
    
    if ! docker compose exec app npm run lint; then
        echo "❌ Lint errors found. Fix before merging."
        exit 1
    fi
    
    # Run tests if available
    if docker compose exec app npm test --passWithNoTests; then
        echo "✅ Tests passed"
    else
        echo "⚠️  Tests failed or not available"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
else
    echo "⚠️  Not in Docker environment, skipping automated tests"
fi

# Switch to feature branch and merge
echo "🔄 Switching to ${FEATURE_BRANCH}..."
git checkout ${FEATURE_BRANCH}

echo "🔀 Merging change-${CHANGE_NUM}..."
git merge change-${CHANGE_NUM} --no-ff -m "merge: change-${CHANGE_NUM} into ${FEATURE_BRANCH}"

# Push feature branch
git push origin ${FEATURE_BRANCH}

# Delete change branch
echo "🗑️  Cleaning up change-${CHANGE_NUM} branch..."
git branch -d change-${CHANGE_NUM}
git push origin --delete change-${CHANGE_NUM}

echo ""
echo "✅ Successfully merged change-${CHANGE_NUM} into ${FEATURE_BRANCH}"
echo "🧹 Change branch deleted"
echo "📝 Feature branch updated and pushed"
echo ""
echo "Next steps:"
echo "1. Continue development: ./create-change.sh N"
echo "2. Or complete feature: ./complete-feature.sh"
