#!/bin/bash

# Trivy Security Scanning Script for Continuia Project
# This script scans all Docker images for vulnerabilities using Trivy

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TRIVY_CACHE_DIR="./.trivy-cache"
SCAN_RESULTS_DIR="./.project/data/security-scans"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create directories
mkdir -p "$TRIVY_CACHE_DIR"
mkdir -p "$SCAN_RESULTS_DIR"

echo -e "${BLUE}🔒 Starting Trivy Security Scan for Continuia Project${NC}"
echo -e "${BLUE}Timestamp: $(date)${NC}"
echo ""

# Function to install Trivy if not present
install_trivy() {
    if ! command -v trivy &> /dev/null; then
        echo -e "${YELLOW}📦 Installing Trivy...${NC}"
        if command -v apt-get &> /dev/null; then
            # Ubuntu/Debian
            sudo apt-get update
            sudo apt-get install -y wget apt-transport-https gnupg lsb-release
            wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add -
            echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | sudo tee -a /etc/apt/sources.list.d/trivy.list
            sudo apt-get update
            sudo apt-get install -y trivy
        elif command -v yum &> /dev/null; then
            # CentOS/RHEL
            sudo yum install -y wget
            wget -O /tmp/trivy.rpm https://github.com/aquasecurity/trivy/releases/latest/download/trivy_Linux-64bit.rpm
            sudo rpm -ivh /tmp/trivy.rpm
        else
            echo -e "${RED}❌ Unable to install Trivy automatically. Please install manually.${NC}"
            exit 1
        fi
        echo -e "${GREEN}✅ Trivy installed successfully${NC}"
    else
        echo -e "${GREEN}✅ Trivy is already installed${NC}"
    fi
}

# Function to scan a Docker image
scan_image() {
    local image_name="$1"
    local service_name="$2"
    local output_file="$SCAN_RESULTS_DIR/${service_name}_${TIMESTAMP}.json"
    local html_file="$SCAN_RESULTS_DIR/${service_name}_${TIMESTAMP}.html"
    
    echo -e "${BLUE}🔍 Scanning $service_name ($image_name)...${NC}"
    
    # JSON output for CI/CD processing
    trivy image \
        --cache-dir "$TRIVY_CACHE_DIR" \
        --format json \
        --output "$output_file" \
        --severity HIGH,CRITICAL \
        "$image_name"
    
    # HTML output for human review
    trivy image \
        --cache-dir "$TRIVY_CACHE_DIR" \
        --format template \
        --template '@contrib/html.tpl' \
        --output "$html_file" \
        --severity HIGH,CRITICAL \
        "$image_name"
    
    # Console output
    echo -e "${YELLOW}📊 Vulnerability Summary for $service_name:${NC}"
    trivy image \
        --cache-dir "$TRIVY_CACHE_DIR" \
        --format table \
        --severity HIGH,CRITICAL \
        "$image_name" | head -20
    
    echo ""
    
    # Check for critical vulnerabilities with proper error handling
    local critical_count=0
    local high_count=0
    
    if [ -f "$output_file" ] && [ -s "$output_file" ]; then
        # Count CRITICAL vulnerabilities with better error handling
        critical_count=$(jq -r '[.Results[]?.Vulnerabilities[]? | select(.Severity == "CRITICAL")] | length' "$output_file" 2>/dev/null || echo "0")
        high_count=$(jq -r '[.Results[]?.Vulnerabilities[]? | select(.Severity == "HIGH")] | length' "$output_file" 2>/dev/null || echo "0")
        
        # Ensure we have valid numbers
        critical_count=${critical_count:-0}
        high_count=${high_count:-0}
        
        echo -e "${BLUE}📊 Vulnerability counts: CRITICAL=$critical_count, HIGH=$high_count${NC}"
    else
        echo -e "${YELLOW}⚠️  No scan results file found or file is empty${NC}"
    fi
    
    if [ "$critical_count" -gt 0 ]; then
        echo -e "${RED}🚨 CRITICAL: $service_name has $critical_count critical vulnerabilities${NC}"
        echo -e "${RED}   Review detailed report: $output_file${NC}"
        return 1
    elif [ "$high_count" -gt 10 ]; then
        echo -e "${YELLOW}⚠️  WARNING: $service_name has $high_count high-severity vulnerabilities${NC}"
        return 2
    else
        echo -e "${GREEN}✅ $service_name security scan completed (CRITICAL=$critical_count, HIGH=$high_count)${NC}"
        return 0
    fi
}

# Function to scan filesystem for secrets
scan_secrets() {
    echo -e "${BLUE}🔍 Scanning filesystem for secrets...${NC}"
    local secrets_file="$SCAN_RESULTS_DIR/secrets_${TIMESTAMP}.json"
    
    trivy fs \
        --cache-dir "$TRIVY_CACHE_DIR" \
        --format json \
        --output "$secrets_file" \
        --scanners secret \
        --skip-dirs .git,.trivy-cache,node_modules,.data \
        .
    
    local secrets_count=$(jq '.Results[]?.Secrets[]? | .RuleID' "$secrets_file" 2>/dev/null | wc -l)
    
    if [ "$secrets_count" -gt 0 ]; then
        echo -e "${RED}❌ Found $secrets_count potential secrets in filesystem${NC}"
        echo -e "${YELLOW}📄 Details saved to: $secrets_file${NC}"
        return 1
    else
        echo -e "${GREEN}✅ No secrets detected in filesystem${NC}"
        return 0
    fi
}

# Main execution
main() {
    # Install Trivy if needed
    install_trivy
    
    # Update Trivy database
    echo -e "${BLUE}📥 Updating Trivy vulnerability database...${NC}"
    trivy image --download-db-only --cache-dir "$TRIVY_CACHE_DIR"
    
    # Get list of images from docker-compose dynamically
    echo -e "${BLUE}🐳 Detecting and scanning Docker images from current project...${NC}"
    
    # Build images first
    docker compose build
    
    local exit_code=0
    local scanned_images=()
    
    # Get all services from docker-compose.yml
    local services=($(docker compose config --services 2>/dev/null || echo ""))
    
    if [ ${#services[@]} -eq 0 ]; then
        echo -e "${YELLOW}⚠️  No docker-compose services found, falling back to running containers${NC}"
        # Fallback: get running containers with project prefix
        local project_name=$(basename "$(pwd)" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]//g')
        services=($(docker ps --format "{{.Names}}" | grep "^${project_name}-" | sed "s/^${project_name}-//" | sed 's/-[0-9]*$//' || echo ""))
    fi
    
    echo -e "${BLUE}📋 Found services: ${services[*]}${NC}"
    
    # Scan each service
    for service in "${services[@]}"; do
        if [ -z "$service" ]; then
            continue
        fi
        
        # Get the image for this service
        local image_name=$(docker compose images -q "$service" 2>/dev/null | head -1)
        
        if [ -z "$image_name" ]; then
            # Try to get image from running container
            local container_name=$(docker compose ps -q "$service" 2>/dev/null | head -1)
            if [ -n "$container_name" ]; then
                image_name=$(docker inspect "$container_name" --format='{{.Config.Image}}' 2>/dev/null)
            fi
        fi
        
        if [ -n "$image_name" ]; then
            echo -e "${BLUE}🔍 Scanning service: $service (image: $image_name)${NC}"
            scanned_images+=("$image_name:$service")
            if ! scan_image "$image_name" "$service"; then
                if [ $? -eq 1 ]; then
                    exit_code=1
                fi
            fi
        else
            echo -e "${YELLOW}⚠️  Could not determine image for service: $service${NC}"
        fi
    done
    
    # Scan filesystem for secrets
    if ! scan_secrets; then
        exit_code=1
    fi
    
    # Generate summary report
    echo -e "${BLUE}📊 Generating security summary report...${NC}"
    cat > "$SCAN_RESULTS_DIR/security_summary_${TIMESTAMP}.md" << EOF
# Security Scan Summary - $(date)

## Scanned Images
$(for image_info in "${scanned_images[@]}"; do
    IFS=':' read -r image_name service_name <<< "$image_info"
    echo "- **$service_name**: \`$image_name\`"
done)

## Scan Results
- **Timestamp**: $(date)
- **Trivy Version**: $(trivy --version | head -1)
- **Scan Directory**: $SCAN_RESULTS_DIR

## Files Generated
$(ls -la "$SCAN_RESULTS_DIR"/*${TIMESTAMP}* | awk '{print "- " $9}')

## Recommendations
1. Review HTML reports for detailed vulnerability information
2. Update base images to latest versions
3. Apply security patches for identified vulnerabilities
4. Consider using distroless or minimal base images
5. Implement regular security scanning in CI/CD pipeline

## Next Steps
- Fix critical vulnerabilities before production deployment
- Set up automated scanning in CI/CD pipeline
- Establish vulnerability management process
- Regular security reviews and updates
EOF
    
    echo -e "${GREEN}📄 Security summary saved to: $SCAN_RESULTS_DIR/security_summary_${TIMESTAMP}.md${NC}"
    
    # Final status
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}🎉 All security scans passed successfully!${NC}"
    else
        echo -e "${RED}❌ Security scan failed. Please review and fix vulnerabilities.${NC}"
    fi
    
    echo -e "${BLUE}📁 Scan results saved in: $SCAN_RESULTS_DIR${NC}"
    
    exit $exit_code
}

# Run main function
main "$@"
