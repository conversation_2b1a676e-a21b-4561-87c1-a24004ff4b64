# Bug Report

## BUG-XXX: [REPORTED] Bug Title

**Priority**: P1/P2/P3  
**Reporter**: Name  

## What Happened
Brief description of the bug.

## How to Reproduce
1. Step 1
2. Step 2
3. Bug occurs

## Expected vs Actual
- **Expected**: What should happen
- **Actual**: What actually happened

## Steps to Reproduce
1. Navigate to [specific page/component]
2. Click on [specific element]
3. Enter [specific data]
4. Observe the issue

## Expected Behavior
Describe what should happen when following the steps above.

## Actual Behavior
Describe what actually happens, including any error messages.

## Screenshots/Videos
- [ ] Screenshot attached showing the issue
- [ ] Video recording of the bug reproduction
- [ ] Console logs/error messages captured

## Impact Assessment
- **User Impact**: How many users are affected?
- **Business Impact**: Revenue, compliance, reputation effects
- **Workaround Available**: Yes/No - describe if available
- **Data Loss Risk**: Yes/No - describe potential data impact

## Technical Details
- **Error Messages**: Full error text from console/logs
- **Network Requests**: Failed API calls, status codes
- **Browser Console**: JavaScript errors, warnings
- **Server Logs**: Backend errors, stack traces

## Reproducibility
- [ ] Always reproducible
- [ ] Sometimes reproducible (X% of attempts)
- [ ] Rarely reproducible
- [ ] Cannot reproduce

## Related Issues
- **Duplicate of**: #XXX (if duplicate)
- **Related to**: #XXX (if related)
- **Blocks**: #XXX (if blocking other work)
- **Blocked by**: #XXX (if blocked by other issues)

## Root Cause Analysis (For Development Team)
- **Component**: Which component/module is affected
- **Code Location**: File path and line numbers
- **Suspected Cause**: Initial analysis of the problem
- **Fix Complexity**: Simple/Medium/Complex

## Testing Requirements
- [ ] Unit tests need updating
- [ ] Integration tests need updating
- [ ] Regression testing required
- [ ] Cross-browser testing needed
- [ ] Mobile testing required

## Fix Verification
- [ ] Bug reproduction steps pass
- [ ] No regression in related functionality
- [ ] Performance impact assessed
- [ ] Security implications reviewed

---

## Status Tracking (Updated by Development Team)

**Current Status**: [REPORTED]  
**Assigned To**: TBD  
**Priority Queue**: Backlog/Current Sprint/Hotfix  
**Start Date**: TBD  
**Target Fix Date**: TBD  
**Actual Fix Date**: TBD  

**Status History**:
- YYYY-MM-DD: [REPORTED] - Bug reported and triaged
- YYYY-MM-DD: [CONFIRMED] - Bug reproduction confirmed
- YYYY-MM-DD: [IN_PROGRESS] - Fix development started
- YYYY-MM-DD: [TESTING] - Fix implemented, testing in progress
- YYYY-MM-DD: [RESOLVED] - Fix deployed and verified

**Resolution**: 
- **Fix Description**: How the bug was resolved
- **Code Changes**: Files modified, PR/commit references
- **Testing Performed**: Verification steps completed
- **Deployment**: When and how the fix was deployed

**Last Updated**: YYYY-MM-DD
