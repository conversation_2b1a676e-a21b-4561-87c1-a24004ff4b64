---
description: Deploy application to staging environment with full validation
---

# Deploy to Staging

This workflow deploys the application to staging environment with comprehensive validation.

## Steps

1. **Pre-deployment validation**
   - Ensure you're on a feature branch ready for deployment
   - Run `/test-and-lint` workflow to validate code quality
   - Check that all tests pass and coverage meets requirements

2. **Build production images**
// turbo
   - Build all services: `docker compose -f docker-compose.prod.yml build`
   - Tag images with staging version
   - Push images to container registry if configured

3. **Deploy to staging environment**
// turbo
   - Deploy with staging configuration: `docker compose -f docker-compose.staging.yml up -d`
   - Wait for all services to be healthy
   - Run database migrations if needed

4. **Validate staging deployment**
   - Check all service health endpoints
   - Verify <PERSON>raefik routing is working correctly
   - Test database connectivity and migrations
   - Verify Redis cache is functioning

5. **Run smoke tests**
// turbo
   - API health checks: `curl -f http://api.staging.localhost/health`
// turbo
   - Frontend accessibility: `curl -f http://app.staging.localhost`
   - Database queries: Test basic CRUD operations
   - Authentication flow: Test login/logout

6. **Performance validation**
   - Check response times are within acceptable limits
   - Verify memory and CPU usage are normal
   - Test concurrent user load if load testing is configured

7. **Security validation**
   - Verify HTTPS is enforced
   - Check security headers are present
   - Validate authentication and authorization
   - Test for common vulnerabilities

8. **Update deployment logs**
   - Record deployment time and version in `.project/changelog.md`
   - Document any issues encountered
   - Update staging environment status

Staging deployment complete! Environment is ready for user acceptance testing.
