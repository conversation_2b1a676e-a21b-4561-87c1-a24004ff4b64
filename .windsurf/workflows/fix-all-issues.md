---
description: Systematically identify failing services, fix lint errors, and cycle through fix-test-fix until all Docker services are healthy
---

# Fix All Issues

This workflow systematically identifies and fixes all failing services, lint errors, and Docker issues until everything is running properly.

## Steps

### Phase 1: Initial Assessment

1. **Check Docker service status**
// turbo
   - Run `docker compose ps` to see all service statuses
// turbo
   - Run `docker compose logs --tail=50` to check for immediate errors
   - Document which services are failing or unhealthy

2. **Identify failing services**
   - List all services that are not in "running" state
   - Note any services with health check failures
   - Check for port conflicts or resource issues

### Phase 2: Start Services and Gather Information

3. **Attempt to start all services**
// turbo
   - Run `docker compose up -d` to start all services
   - Wait 30 seconds for services to initialize
// turbo
   - Run `docker compose ps` again to check status

4. **Collect detailed error information**
// turbo
   - For each failing service, run `docker compose logs [service-name] --tail=100`
   - Look for specific error patterns:
     - Compilation errors
     - Missing dependencies
     - Database connection issues
     - Port binding problems
     - Environment variable issues

### Phase 3: Fix-Test Cycle

5. **Start fix-test cycle for each issue**
   - **Priority order**: Database → Cache → Backend API → Frontend → Proxy
   - For each service with issues:

   **a. Fix lint errors first**
// turbo
   - Run linting for the service: `docker compose exec [service] npm run lint` (or appropriate command)
   - If lint errors exist, fix them one by one:
     - Identify the specific lint rule being violated
     - Fix the code to comply with the rule
     - Re-run lint to verify fix
// turbo
   - Continue until `docker compose exec [service] npm run lint` passes

   **b. Fix compilation/build errors**
// turbo
   - Run build command: `docker compose exec [service] npm run build` (or appropriate)
   - If build fails:
     - Identify the specific compilation error
     - Fix import statements, type errors, or syntax issues
     - Re-run build to verify fix
// turbo
   - Continue until build passes

   **c. Fix runtime errors**
// turbo
   - Restart the service: `docker compose restart [service]`
// turbo
   - Check logs: `docker compose logs [service] --tail=20`
   - If runtime errors exist:
     - Fix environment variable issues
     - Fix database connection problems
     - Fix missing dependencies
     - Fix configuration issues
// turbo
   - Restart and check logs again

   **d. Verify service health**
// turbo
   - Check service status: `docker compose ps [service]`
   - Test service endpoint if applicable: `curl -f http://[service].localhost/health`
   - Ensure service is responding correctly

6. **Repeat fix-test cycle until service is healthy**
   - Continue the fix-test cycle for current service until it's fully operational
   - Move to next failing service
   - Repeat until all services are healthy

### Phase 4: Integration Testing

7. **Test inter-service communication**
// turbo
   - Test database connectivity from application services
// turbo
   - Test Redis cache connectivity
// turbo
   - Test API endpoints through Traefik proxy
   - Verify all services can communicate with each other

8. **Run comprehensive tests**
// turbo
   - Run unit tests: `docker compose exec app npm test`
// turbo
   - Run integration tests if available
// turbo
   - Run API health checks for all endpoints

### Phase 5: Final Validation

9. **Verify all services are healthy**
// turbo
   - Run `docker compose ps` - all services should show "running" and "healthy"
// turbo
   - Check logs for any remaining warnings: `docker compose logs --tail=20`
   - Test Traefik proxy routing: `curl -f http://localhost`

10. **Performance and resource check**
// turbo
   - Check resource usage: `docker stats --no-stream`
   - Verify no services are consuming excessive resources
   - Check disk space: `docker system df`

11. **Document any remaining issues**
   - If any issues couldn't be resolved, document them
   - Create GitHub issues or TODO items for complex problems
   - Update project documentation with any configuration changes made

### Phase 6: Cleanup and Commit

12. **Clean up temporary files**
// turbo
   - Remove any temporary debug files created
// turbo
   - Clean up Docker resources if needed: `docker system prune -f`

13. **Commit fixes if changes were made**
// turbo
   - Stage changes: `git add .`
// turbo
   - Commit with descriptive message: `git commit -m "fix: resolve Docker service issues and lint errors"`

## Success Criteria

✅ All Docker services show "running" and "healthy" status
✅ No lint errors in any service
✅ No compilation/build errors
✅ All services accessible through Traefik proxy
✅ Inter-service communication working
✅ All tests passing
✅ No critical errors in logs
✅ Resource usage within normal limits

**All issues resolved! Docker environment is fully operational.**
