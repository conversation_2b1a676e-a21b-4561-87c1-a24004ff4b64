---
description: Diagnose and fix common Docker development issues
---

# Fix Docker Issues

This workflow helps diagnose and resolve common Docker development problems.

## Steps

1. **Check Docker service status**
// turbo
   - Run `docker compose ps` to see all service statuses
// turbo
   - Check for any stopped or unhealthy services
// turbo
   - Run `docker system df` to check disk usage

2. **Examine container logs**
// turbo
   - Check logs for all services: `docker compose logs --tail=100`
   - Look for error messages, stack traces, or warnings
   - Focus on services that are failing or unhealthy

3. **Restart problematic services**
// turbo
   - Restart specific service: `docker compose restart [service-name]`
   - Or restart all services: `docker compose restart`
   - Wait for services to become healthy again

4. **Clean up Docker resources if needed**
// turbo
   - Remove stopped containers: `docker container prune -f`
// turbo
   - Remove unused images: `docker image prune -f`
// turbo
   - Remove unused volumes: `docker volume prune -f`
// turbo
   - Remove unused networks: `docker network prune -f`

5. **Rebuild containers if issues persist**
// turbo
   - Stop all services: `docker compose down`
// turbo
   - Rebuild with no cache: `docker compose build --no-cache`
// turbo
   - Start services: `docker compose up -d`

6. **Verify environment variables**
   - Check that `.env` file exists and has required variables
   - Validate database connection strings
   - Ensure secrets are properly configured
   - Check port conflicts with host system

7. **Test service connectivity**
// turbo
   - Test Traefik proxy: `curl -f http://localhost`
   - Test database connection from app container
   - Verify Redis connectivity
   - Check inter-service communication

8. **Check resource constraints**
   - Monitor CPU and memory usage of containers
   - Check if any containers are being killed due to resource limits
   - Verify disk space is sufficient for all services

Docker issues resolved! All services should now be running properly.
