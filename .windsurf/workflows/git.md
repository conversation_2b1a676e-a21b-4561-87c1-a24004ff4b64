---
description: Git workflow commands and branch management
---

# Git Workflow

This workflow provides common git operations following the project's branching strategy.

## Steps

1. **Check current git status**
// turbo
   - Run `git status` to see current branch and changes
// turbo
   - Run `git branch -a` to see all branches

2. **Create new feature (if needed)**
   - Use `/start-feature` workflow for new features
   - Or manually: `./.project/scripts/create-feature.sh [NUM] "[DESC]"`

3. **Create change branch for development**
// turbo
   - Run `./.project/scripts/create-change.sh [NUM]`
   - Switch to change branch for development

4. **Make commits with conventional format**
// turbo
   - Stage changes: `git add .`
// turbo
   - Commit with format: `git commit -m "feat(FEAT-XXX): description"`

5. **Merge change back to feature**
// turbo
   - Run `./.project/scripts/merge-change.sh [NUM]`
   - This runs tests and merges if successful

6. **Complete feature when ready**
// turbo
   - Run `./.project/scripts/complete-feature.sh`
   - This merges to main branch and updates status

Git workflow complete! Follow the feature → change → merge pattern.
