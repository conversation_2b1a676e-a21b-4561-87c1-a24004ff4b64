---
description: Run comprehensive security scanning on all Docker images and filesystem
---

# Security Scanning Workflow

This workflow runs comprehensive security scanning using Trivy on all Docker images in your project and scans the filesystem for secrets.

## Prerequisites

- Docker and Docker Compose installed
- Project with docker-compose.yml file
- Internet connection for Trivy database updates

## Steps

1. **Navigate to project directory**
```bash
cd /home/<USER>/CascadeProjects/my.continuia
```

2. **Make security scan script executable**
// turbo
```bash
chmod +x .windsurf/scripts/security-scan.sh
```

3. **Run comprehensive security scan**
// turbo
```bash
./.windsurf/scripts/security-scan.sh
```

4. **View scan results**
The script will generate multiple output formats:
- **JSON reports**: `.project/data/security-scans/*_TIMESTAMP.json`
- **HTML reports**: `.project/data/security-scans/*_TIMESTAMP.html`
- **Summary report**: `.project/data/security-scans/security_summary_TIMESTAMP.md`
- **Secrets scan**: `.project/data/security-scans/secrets_TIMESTAMP.json`

5. **Review critical vulnerabilities**
If any CRITICAL vulnerabilities are found, the script will:
- Exit with error code 1
- Display the count of critical vulnerabilities
- Generate detailed reports for investigation

6. **Check filesystem secrets**
The script automatically scans for:
- API keys and tokens
- Passwords and credentials
- Private keys and certificates
- Database connection strings

## What the Script Does

### Dynamic Image Detection
- Automatically detects all services from `docker-compose.yml`
- Gets actual image names from running containers
- Scans both custom-built and third-party images
- No hardcoded image names - works with any Docker Compose project

### Security Scanning
- Downloads latest Trivy vulnerability database
- Scans for HIGH and CRITICAL severity vulnerabilities
- Generates multiple report formats (JSON, HTML, Markdown)
- Scans filesystem for exposed secrets
- Provides actionable security insights

### Report Generation
- **Service-specific reports**: One report per Docker service
- **Consolidated summary**: Overview of all scanned images
- **Timestamp-based naming**: Easy to track scan history
- **Multiple formats**: JSON for automation, HTML for viewing

## Example Output

```
🐳 Detecting and scanning Docker images from current project...
📋 Found services: traefik postgres minio api app letta
🔍 Scanning service: traefik (image: traefik:v3.0)
✅ traefik scan completed
🔍 Scanning service: postgres (image: postgres:15-alpine)
✅ postgres scan completed
🔍 Scanning service: minio (image: minio/minio:RELEASE.2025-07-23T15-54-02Z)
✅ minio scan completed
🔐 Scanning filesystem for secrets...
✅ No secrets detected in filesystem
📊 Generating security summary report...
✅ Security scan completed successfully!
```

## Troubleshooting

### Script fails to detect services
- Ensure you're in the project root directory
- Check that `docker-compose.yml` exists
- Verify Docker Compose is installed: `docker compose version`

### Trivy installation fails
- Check internet connection
- Verify you have sudo permissions
- Try manual installation: `curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin`

### No scan results generated
- Check that Docker images exist: `docker compose images`
- Ensure containers are built: `docker compose build`
- Verify write permissions to `.project/data/security-scans/`

## Integration with CI/CD

The security scan can be integrated into your CI/CD pipeline:

1. **GitHub Actions**: Use the existing `.github/workflows/security-scan.yml`
2. **Local Development**: Run before commits or deployments
3. **Scheduled Scans**: Set up cron jobs for regular security audits

## Security Best Practices

- **Run regularly**: Security vulnerabilities are discovered daily
- **Act on CRITICAL**: Address critical vulnerabilities immediately
- **Review secrets**: Investigate any detected secrets or credentials
- **Update images**: Keep base images and dependencies updated
- **Monitor trends**: Track vulnerability counts over time
