---
description: Initialize a new project with proper foundation and Docker environment
---

# Setup New Project

This workflow initializes a new project with all necessary foundation files and Docker environment.

## Steps

1. **Check if this is a fresh repository**
   - Run `git log --oneline` to check if any commits exist
   - If no commits exist, proceed with initialization

2. **Create foundation files**
   - Create `.project/charter.md` with project charter header
   - Create `.project/changelog.md` with changelog header  
   - Create `README.md` with project name
   - Create `.gitignore` with appropriate exclusions

3. **Initialize git repository properly**
   - Run `git add .`
   - Run `git commit -m "chore: initial project setup with foundation files"`

4. **Set up Docker environment**
   - Create `docker-compose.yml` for development environment
   - Create `docker-compose.prod.yml` for production
   - Create `.env.example` template
   - Create `.dockerignore` file

5. **Create project structure**
   - Create `src/` directory structure following DDD patterns
   - Create `tests/` directory
   - Create `docs/` directory
   - Create `.data/` directory for Docker persistence

6. **Validate setup**
   - Run `git status` to ensure clean working state
   - Check that all foundation files exist
   - Verify Docker compose files are valid

7. **Create initial feature branch**
   - Run `./.project/scripts/create-feature.sh 001 "Initial project setup"`
   - Update project charter with first feature

Project is now ready for development!
