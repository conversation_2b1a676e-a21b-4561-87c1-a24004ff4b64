---
description: Start a new feature with proper git workflow and Docker environment
---

# Start New Feature

This workflow creates a new feature branch and sets up the development environment.

## Steps

1. **Validate git repository state**
   - Check `git status` to ensure clean working directory
   - Ensure you're on the default branch (main/master)
   - Pull latest changes if remote exists

2. **Create new feature**
   - Ask user for feature number (3-digit format like 007)
   - Ask user for feature description
   - Run `./.project/scripts/create-feature.sh [NUMBER] "[DESCRIPTION]"`

3. **Set up development environment**
// turbo
   - Run `docker compose up -d` to start all services
   - Wait for all services to be healthy
   - Check logs: `docker compose logs --tail=50`

4. **Verify environment is ready**
   - Check that Traefik proxy is running on port 80
   - Verify all services are accessible through proxy
   - Test database connection
   - Verify Redis cache is running

5. **Create first change branch**
   - Run `./.project/scripts/create-change.sh 1`
   - Switch to change-1 branch for development

6. **Update project charter**
   - Add feature details to `.project/charter.md`
   - Commit charter updates

Feature development environment is ready! Start coding in the change-1 branch.
