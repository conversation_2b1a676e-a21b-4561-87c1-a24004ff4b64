---
description: Run comprehensive testing and linting across all services
---

# Test and Lint Project

This workflow runs all quality checks, tests, and linting across the entire project.

## Steps

1. **Ensure Docker environment is running**
// turbo
   - Run `docker compose up -d` to start all services
   - Wait for services to be healthy

2. **Run linting checks**
// turbo
   - Frontend: `docker compose exec app npm run lint`
// turbo
   - Backend API: `docker compose exec api npm run lint`
// turbo
   - Python services: `docker compose exec python-api flake8 . && black --check .`

3. **Run type checking**
// turbo
   - TypeScript: `docker compose exec app npm run type-check`
// turbo
   - Python: `docker compose exec python-api mypy .`

4. **Run unit tests**
// turbo
   - Frontend: `docker compose exec app npm test`
// turbo
   - Backend API: `docker compose exec api npm test`
// turbo
   - Python: `docker compose exec python-api pytest tests/unit/`

5. **Run integration tests**
// turbo
   - API integration: `docker compose exec api npm run test:integration`
// turbo
   - Python integration: `docker compose exec python-api pytest tests/integration/`

6. **Run security scans**
// turbo
   - Node.js audit: `docker compose exec app npm audit --audit-level=moderate`
// turbo
   - Python security: `docker compose exec python-api safety check`

7. **Build production images**
// turbo
   - Build all services: `docker compose -f docker-compose.prod.yml build`

8. **Generate test reports**
   - Collect coverage reports from all services
   - Generate consolidated quality report
   - Check that coverage meets minimum thresholds (80%)

9. **Validate Docker health**
   - Check all container health statuses
   - Verify no critical errors in logs
   - Confirm all services are responding

All quality checks complete! Review any failures before proceeding with deployment.
