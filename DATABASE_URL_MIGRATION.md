# Database Configuration Consolidation

## 🎯 Problem Solved

Previously, the codebase used both `DATABASE_URL` and individual `POSTGRES_*` variables inconsistently across different files. This caused confusion and potential configuration mismatches.

## ✅ Solution: Standardize on DATABASE_URL

We've consolidated all database connections to use the standard `DATABASE_URL` format, which is the industry standard for database connections.

### Before (Inconsistent):
```bash
# Some files used individual variables
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=continuia

# Other files used DATABASE_URL
DATABASE_URL=********************************************/continuia
```

### After (Consistent):
```bash
# Single source of truth - ALL application code uses this
DATABASE_URL=********************************************/continuia

# Individual components (only for Docker Compose PostgreSQL container)
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=continuia
```

## 🔧 Files Updated

### ✅ Fixed Files:
1. **`api/drizzle.config.ts`** - Now uses `DATABASE_URL` with `connectionString`
2. **`docker-compose.yml`** - Removed redundant `POSTGRES_HOST` and `POSTGRES_PORT`
3. **`.env` and `.env.docker`** - Added `DATABASE_URL` as primary configuration
4. **`api/run-migrations.cjs`** - Parses `DATABASE_URL` for legacy script compatibility

### ✅ Already Correct Files:
- `api/src/db/index.ts` - Main database connection (was already using `DATABASE_URL`)
- `api/src/run-migrations.ts` - Migration runner
- All script files in `api/src/scripts/`
- `api/.env.example`

## 🌐 DATABASE_URL Format

The standard PostgreSQL connection string format:
```
postgresql://[username]:[password]@[host]:[port]/[database]
```

### Examples:
```bash
# Local development
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/continuia

# Docker environment
DATABASE_URL=********************************************/continuia

# Production (with SSL)
DATABASE_URL=*************************************/continuia?sslmode=require
```

## 🔄 Migration Path

### For Development:
1. Update your `.env` file to include `DATABASE_URL`
2. Keep legacy `POSTGRES_*` variables for now (Docker Compose still needs them)
3. All application code now uses `DATABASE_URL` exclusively

### For Production:
1. Set `DATABASE_URL` environment variable
2. Remove individual `POSTGRES_*` variables from your deployment configuration
3. Update any custom scripts to use `DATABASE_URL`

## 🛡️ Benefits

1. **Consistency**: Single configuration method across all files
2. **Industry Standard**: `DATABASE_URL` is the standard for database connections
3. **Simplicity**: One variable instead of five
4. **Portability**: Easy to move between environments
5. **Security**: Connection strings can include SSL and other security parameters

## 🔍 Verification

To verify your database connection is working:

```bash
# Check if DATABASE_URL is set
echo $DATABASE_URL

# Test connection (if psql is available)
psql $DATABASE_URL -c "SELECT version();"
```

## 🚨 Why POSTGRES_* Variables Still Exist

**The Reality:** We still need individual `POSTGRES_*` variables for these specific cases:

1. **PostgreSQL Docker Container**: The official PostgreSQL Docker image requires `POSTGRES_USER`, `POSTGRES_PASSWORD`, and `POSTGRES_DB` environment variables to initialize the database.

2. **Legacy Migration Scripts**: Some migration scripts were written to use individual variables.

**The Solution:**
- **Primary**: All application code uses `DATABASE_URL`
- **Secondary**: Individual variables are kept in sync for Docker/legacy compatibility
- **Tool**: Use `parse-database-url.sh` to extract individual components from `DATABASE_URL`

This is the cleanest approach that maintains compatibility while standardizing on `DATABASE_URL`.

## 📋 Next Steps

1. **Phase 1** (✅ Complete): Update application code to use `DATABASE_URL`
2. **Phase 2** (Future): Remove legacy `POSTGRES_*` variables from Docker Compose
3. **Phase 3** (Future): Update all migration scripts to use `DATABASE_URL` natively

## 🔧 Troubleshooting

### Common Issues:

1. **Connection refused**: Check if the host/port in `DATABASE_URL` is correct
2. **Authentication failed**: Verify username/password in the connection string
3. **Database not found**: Ensure the database name is correct in the URL

### Debug Commands:
```bash
# Parse DATABASE_URL in Node.js
node -e "console.log(new URL(process.env.DATABASE_URL))"

# Test connection
npm run db:test-connection
```
