# Migration System Consolidation

## 🎯 Problem Solved

Previously, you had **4 different migration systems** running in parallel:

1. **`api/drizzle/`** - Drizzle Kit auto-generated (5 files)
2. **`api/src/db/migrations/`** - Drizzle ORM manual (17+ files)  
3. **`api/migrations-clean/`** - Custom bash system (18 files)
4. **`api/src/migrations/`** - Legacy/unused (5 files)

This created confusion, potential conflicts, and made it impossible to know the true database state.

## ✅ Solution: Single Drizzle Kit System

We've consolidated everything into **one clean Drizzle Kit system** that:

- ✅ **Starts from zero** (fresh database)
- ✅ **Includes ALL migrations** from the most complete system
- ✅ **Includes ALL seed data** (roles, permissions, users, note types)
- ✅ **Uses standard Drizzle workflow**
- ✅ **Eliminates conflicts** between systems

## 🚀 How to Run the Consolidation

### Prerequisites
```bash
# Ensure DATABASE_URL is set
echo $DATABASE_URL

# Should output something like:
# ********************************************/continuia
```

### Run Consolidation
```bash
# This will consolidate everything into a single system
./consolidate-migrations.sh
```

### What the Script Does

1. **📦 Backup** - Creates backup of existing database
2. **🗑️ Fresh Start** - Drops and recreates database  
3. **📋 Apply migrations-clean** - Uses the most complete migration system (18 migrations)
4. **🌱 Seed Data** - Runs all seed scripts (permissions, note types)
5. **🔧 Generate Schema** - Creates fresh Drizzle schema from consolidated database
6. **📦 Archive Old** - Moves old migration systems to archive folder
7. **🧹 Clean Scripts** - Updates package.json with clean commands

## 📊 What Gets Consolidated

### Database Schema (from migrations-clean)
- **Foundation**: Users, roles, permissions, audit logs
- **Core Business**: Cases, documents, appointments, medical opinions
- **Advanced Features**: Legal compliance, CRM, specializations, credentials
- **Initial Data**: Admin/doctor/patient users, roles, permissions

### Seed Data Included
- **Roles**: patient, doctor, agent, admin
- **Permissions**: Complete OPAL permission matrix
- **Users**: <EMAIL>, <EMAIL>, <EMAIL> (password: "Continuia")
- **Note Types**: All configurable note types for doctors
- **Sample Case**: Ready for testing document uploads

## 🔧 New Workflow

### Development Workflow
```bash
# 1. Make schema changes in api/src/db/schema/*
# 2. Generate migration
npm run db:generate

# 3. Apply to database  
npm run db:migrate

# 4. Open database studio (optional)
npm run db:studio
```

### Seeding Commands
```bash
# Seed permissions (critical for OPAL)
npm run db:seed-permissions

# Seed note types
npm run db:seed

# Seed test data (120 cases, users)
npm run db:seed-test-data

# Validate database connection
npm run db:validate
```

## 📁 File Structure After Consolidation

```
api/
├── drizzle/                    # ✅ ACTIVE - Single migration system
│   ├── 0000_comprehensive.sql  # Generated from consolidated schema
│   └── meta/
├── src/
│   ├── db/
│   │   └── schema/             # ✅ Source of truth for schema
│   └── scripts/                # ✅ Seed scripts
│       ├── seed.ts
│       ├── seed-permissions.ts
│       └── wipe-and-seed-test-data.ts
├── migrations-clean/           # 📚 Reference (can be removed later)
└── migration-archive/          # 📦 Old systems archived here
    └── 20241203_123456/
        ├── src-db-migrations/
        ├── src-migrations/
        ├── run-migrations.cjs
        └── src-run-migrations.ts
```

## 🔐 Login Credentials

After consolidation, you can login with:

- **Admin**: <EMAIL> / Continuia
- **Doctor**: <EMAIL> / Continuia  
- **Patient**: <EMAIL> / Continuia

⚠️ **Change these passwords in production!**

## 🧪 Testing the Consolidation

### 1. Validate Database
```bash
npm run db:validate
```

### 2. Test Authentication
- Try logging in with the provided credentials
- Verify appointments and discussions work (should fix 401/403 errors)

### 3. Test Permissions
- Login as different roles
- Verify proper access controls

### 4. Add Test Data (Optional)
```bash
npm run db:seed-test-data
```
This creates 120 test cases and additional users for comprehensive testing.

## 🔄 Rollback Plan

If something goes wrong:

1. **Restore from backup**:
   ```bash
   # Find your backup file
   ls -la database_backup_*.sql
   
   # Restore it
   PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" < database_backup_YYYYMMDD_HHMMSS.sql
   ```

2. **Restore old migration systems**:
   ```bash
   # Find your archive
   ls -la migration-archive/
   
   # Restore files from archive
   cp -r migration-archive/YYYYMMDD_HHMMSS/* api/
   ```

## 🎉 Benefits Achieved

1. **Single Source of Truth**: Only Drizzle Kit manages migrations
2. **Complete Schema**: All tables and data from the most comprehensive system
3. **Standard Workflow**: Uses official Drizzle patterns
4. **No Conflicts**: Eliminated competing migration systems
5. **Fresh Start**: Clean database state from zero
6. **All Data Included**: Permissions, users, and seed data ready to go
7. **Easy Maintenance**: Standard npm scripts for all operations

## 📋 Next Steps

1. **Test thoroughly** with the consolidated system
2. **Update CI/CD** to use new migration commands
3. **Remove migrations-clean** folder when confident (currently kept as reference)
4. **Update documentation** to reflect new workflow
5. **Train team** on new Drizzle Kit workflow

The consolidation eliminates the confusion of multiple migration systems while ensuring nothing is lost from your existing database structure and data.
