FROM node:22-alpine

WORKDIR /app

# Install PostgreSQL client and bash for migration scripts
RUN apk add --no-cache postgresql-client bash

# Install dependencies first (for better caching)
COPY package*.json ./
RUN npm install

# Copy source code
COPY . .

# Copy migration system and make scripts executable
COPY migrations-clean/ ./migrations-clean/
RUN chmod +x ./migrations-clean/*.sh

# Copy scripts and make them executable
COPY scripts/ /usr/local/bin/
RUN chmod +x /usr/local/bin/migrate.sh && \
    chmod +x /usr/local/bin/validate-migrations.sh && \
    chmod +x /usr/local/bin/start-with-migrations.sh && \
    ln -s /usr/local/bin/migrate.sh /usr/local/bin/migrate && \
    ln -s /usr/local/bin/validate-migrations.sh /usr/local/bin/validate-migrations && \
    ln -s /usr/local/bin/start-with-migrations.sh /usr/local/bin/start-with-migrations

# Expose port
EXPOSE 3001

# Start with migrations then run the app
CMD ["start-with-migrations"]
