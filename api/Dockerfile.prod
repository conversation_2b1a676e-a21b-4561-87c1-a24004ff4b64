# Production Dockerfile for API Service with Security Best Practices
# Multi-stage build for smaller, more secure final image

# Build stage
FROM node:20-alpine AS builder

# Install security updates and PostgreSQL client
RUN apk update && apk upgrade && apk add --no-cache \
    dumb-init \
    postgresql-client \
    bash \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies (including dev dependencies for build)
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build application (if needed)
RUN npm run build 2>/dev/null || echo "No build script found"

# Production stage
FROM node:20-alpine AS production

# Install security updates, dumb-init, and PostgreSQL client
RUN apk update && apk upgrade && apk add --no-cache \
    dumb-init \
    postgresql-client \
    bash \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Create app directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=nodejs:nodejs /app/node_modules ./node_modules/
COPY --from=builder --chown=nodejs:nodejs /app/package.json ./
COPY --from=builder --chown=nodejs:nodejs /app/package-lock.json ./
COPY --from=builder --chown=nodejs:nodejs /app/src ./src/
# The problematic COPY command has been removed.

# Copy migration system and scripts
COPY --from=builder --chown=nodejs:nodejs /app/migrations-clean ./migrations-clean/
COPY --from=builder --chown=nodejs:nodejs /app/scripts ./scripts/

# Set permissions for scripts and create symlinks
RUN chmod +x ./scripts/*.sh && \
    ln -s /app/scripts/migrate.sh /usr/local/bin/migrate && \
    ln -s /app/scripts/validate-migrations.sh /usr/local/bin/validate-migrations && \
    ln -s /app/scripts/start-with-migrations.sh /usr/local/bin/start-with-migrations

# Install production dependencies
RUN npm install --production

# Install production dependencies
RUN npm install --production

# Set proper permissions
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start application
# Start application with migrations
CMD ["start-with-migrations"]
