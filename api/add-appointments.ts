import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { users, appointments } from './src/db/schema/index.js';
import { eq } from 'drizzle-orm';

// Create database connection for running outside Docker
const connectionString = 'postgresql://postgres:postgres@localhost:5432/continuia';
const sql = postgres(connectionString, {
  max: 1,
  connect_timeout: 10,
});
const db = drizzle(sql);

async function addAppointmentsForUser() {
  try {
    console.log('🔍 <NAME_EMAIL>...');
    
    // Find the user by email
    const user = await db.select().from(users).where(eq(users.email, '<EMAIL>')).limit(1);
    
    if (user.length === 0) {
      console.error('❌ User <EMAIL> not found in database');
      console.log('Available users:');
      const allUsers = await db.select({ email: users.email, firstName: users.firstName, lastName: users.lastName }).from(users);
      console.table(allUsers);
      return;
    }

    const patientUser = user[0];
    console.log(`✅ Found user: ${patientUser.firstName} ${patientUser.lastName} (${patientUser.email})`);

    // Find a doctor user to assign appointments to
    const doctors = await db.select().from(users).where(eq(users.role, 'doctor')).limit(1);
    
    if (doctors.length === 0) {
      console.error('❌ No doctor found in database. Creating a sample doctor...');
      
      // Create a sample doctor
      const [newDoctor] = await db.insert(users).values({
        email: '<EMAIL>',
        passwordHash: '$2b$10$dummy.hash.for.testing',
        firstName: 'Dr. Sarah',
        lastName: 'Smith',
        role: 'doctor',
        isActive: true,
        isEmailVerified: true,
      }).returning();
      
      console.log(`✅ Created doctor: ${newDoctor.firstName} ${newDoctor.lastName}`);
      doctors.push(newDoctor);
    }

    const doctor = doctors[0];
    console.log(`✅ Using doctor: ${doctor.firstName} ${doctor.lastName} (${doctor.email})`);

    // Create sample appointments
    const now = new Date();
    const appointmentsData = [
      {
        patientId: patientUser.id,
        doctorId: doctor.id,
        appointmentType: 'consultation' as const,
        scheduledAt: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        duration: 30,
        status: 'scheduled' as const,
        notes: 'Initial consultation for new patient',
        meetingLink: 'https://meet.continuia.com/room/abc123',
      },
      {
        patientId: patientUser.id,
        doctorId: doctor.id,
        appointmentType: 'follow_up' as const,
        scheduledAt: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
        duration: 20,
        status: 'scheduled' as const,
        notes: 'Follow-up appointment to review test results',
        meetingLink: 'https://meet.continuia.com/room/def456',
      },
      {
        patientId: patientUser.id,
        doctorId: doctor.id,
        appointmentType: 'review' as const,
        scheduledAt: new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000), // 2 weeks from now
        duration: 15,
        status: 'scheduled' as const,
        notes: 'Review medication effectiveness and side effects',
        meetingLink: 'https://meet.continuia.com/room/ghi789',
      },
      {
        patientId: patientUser.id,
        doctorId: doctor.id,
        appointmentType: 'consultation' as const,
        scheduledAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        duration: 45,
        status: 'completed' as const,
        notes: 'Completed consultation - discussed treatment plan',
        meetingLink: 'https://meet.continuia.com/room/jkl012',
      },
      {
        patientId: patientUser.id,
        doctorId: doctor.id,
        appointmentType: 'emergency' as const,
        scheduledAt: new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000), // Tomorrow
        duration: 60,
        status: 'confirmed' as const,
        notes: 'Emergency consultation for urgent symptoms',
        meetingLink: 'https://meet.continuia.com/room/mno345',
      },
    ];

    console.log('📅 Adding appointments...');
    const insertedAppointments = await db.insert(appointments).values(appointmentsData).returning();

    console.log(`✅ Successfully added ${insertedAppointments.length} appointments for ${patientUser.email}`);
    
    // Display the created appointments
    console.log('\n📋 Created Appointments:');
    insertedAppointments.forEach((apt, index) => {
      console.log(`${index + 1}. ${apt.appointmentType} - ${apt.scheduledAt.toISOString()} (${apt.status})`);
      console.log(`   Duration: ${apt.duration} minutes`);
      console.log(`   Notes: ${apt.notes}`);
      console.log(`   Meeting Link: ${apt.meetingLink}`);
      console.log('');
    });

    console.log('🎉 All appointments added successfully!');

  } catch (error) {
    console.error('❌ Error adding appointments:', error);
    throw error;
  }
}

// Run the script
addAppointmentsForUser()
  .then(async () => {
    console.log('✅ Script completed successfully');
    await sql.end();
    process.exit(0);
  })
  .catch(async (error) => {
    console.error('❌ Script failed:', error);
    await sql.end();
    process.exit(1);
  });
