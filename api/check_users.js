// Simple script to check users in the database
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { users } from './src/db/schema/users.ts';

async function checkUsers() {
  const connectionString = process.env.DATABASE_URL || 'postgresql://continuia_user:continuia_password@localhost:5432/continuia_db';
  const sql = postgres(connectionString);
  const db = drizzle(sql);

  try {
    const allUsers = await db.select().from(users);
    console.log('Total users:', allUsers.length);
    
    const usersByRole = {};
    allUsers.forEach(user => {
      const role = user.role || 'unknown';
      usersByRole[role] = (usersByRole[role] || 0) + 1;
    });
    
    console.log('Users by role:', usersByRole);
    
    // Show first few users for debugging
    console.log('\nFirst 5 users:');
    allUsers.slice(0, 5).forEach(user => {
      console.log(`- ID: ${user.id}, Email: ${user.email}, Role: ${user.role}, Active: ${user.isActive}`);
    });
    
  } catch (error) {
    console.error('Error checking users:', error);
  } finally {
    await sql.end();
  }
}

checkUsers();