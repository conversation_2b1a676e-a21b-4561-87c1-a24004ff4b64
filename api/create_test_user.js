import bcrypt from 'bcrypt';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { users } from './src/db/schema/users.js';

async function createTestUser() {
  // Database connection
  const connectionString = process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/continuia';
  const sql = postgres(connectionString);
  const db = drizzle(sql);

  try {
    // Hash the password
    const password = 'testpass123';
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create test user
    const [newUser] = await db.insert(users).values({
      email: '<EMAIL>',
      passwordHash,
      firstName: 'Test',
      lastName: 'Patient',
      role: 'patient',
      isActive: true,
    }).returning({
      id: users.id,
      email: users.email,
      firstName: users.firstName,
      lastName: users.lastName,
      role: users.role,
    });

    console.log('Created test user:', newUser);
    console.log('Email: <EMAIL>');
    console.log('Password: testpass123');
    
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await sql.end();
  }
}

createTestUser();