# Docker Compose for Migration Management
# Use this to run migrations on demand without affecting the main application

version: '3.8'

services:
  # Migration runner service
  migrations:
    build:
      context: ./
      dockerfile: Dockerfile
    container_name: continuia-migrations
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - ./migrations-clean:/app/migrations-clean
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - continuia
    # Override the default command - this service won't start automatically
    # Use: docker compose -f docker-compose.migrations.yml run --rm migrations <command>
    command: ["tail", "-f", "/dev/null"]
    profiles:
      - migrations

  # PostgreSQL Database (same as main docker-compose.yml)
  postgres:
    image: postgres:16-alpine
    container_name: continuia-postgres-migrations
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    networks:
      - continuia
    volumes:
      - postgres-migration-data:/var/lib/postgresql/data/pgdata
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres-migration-data:

networks:
  continuia:
    external: true