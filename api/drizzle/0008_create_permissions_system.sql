-- Migration: Create permission-based authentication system
-- This migration creates tables for roles, permissions, and their relationships

-- Create roles table
CREATE TABLE IF NOT EXISTS "roles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(50) NOT NULL,
	"display_name" varchar(100) NOT NULL,
	"description" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "roles_name_unique" UNIQUE("name")
);

-- Create permissions table
CREATE TABLE IF NOT EXISTS "permissions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	"display_name" varchar(150) NOT NULL,
	"description" text,
	"resource" varchar(50) NOT NULL,
	"action" varchar(50) NOT NULL,
	"scope" varchar(50) DEFAULT 'global',
	"filter_conditions" jsonb,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "permissions_name_unique" UNIQUE("name")
);

-- Create role_permissions junction table
CREATE TABLE IF NOT EXISTS "role_permissions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"role_id" uuid NOT NULL,
	"permission_id" uuid NOT NULL,
	"override_filter_conditions" jsonb,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	PRIMARY KEY ("role_id", "permission_id")
);

-- Create user_roles junction table (replaces single role column)
CREATE TABLE IF NOT EXISTS "user_roles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"role_id" uuid NOT NULL,
	"assigned_by" uuid,
	"assigned_at" timestamp DEFAULT now() NOT NULL,
	"expires_at" timestamp,
	"context" jsonb,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	PRIMARY KEY ("user_id", "role_id")
);

-- Create permission audit log table
CREATE TABLE IF NOT EXISTS "permission_audit_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"permission_name" varchar(100) NOT NULL,
	"resource" varchar(50) NOT NULL,
	"action" varchar(50) NOT NULL,
	"endpoint" varchar(200),
	"method" varchar(10),
	"ip_address" varchar(45),
	"user_agent" text,
	"granted" boolean NOT NULL,
	"reason" text,
	"filter_applied" jsonb,
	"timestamp" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_permission_id_permissions_id_fk" FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_assigned_by_users_id_fk" FOREIGN KEY ("assigned_by") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "permission_audit_log" ADD CONSTRAINT "permission_audit_log_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_roles_name" ON "roles"("name");
CREATE INDEX IF NOT EXISTS "idx_roles_is_active" ON "roles"("is_active");
CREATE INDEX IF NOT EXISTS "idx_permissions_resource_action" ON "permissions"("resource", "action");
CREATE INDEX IF NOT EXISTS "idx_permissions_is_active" ON "permissions"("is_active");
CREATE INDEX IF NOT EXISTS "idx_role_permissions_role_id" ON "role_permissions"("role_id");
CREATE INDEX IF NOT EXISTS "idx_role_permissions_permission_id" ON "role_permissions"("permission_id");
CREATE INDEX IF NOT EXISTS "idx_user_roles_user_id" ON "user_roles"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_roles_role_id" ON "user_roles"("role_id");
CREATE INDEX IF NOT EXISTS "idx_user_roles_is_active" ON "user_roles"("is_active");
CREATE INDEX IF NOT EXISTS "idx_permission_audit_log_user_id" ON "permission_audit_log"("user_id");
CREATE INDEX IF NOT EXISTS "idx_permission_audit_log_timestamp" ON "permission_audit_log"("timestamp");

-- Insert default roles
INSERT INTO "roles" ("name", "display_name", "description") VALUES
('patient', 'Patient', 'Healthcare service recipient with access to personal medical records'),
('doctor', 'Healthcare Provider', 'Licensed medical professional providing patient care'),
('admin', 'System Administrator', 'Full system access for administrative tasks'),
('agent', 'AI Agent', 'Automated system agent for AI-assisted operations')
ON CONFLICT ("name") DO NOTHING;

-- Insert default permissions
INSERT INTO "permissions" ("name", "display_name", "description", "resource", "action", "scope") VALUES
-- Case permissions
('cases:read', 'Read Cases', 'View medical cases', 'cases', 'read', 'global'),
('cases:read:own', 'Read Own Cases', 'View own medical cases', 'cases', 'read', 'own'),
('cases:read:assigned', 'Read Assigned Cases', 'View assigned medical cases', 'cases', 'read', 'assigned'),
('cases:write', 'Write Cases', 'Create and modify medical cases', 'cases', 'write', 'global'),
('cases:write:assigned', 'Write Assigned Cases', 'Modify assigned medical cases', 'cases', 'write', 'assigned'),
('cases:delete', 'Delete Cases', 'Delete medical cases', 'cases', 'delete', 'global'),

-- User permissions
('users:read', 'Read Users', 'View user accounts', 'users', 'read', 'global'),
('users:write', 'Write Users', 'Create and modify user accounts', 'users', 'write', 'global'),
('users:delete', 'Delete Users', 'Delete user accounts', 'users', 'delete', 'global'),

-- Document permissions
('documents:read', 'Read Documents', 'View medical documents', 'documents', 'read', 'global'),
('documents:read:own', 'Read Own Documents', 'View own medical documents', 'documents', 'read', 'own'),
('documents:write', 'Write Documents', 'Upload and modify medical documents', 'documents', 'write', 'global'),
('documents:write:own', 'Write Own Documents', 'Upload and modify own medical documents', 'documents', 'write', 'own'),
('documents:delete', 'Delete Documents', 'Delete medical documents', 'documents', 'delete', 'global'),

-- Appointment permissions
('appointments:read', 'Read Appointments', 'View appointments', 'appointments', 'read', 'global'),
('appointments:read:own', 'Read Own Appointments', 'View own appointments', 'appointments', 'read', 'own'),
('appointments:write', 'Write Appointments', 'Create and modify appointments', 'appointments', 'write', 'global'),
('appointments:write:own', 'Write Own Appointments', 'Create and modify own appointments', 'appointments', 'write', 'own'),
('appointments:delete', 'Delete Appointments', 'Delete appointments', 'appointments', 'delete', 'global'),

-- API endpoint permissions
('api/cases:get', 'API: GET Cases', 'Access GET /api/cases endpoint', 'api', 'get', 'global'),
('api/cases:post', 'API: POST Cases', 'Access POST /api/cases endpoint', 'api', 'post', 'global'),
('api/cases:put', 'API: PUT Cases', 'Access PUT /api/cases endpoint', 'api', 'put', 'global'),
('api/cases:delete', 'API: DELETE Cases', 'Access DELETE /api/cases endpoint', 'api', 'delete', 'global'),

-- Admin permissions
('admin:system', 'System Administration', 'Full system administration access', 'admin', 'manage', 'global'),
('admin:users', 'User Administration', 'Manage user accounts and roles', 'admin', 'manage', 'global'),
('admin:audit', 'Audit Access', 'View system audit logs', 'admin', 'read', 'global')

ON CONFLICT ("name") DO NOTHING;

-- Assign permissions to roles
WITH role_permission_assignments AS (
  SELECT 
    r.id as role_id,
    p.id as permission_id
  FROM roles r
  CROSS JOIN permissions p
  WHERE 
    -- Patient permissions
    (r.name = 'patient' AND p.name IN (
      'cases:read:own', 'documents:read:own', 'documents:write:own', 
      'appointments:read:own', 'appointments:write:own'
    )) OR
    -- Doctor permissions  
    (r.name = 'doctor' AND p.name IN (
      'cases:read', 'cases:read:assigned', 'cases:write:assigned',
      'documents:read', 'documents:write', 
      'appointments:read', 'appointments:write',
      'api/cases:get', 'api/cases:post', 'api/cases:put'
    )) OR
    -- Admin permissions (all permissions)
    (r.name = 'admin') OR
    -- Agent permissions
    (r.name = 'agent' AND p.name IN (
      'cases:read', 'documents:read', 'appointments:read',
      'api/cases:get'
    ))
)
INSERT INTO "role_permissions" ("role_id", "permission_id")
SELECT role_id, permission_id FROM role_permission_assignments
ON CONFLICT ("role_id", "permission_id") DO NOTHING;

-- Migrate existing user roles to new system
INSERT INTO "user_roles" ("user_id", "role_id", "assigned_at")
SELECT 
  u.id as user_id,
  r.id as role_id,
  u.created_at as assigned_at
FROM users u
JOIN roles r ON r.name = u.role
WHERE u.role IS NOT NULL
ON CONFLICT ("user_id", "role_id") DO NOTHING;
