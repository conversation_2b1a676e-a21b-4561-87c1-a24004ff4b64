-- Add acceptance status and time tracking to case_doctors table
-- This allows tracking individual doctor acceptance per case and time spent

-- Add acceptance status enum
CREATE TYPE "doctor_acceptance_status" AS ENUM('pending', 'accepted', 'declined', 'closed');

-- Add acceptance status column to case_doctors table
ALTER TABLE "case_doctors" ADD COLUMN "acceptance_status" "doctor_acceptance_status" DEFAULT 'pending' NOT NULL;

-- Add accepted_at timestamp for tracking when doctor accepted
ALTER TABLE "case_doctors" ADD COLUMN "accepted_at" timestamp;

-- Add time tracking fields
ALTER TABLE "case_doctors" ADD COLUMN "time_spent_minutes" integer DEFAULT 0 NOT NULL;
ALTER TABLE "case_doctors" ADD COLUMN "last_activity_at" timestamp;
ALTER TABLE "case_doctors" ADD COLUMN "notes" text;

-- Update existing records to have 'accepted' status if they were created before this migration
-- This assumes existing assignments were already accepted
UPDATE "case_doctors" SET "acceptance_status" = 'accepted', "accepted_at" = "created_at", "last_activity_at" = "created_at" WHERE "is_active" = true;