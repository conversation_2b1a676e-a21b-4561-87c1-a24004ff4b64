-- Doctor Credentials Migration
-- This migration adds the doctor credentials, profile URLs, and credential documents tables

-- Create enums for doctor credentials
DO $$ BEGIN
 CREATE TYPE "credential_status" AS ENUM('pending', 'verified', 'expired', 'revoked', 'under_review');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "credential_type" AS ENUM('medical_license', 'board_certification', 'dea_registration', 'npi_number', 'hospital_privileges', 'malpractice_insurance', 'continuing_education', 'other');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "verification_method" AS ENUM('manual', 'automated', 'third_party', 'document_upload');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create doctor_credentials table
CREATE TABLE IF NOT EXISTS "doctor_credentials" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"doctor_id" uuid NOT NULL,
	"credential_type" "credential_type" NOT NULL,
	"credential_number" varchar(100) NOT NULL,
	"issuing_authority" varchar(255) NOT NULL,
	"issued_date" timestamp NOT NULL,
	"expiration_date" timestamp,
	"status" "credential_status" DEFAULT 'pending' NOT NULL,
	"verification_method" "verification_method" DEFAULT 'manual' NOT NULL,
	"verified_by" uuid,
	"verified_at" timestamp,
	"notes" text,
	"metadata" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create doctor_profile_urls table
CREATE TABLE IF NOT EXISTS "doctor_profile_urls" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"doctor_id" uuid NOT NULL,
	"url_type" varchar(50) NOT NULL,
	"url" varchar(500) NOT NULL,
	"display_name" varchar(100),
	"is_verified" boolean DEFAULT false NOT NULL,
	"verified_by" uuid,
	"verified_at" timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"sort_order" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create credential_documents table (integrated with existing storage system)
CREATE TABLE IF NOT EXISTS "credential_documents" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"credential_id" uuid NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text,
	"file_name" varchar(255) NOT NULL,
	"original_file_name" varchar(255) NOT NULL,
	"file_path" varchar(500) NOT NULL,
	"file_size" integer NOT NULL,
	"mime_type" varchar(100) NOT NULL,
	"uploaded_by" uuid NOT NULL,
	"is_verified" boolean DEFAULT false NOT NULL,
	"verified_by" uuid,
	"verified_at" timestamp,
	"is_deleted" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "doctor_credentials" ADD CONSTRAINT "doctor_credentials_doctor_id_users_id_fk" FOREIGN KEY ("doctor_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_credentials" ADD CONSTRAINT "doctor_credentials_verified_by_users_id_fk" FOREIGN KEY ("verified_by") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_profile_urls" ADD CONSTRAINT "doctor_profile_urls_doctor_id_users_id_fk" FOREIGN KEY ("doctor_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_profile_urls" ADD CONSTRAINT "doctor_profile_urls_verified_by_users_id_fk" FOREIGN KEY ("verified_by") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "credential_documents" ADD CONSTRAINT "credential_documents_credential_id_doctor_credentials_id_fk" FOREIGN KEY ("credential_id") REFERENCES "doctor_credentials"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "credential_documents" ADD CONSTRAINT "credential_documents_uploaded_by_users_id_fk" FOREIGN KEY ("uploaded_by") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "credential_documents" ADD CONSTRAINT "credential_documents_verified_by_users_id_fk" FOREIGN KEY ("verified_by") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;