-- Add doctor credentials permissions
INSERT INTO permissions (name, resource, action, description, scope_filter) VALUES
('doctor_credentials:read', 'doctor_credentials', 'read', 'View doctor credentials and profile information', '{"scope": "own"}'),
('doctor_credentials:create', 'doctor_credentials', 'create', 'Create new doctor credentials and profile information', '{"scope": "own"}'),
('doctor_credentials:update', 'doctor_credentials', 'update', 'Update doctor credentials and profile information', '{"scope": "own"}'),
('doctor_credentials:delete', 'doctor_credentials', 'delete', 'Delete doctor credentials and profile information', '{"scope": "own"}'),
('doctor_credentials:manage', 'doctor_credentials', 'manage', 'Full management access to all doctor credentials (admin only)', '{"scope": "global"}');

-- Get role IDs and assign permissions
DO $$
DECLARE
    doctor_role_id UUID;
    admin_role_id UUID;
    perm_id UUID;
BEGIN
    -- Get doctor role ID
    SELECT id INTO doctor_role_id FROM roles WHERE name = 'doctor' LIMIT 1;
    
    -- Get admin role ID  
    SELECT id INTO admin_role_id FROM roles WHERE name = 'admin' LIMIT 1;
    
    -- Assign own-scope permissions to doctor role
    IF doctor_role_id IS NOT NULL THEN
        FOR perm_id IN
            SELECT id FROM permissions
            WHERE resource = 'doctor_credentials'
            AND scope_filter->>'scope' = 'own'
        LOOP
            INSERT INTO role_permissions (role_id, permission_id)
            SELECT doctor_role_id, perm_id
            WHERE NOT EXISTS (
                SELECT 1 FROM role_permissions
                WHERE role_id = doctor_role_id AND permission_id = perm_id
            );
        END LOOP;
    END IF;
    
    -- Assign all doctor credentials permissions to admin role
    IF admin_role_id IS NOT NULL THEN
        FOR perm_id IN
            SELECT id FROM permissions
            WHERE resource = 'doctor_credentials'
        LOOP
            INSERT INTO role_permissions (role_id, permission_id)
            SELECT admin_role_id, perm_id
            WHERE NOT EXISTS (
                SELECT 1 FROM role_permissions
                WHERE role_id = admin_role_id AND permission_id = perm_id
            );
        END LOOP;
    END IF;
END $$;