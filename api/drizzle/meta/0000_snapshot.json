{"id": "ab63fd9e-62d5-4ad1-bebc-0653eb9fc761", "prevId": "00000000-0000-0000-0000-000000000000", "version": "5", "dialect": "pg", "tables": {"crm_communication_plan_steps": {"name": "crm_communication_plan_steps", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "touchpoint_type", "primaryKey": false, "notNull": true, "default": "'email'"}, "day_offset": {"name": "day_offset", "type": "integer", "primaryKey": false, "notNull": true}, "template": {"name": "template", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_communication_plan_steps_plan_id_crm_communication_plans_id_fk": {"name": "crm_communication_plan_steps_plan_id_crm_communication_plans_id_fk", "tableFrom": "crm_communication_plan_steps", "tableTo": "crm_communication_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_communication_plans": {"name": "crm_communication_plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "target_audience": {"name": "target_audience", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_communication_plans_created_by_users_id_fk": {"name": "crm_communication_plans_created_by_users_id_fk", "tableFrom": "crm_communication_plans", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_contact_plan_assignments": {"name": "crm_contact_plan_assignments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "contact_id": {"name": "contact_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "progress": {"name": "progress", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "assigned_by": {"name": "assigned_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_contact_plan_assignments_contact_id_crm_contacts_id_fk": {"name": "crm_contact_plan_assignments_contact_id_crm_contacts_id_fk", "tableFrom": "crm_contact_plan_assignments", "tableTo": "crm_contacts", "columnsFrom": ["contact_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "crm_contact_plan_assignments_plan_id_crm_communication_plans_id_fk": {"name": "crm_contact_plan_assignments_plan_id_crm_communication_plans_id_fk", "tableFrom": "crm_contact_plan_assignments", "tableTo": "crm_communication_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "crm_contact_plan_assignments_assigned_by_users_id_fk": {"name": "crm_contact_plan_assignments_assigned_by_users_id_fk", "tableFrom": "crm_contact_plan_assignments", "tableTo": "users", "columnsFrom": ["assigned_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_contacts": {"name": "crm_contacts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "contact_status", "primaryKey": false, "notNull": true, "default": "'lead'"}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "assigned_to_id": {"name": "assigned_to_id", "type": "uuid", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "last_contacted_at": {"name": "last_contacted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_contacts_organization_id_crm_organizations_id_fk": {"name": "crm_contacts_organization_id_crm_organizations_id_fk", "tableFrom": "crm_contacts", "tableTo": "crm_organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_contacts_assigned_to_id_users_id_fk": {"name": "crm_contacts_assigned_to_id_users_id_fk", "tableFrom": "crm_contacts", "tableTo": "users", "columnsFrom": ["assigned_to_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_contacts_created_by_users_id_fk": {"name": "crm_contacts_created_by_users_id_fk", "tableFrom": "crm_contacts", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_organizations": {"name": "crm_organizations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "organization_type", "primaryKey": false, "notNull": true, "default": "'other'"}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "size": {"name": "size", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "specialties": {"name": "specialties", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_organizations_created_by_users_id_fk": {"name": "crm_organizations_created_by_users_id_fk", "tableFrom": "crm_organizations", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_referrals": {"name": "crm_referrals", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "contact_id": {"name": "contact_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "patient_id": {"name": "patient_id", "type": "uuid", "primaryKey": false, "notNull": false}, "case_id": {"name": "case_id", "type": "uuid", "primaryKey": false, "notNull": false}, "referral_date": {"name": "referral_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'new'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "integer", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_referrals_contact_id_crm_contacts_id_fk": {"name": "crm_referrals_contact_id_crm_contacts_id_fk", "tableFrom": "crm_referrals", "tableTo": "crm_contacts", "columnsFrom": ["contact_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_referrals_organization_id_crm_organizations_id_fk": {"name": "crm_referrals_organization_id_crm_organizations_id_fk", "tableFrom": "crm_referrals", "tableTo": "crm_organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_referrals_patient_id_users_id_fk": {"name": "crm_referrals_patient_id_users_id_fk", "tableFrom": "crm_referrals", "tableTo": "users", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_referrals_created_by_users_id_fk": {"name": "crm_referrals_created_by_users_id_fk", "tableFrom": "crm_referrals", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "crm_touchpoints": {"name": "crm_touchpoints", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "contact_id": {"name": "contact_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "touchpoint_type", "primaryKey": false, "notNull": true, "default": "'other'"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "outcome": {"name": "outcome", "type": "text", "primaryKey": false, "notNull": false}, "follow_up_required": {"name": "follow_up_required", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "follow_up_date": {"name": "follow_up_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"crm_touchpoints_contact_id_crm_contacts_id_fk": {"name": "crm_touchpoints_contact_id_crm_contacts_id_fk", "tableFrom": "crm_touchpoints", "tableTo": "crm_contacts", "columnsFrom": ["contact_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "crm_touchpoints_organization_id_crm_organizations_id_fk": {"name": "crm_touchpoints_organization_id_crm_organizations_id_fk", "tableFrom": "crm_touchpoints", "tableTo": "crm_organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "crm_touchpoints_created_by_users_id_fk": {"name": "crm_touchpoints_created_by_users_id_fk", "tableFrom": "crm_touchpoints", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {"contact_status": {"name": "contact_status", "values": {"lead": "lead", "prospect": "prospect", "customer": "customer", "inactive": "inactive"}}, "organization_type": {"name": "organization_type", "values": {"hospital": "hospital", "clinic": "clinic", "private_practice": "private_practice", "insurance": "insurance", "pharmacy": "pharmacy", "other": "other"}}, "touchpoint_type": {"name": "touchpoint_type", "values": {"email": "email", "call": "call", "meeting": "meeting", "referral": "referral", "social": "social", "other": "other"}}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}