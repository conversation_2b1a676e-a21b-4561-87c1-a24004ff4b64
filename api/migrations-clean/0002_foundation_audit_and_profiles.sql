-- Migration: 0002_foundation_audit_and_profiles
-- Description: Audit logs and user profiles system
-- Dependencies: 0001_foundation_users_and_auth.sql

-- Create audit logs table for HIPAA compliance
CREATE TABLE IF NOT EXISTS "audit_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid,
	"action" varchar(100) NOT NULL,
	"resource" varchar(100) NOT NULL,
	"resource_id" varchar(255),
	"ip_address" varchar(45),
	"user_agent" text,
	"metadata" text, -- JSON string for additional context
	"timestamp" timestamp DEFAULT now() NOT NULL
);

-- Create user profiles table for extended information
CREATE TABLE IF NOT EXISTS "user_profiles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"phone_number" varchar(20),
	"date_of_birth" timestamp,
	"gender" varchar(20),
	"address" text,
	"city" varchar(100),
	"state" varchar(100),
	"country" varchar(100),
	"zip_code" varchar(20),
	"emergency_contact_name" varchar(200),
	"emergency_contact_phone" varchar(20),
	"emergency_contact_relationship" varchar(100),
	"profile_picture_url" varchar(500),
	"bio" text,
	-- Preferences fields
	"notification_email" boolean DEFAULT true,
	"notification_sms" boolean DEFAULT false,
	"notification_push" boolean DEFAULT true,
	"privacy_share_data_for_research" boolean DEFAULT false,
	"privacy_allow_marketing_communications" boolean DEFAULT false,
	-- Medical information fields
	"blood_type" varchar(10),
	"insurance_provider" varchar(200),
	"insurance_policy_number" varchar(100),
	"allergies" text, -- JSON array stored as text
	"medications" text, -- JSON array stored as text
	"medical_conditions" text, -- JSON array stored as text
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_user_id_users_id_fk" 
   FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_user_id_users_id_fk" 
   FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_audit_logs_user" ON "audit_logs"("user_id");
CREATE INDEX IF NOT EXISTS "idx_audit_logs_timestamp" ON "audit_logs"("timestamp");
CREATE INDEX IF NOT EXISTS "idx_audit_logs_resource" ON "audit_logs"("resource");
CREATE INDEX IF NOT EXISTS "idx_audit_logs_action" ON "audit_logs"("action");
CREATE INDEX IF NOT EXISTS "idx_user_profiles_user" ON "user_profiles"("user_id");

-- Add unique constraint to ensure one profile per user
DO $$ BEGIN
 ALTER TABLE "user_profiles" ADD CONSTRAINT "user_profiles_user_id_unique" UNIQUE("user_id");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;