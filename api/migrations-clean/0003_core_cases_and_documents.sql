-- Migration: 0003_core_cases_and_documents
-- Description: Core cases and medical documents system
-- Dependencies: 0001_foundation_users_and_auth.sql, 0002_foundation_audit_and_profiles.sql

-- Create case-related enums
DO $$ BEGIN
 CREATE TYPE "case_status" AS ENUM('draft', 'submitted', 'in_review', 'assigned', 'completed', 'cancelled');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "urgency_level" AS ENUM('low', 'medium', 'high', 'urgent');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "document_type" AS ENUM('lab_report', 'imaging', 'prescription', 'medical_history', 'insurance', 'other');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "doctor_acceptance_status" AS ENUM('pending', 'accepted', 'declined', 'closed');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create cases table - clinical details now stored in case_notes table
CREATE TABLE IF NOT EXISTS "cases" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"patient_id" uuid NOT NULL,
	"assigned_doctor_id" uuid, -- Legacy field for backward compatibility
	"title" varchar(255) NOT NULL,
	"urgency_level" "urgency_level" DEFAULT 'medium' NOT NULL,
	"specialty_required" varchar(100),
	"status" "case_status" DEFAULT 'draft' NOT NULL,
	"submitted_at" timestamp,
	"assigned_at" timestamp,
	"completed_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create medical documents table
CREATE TABLE IF NOT EXISTS "medical_documents" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"case_id" uuid,
	"uploaded_by" uuid NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text,
	"document_type" "document_type" DEFAULT 'other' NOT NULL,
	"file_name" varchar(255) NOT NULL,
	"original_file_name" varchar(255) NOT NULL,
	"file_path" varchar(500) NOT NULL,
	"file_size" integer NOT NULL,
	"mime_type" varchar(100) NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create note types table for configurable note types
CREATE TABLE IF NOT EXISTS "note_types" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"key" varchar(50) NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" text,
	"icon" varchar(50) DEFAULT 'FileText',
	"color" varchar(50) DEFAULT 'blue',
	"category" varchar(50) DEFAULT 'clinical',
	"allowed_roles" jsonb DEFAULT '["doctor","admin"]' NOT NULL,
	"requires_doctor" boolean DEFAULT false,
	"requires_permission" varchar(100),
	"auto_save" boolean DEFAULT true,
	"auto_save_delay" integer DEFAULT 2000,
	"rich_text" boolean DEFAULT true,
	"show_doctor_info" boolean DEFAULT true,
	"show_in_sidebar" boolean DEFAULT false,
	"placeholder" text,
	"template" jsonb,
	"ai_enabled" boolean DEFAULT false,
	"ai_model" varchar(100),
	"ai_prompt" text,
	"sort_order" integer DEFAULT 0,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"created_by" uuid
);

-- Create case notes table for all clinical documentation
CREATE TABLE IF NOT EXISTS "case_notes" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"case_id" uuid NOT NULL,
	"doctor_id" uuid NOT NULL,
	"note_type_id" uuid NOT NULL,
	"structured_content" jsonb DEFAULT '{}' NOT NULL,
	"raw_content" text, -- Legacy field for backward compatibility
	"version" integer DEFAULT 1,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "cases" ADD CONSTRAINT "cases_patient_id_users_id_fk" 
   FOREIGN KEY ("patient_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "cases" ADD CONSTRAINT "cases_assigned_doctor_id_users_id_fk" 
   FOREIGN KEY ("assigned_doctor_id") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "medical_documents" ADD CONSTRAINT "medical_documents_case_id_cases_id_fk" 
   FOREIGN KEY ("case_id") REFERENCES "cases"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "medical_documents" ADD CONSTRAINT "medical_documents_uploaded_by_users_id_fk" 
   FOREIGN KEY ("uploaded_by") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "note_types" ADD CONSTRAINT "note_types_created_by_users_id_fk" 
   FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_notes" ADD CONSTRAINT "case_notes_case_id_cases_id_fk" 
   FOREIGN KEY ("case_id") REFERENCES "cases"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_notes" ADD CONSTRAINT "case_notes_doctor_id_users_id_fk" 
   FOREIGN KEY ("doctor_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_notes" ADD CONSTRAINT "case_notes_note_type_id_note_types_id_fk" 
   FOREIGN KEY ("note_type_id") REFERENCES "note_types"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_cases_patient" ON "cases"("patient_id");
CREATE INDEX IF NOT EXISTS "idx_cases_assigned_doctor" ON "cases"("assigned_doctor_id");
CREATE INDEX IF NOT EXISTS "idx_cases_status" ON "cases"("status");
CREATE INDEX IF NOT EXISTS "idx_cases_urgency" ON "cases"("urgency_level");
CREATE INDEX IF NOT EXISTS "idx_cases_created" ON "cases"("created_at");

CREATE INDEX IF NOT EXISTS "idx_medical_documents_case" ON "medical_documents"("case_id");
CREATE INDEX IF NOT EXISTS "idx_medical_documents_uploaded_by" ON "medical_documents"("uploaded_by");
CREATE INDEX IF NOT EXISTS "idx_medical_documents_type" ON "medical_documents"("document_type");
CREATE INDEX IF NOT EXISTS "idx_medical_documents_deleted" ON "medical_documents"("is_deleted");

CREATE INDEX IF NOT EXISTS "idx_note_types_key" ON "note_types"("key");
CREATE INDEX IF NOT EXISTS "idx_note_types_active" ON "note_types"("is_active");
CREATE INDEX IF NOT EXISTS "idx_note_types_category" ON "note_types"("category");

CREATE INDEX IF NOT EXISTS "idx_case_notes_case" ON "case_notes"("case_id");
CREATE INDEX IF NOT EXISTS "idx_case_notes_doctor" ON "case_notes"("doctor_id");
CREATE INDEX IF NOT EXISTS "idx_case_notes_type" ON "case_notes"("note_type_id");
CREATE INDEX IF NOT EXISTS "idx_case_notes_active" ON "case_notes"("is_active");

-- Add unique constraints
DO $$ BEGIN
 ALTER TABLE "note_types" ADD CONSTRAINT "note_types_key_unique" UNIQUE("key");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;