-- Migration: Add Medical Opinion note type
-- This adds a new note type specifically for comprehensive medical opinions that only doctors can edit

-- Insert the new medical_opinion note type
INSERT INTO note_types (
  key, 
  name, 
  description, 
  icon, 
  color, 
  category, 
  allowed_roles, 
  requires_doctor, 
  show_in_sidebar, 
  placeholder, 
  sort_order,
  is_active
) VALUES (
  'medical_opinion',
  'Medical Opinion',
  'Comprehensive medical opinion and diagnosis by attending physician',
  'Stethoscope',
  'red',
  'clinical',
  '["doctor", "admin"]'::jsonb,
  true,
  false,
  'Provide your comprehensive medical opinion including diagnosis, treatment recommendations, prognosis, and clinical assessment...',
  5,
  true
) ON CONFLICT (key) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  icon = EXCLUDED.icon,
  color = EXCLUDED.color,
  category = EXCLUDED.category,
  allowed_roles = EXCLUDED.allowed_roles,
  requires_doctor = EXCLUDED.requires_doctor,
  show_in_sidebar = EXCLUDED.show_in_sidebar,
  placeholder = EXCLUDED.placeholder,
  sort_order = EXCLUDED.sort_order,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();
