-- Migration: 0004_core_appointments_and_opinions
-- Description: Appointments and medical opinions system
-- Dependencies: 0003_core_cases_and_documents.sql

-- Create appointment-related enums
DO $$ BEGIN
 CREATE TYPE "appointment_status" AS ENUM('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "appointment_type" AS ENUM('consultation', 'follow_up', 'review', 'emergency');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create opinion-related enums
DO $$ BEGIN
 CREATE TYPE "opinion_status" AS ENUM('draft', 'submitted', 'reviewed', 'approved');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "confidence_level" AS ENUM('low', 'medium', 'high');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create appointments table
CREATE TABLE IF NOT EXISTS "appointments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"patient_id" uuid NOT NULL,
	"doctor_id" uuid NOT NULL,
	"case_id" uuid,
	"appointment_type" "appointment_type" DEFAULT 'consultation' NOT NULL,
	"scheduled_at" timestamp NOT NULL,
	"duration" integer DEFAULT 30 NOT NULL, -- Duration in minutes
	"status" "appointment_status" DEFAULT 'scheduled' NOT NULL,
	"notes" text,
	"meeting_link" varchar(500),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create medical opinions table
CREATE TABLE IF NOT EXISTS "medical_opinions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"case_id" uuid NOT NULL,
	"doctor_id" uuid NOT NULL,
	"diagnosis" text NOT NULL,
	"recommendations" text NOT NULL,
	"treatment_plan" text,
	"follow_up_instructions" text,
	"urgency_level" "urgency_level" DEFAULT 'medium' NOT NULL,
	"confidence_level" "confidence_level" DEFAULT 'medium' NOT NULL,
	"additional_tests" text,
	"referral_specialty" varchar(100),
	"notes" text,
	"status" "opinion_status" DEFAULT 'draft' NOT NULL,
	"submitted_at" timestamp,
	"approved_by" uuid,
	"approved_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "appointments" ADD CONSTRAINT "appointments_patient_id_users_id_fk" 
   FOREIGN KEY ("patient_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "appointments" ADD CONSTRAINT "appointments_doctor_id_users_id_fk" 
   FOREIGN KEY ("doctor_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "appointments" ADD CONSTRAINT "appointments_case_id_cases_id_fk" 
   FOREIGN KEY ("case_id") REFERENCES "cases"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "medical_opinions" ADD CONSTRAINT "medical_opinions_case_id_cases_id_fk" 
   FOREIGN KEY ("case_id") REFERENCES "cases"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "medical_opinions" ADD CONSTRAINT "medical_opinions_doctor_id_users_id_fk" 
   FOREIGN KEY ("doctor_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "medical_opinions" ADD CONSTRAINT "medical_opinions_approved_by_users_id_fk" 
   FOREIGN KEY ("approved_by") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_appointments_patient" ON "appointments"("patient_id");
CREATE INDEX IF NOT EXISTS "idx_appointments_doctor" ON "appointments"("doctor_id");
CREATE INDEX IF NOT EXISTS "idx_appointments_case" ON "appointments"("case_id");
CREATE INDEX IF NOT EXISTS "idx_appointments_status" ON "appointments"("status");
CREATE INDEX IF NOT EXISTS "idx_appointments_scheduled" ON "appointments"("scheduled_at");
CREATE INDEX IF NOT EXISTS "idx_appointments_type" ON "appointments"("appointment_type");

CREATE INDEX IF NOT EXISTS "idx_medical_opinions_case" ON "medical_opinions"("case_id");
CREATE INDEX IF NOT EXISTS "idx_medical_opinions_doctor" ON "medical_opinions"("doctor_id");
CREATE INDEX IF NOT EXISTS "idx_medical_opinions_status" ON "medical_opinions"("status");
CREATE INDEX IF NOT EXISTS "idx_medical_opinions_urgency" ON "medical_opinions"("urgency_level");
CREATE INDEX IF NOT EXISTS "idx_medical_opinions_confidence" ON "medical_opinions"("confidence_level");
CREATE INDEX IF NOT EXISTS "idx_medical_opinions_approved_by" ON "medical_opinions"("approved_by");