-- Migration: 0005_advanced_case_management
-- Description: Advanced case management - case doctors, discussions, and multi-doctor assignments
-- Dependencies: 0004_core_appointments_and_opinions.sql

-- Create doctor role enum for case assignments
DO $$ BEGIN
 CREATE TYPE "doctor_role" AS ENUM('primary', 'consulting', 'second_opinion', 'reviewing', 'collaborating');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create case doctors table for multi-doctor assignments
CREATE TABLE IF NOT EXISTS "case_doctors" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"case_id" uuid NOT NULL,
	"doctor_id" uuid NOT NULL,
	"assigned_by" uuid NOT NULL,
	"assigned_at" timestamp DEFAULT now() NOT NULL,
	"acceptance_status" "doctor_acceptance_status" DEFAULT 'pending' NOT NULL,
	"accepted_at" timestamp,
	"time_spent_minutes" integer DEFAULT 0 NOT NULL,
	"last_activity_at" timestamp,
	"notes" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create case discussions table for collaborative case communication
CREATE TABLE IF NOT EXISTS "case_discussions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"case_id" uuid NOT NULL,
	"author_id" uuid NOT NULL,
	"content" text NOT NULL,
	"has_attachments" boolean DEFAULT false NOT NULL,
	"is_read" boolean DEFAULT false NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	-- Patient visibility flag - determines if patients can see this message
	-- Patient messages are always visible to patients (true by default for patient authors)
	-- Doctor/admin messages can be marked as visible or hidden from patients
	"is_visible_to_patient" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create discussion attachments table to link discussions with documents
CREATE TABLE IF NOT EXISTS "discussion_attachments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"discussion_id" uuid NOT NULL,
	"document_id" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "case_doctors" ADD CONSTRAINT "case_doctors_case_id_cases_id_fk" 
   FOREIGN KEY ("case_id") REFERENCES "cases"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_doctors" ADD CONSTRAINT "case_doctors_doctor_id_users_id_fk" 
   FOREIGN KEY ("doctor_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_doctors" ADD CONSTRAINT "case_doctors_assigned_by_users_id_fk" 
   FOREIGN KEY ("assigned_by") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_discussions" ADD CONSTRAINT "case_discussions_case_id_cases_id_fk" 
   FOREIGN KEY ("case_id") REFERENCES "cases"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_discussions" ADD CONSTRAINT "case_discussions_author_id_users_id_fk" 
   FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "discussion_attachments" ADD CONSTRAINT "discussion_attachments_discussion_id_case_discussions_id_fk" 
   FOREIGN KEY ("discussion_id") REFERENCES "case_discussions"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "discussion_attachments" ADD CONSTRAINT "discussion_attachments_document_id_medical_documents_id_fk" 
   FOREIGN KEY ("document_id") REFERENCES "medical_documents"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_case_doctors_case" ON "case_doctors"("case_id");
CREATE INDEX IF NOT EXISTS "idx_case_doctors_doctor" ON "case_doctors"("doctor_id");
CREATE INDEX IF NOT EXISTS "idx_case_doctors_assigned_by" ON "case_doctors"("assigned_by");
CREATE INDEX IF NOT EXISTS "idx_case_doctors_status" ON "case_doctors"("acceptance_status");
CREATE INDEX IF NOT EXISTS "idx_case_doctors_active" ON "case_doctors"("is_active");
CREATE INDEX IF NOT EXISTS "idx_case_doctors_assigned_at" ON "case_doctors"("assigned_at");

CREATE INDEX IF NOT EXISTS "idx_case_discussions_case" ON "case_discussions"("case_id");
CREATE INDEX IF NOT EXISTS "idx_case_discussions_author" ON "case_discussions"("author_id");
CREATE INDEX IF NOT EXISTS "idx_case_discussions_created" ON "case_discussions"("created_at");
CREATE INDEX IF NOT EXISTS "idx_case_discussions_deleted" ON "case_discussions"("is_deleted");
CREATE INDEX IF NOT EXISTS "idx_case_discussions_patient_visible" ON "case_discussions"("is_visible_to_patient");

CREATE INDEX IF NOT EXISTS "idx_discussion_attachments_discussion" ON "discussion_attachments"("discussion_id");
CREATE INDEX IF NOT EXISTS "idx_discussion_attachments_document" ON "discussion_attachments"("document_id");

-- Add unique constraints to prevent duplicate assignments
DO $$ BEGIN
 ALTER TABLE "case_doctors" ADD CONSTRAINT "case_doctors_case_doctor_unique" UNIQUE("case_id", "doctor_id");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "discussion_attachments" ADD CONSTRAINT "discussion_attachments_discussion_document_unique" UNIQUE("discussion_id", "document_id");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;