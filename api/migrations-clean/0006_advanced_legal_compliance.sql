-- Migration: 0006_advanced_legal_compliance
-- Description: Legal compliance and consent management system
-- Dependencies: 0005_advanced_case_management.sql

-- Create legal compliance templates table
CREATE TABLE IF NOT EXISTS "legal_compliance_templates" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create legal compliance versions table
CREATE TABLE IF NOT EXISTS "legal_compliance_versions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"template_id" uuid NOT NULL,
	"version" integer NOT NULL,
	"content" text NOT NULL,
	"notes" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Create user legal agreements table
CREATE TABLE IF NOT EXISTS "user_legal_agreements" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"document_version_id" uuid,
	"document_version_id_uuid" uuid, -- Legacy field for backward compatibility
	"form_version_id" integer NOT NULL,
	"consented_at" timestamp DEFAULT now() NOT NULL,
	"ip_address" varchar(45),
	"user_agent" text,
	"metadata" text
);

-- Create consent assignment rules table
CREATE TABLE IF NOT EXISTS "consent_assignment_rules" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"template_id" uuid NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"trigger_type" varchar(50) NOT NULL,
	"conditions" jsonb,
	"is_required" boolean DEFAULT true NOT NULL,
	"is_recurring" boolean DEFAULT false NOT NULL,
	"priority" varchar(20) DEFAULT 'medium' NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create user consent requirements table
CREATE TABLE IF NOT EXISTS "user_consent_requirements" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"rule_id" uuid NOT NULL,
	"template_id" uuid NOT NULL,
	"status" varchar(20) DEFAULT 'pending' NOT NULL,
	"is_blocking" boolean DEFAULT true NOT NULL,
	"trigger_context" jsonb,
	"assigned_at" timestamp DEFAULT now() NOT NULL,
	"due_date" timestamp,
	"resolved_at" timestamp,
	"resolved_by" uuid,
	"agreement_id" uuid
);

-- Create case consent requirements table
CREATE TABLE IF NOT EXISTS "case_consent_requirements" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"case_id" uuid NOT NULL,
	"rule_id" uuid NOT NULL,
	"template_id" uuid NOT NULL,
	"required_from_user_id" uuid,
	"required_from_role" varchar(50),
	"status" varchar(20) DEFAULT 'pending' NOT NULL,
	"is_blocking" boolean DEFAULT true NOT NULL,
	"assigned_at" timestamp DEFAULT now() NOT NULL,
	"due_date" timestamp,
	"resolved_at" timestamp,
	"agreement_id" uuid
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "legal_compliance_templates" ADD CONSTRAINT "legal_compliance_templates_created_by_users_id_fk" 
   FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "legal_compliance_versions" ADD CONSTRAINT "legal_compliance_versions_template_id_legal_compliance_templates_id_fk" 
   FOREIGN KEY ("template_id") REFERENCES "legal_compliance_templates"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "legal_compliance_versions" ADD CONSTRAINT "legal_compliance_versions_created_by_users_id_fk" 
   FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_legal_agreements" ADD CONSTRAINT "user_legal_agreements_user_id_users_id_fk" 
   FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_legal_agreements" ADD CONSTRAINT "user_legal_agreements_document_version_id_legal_compliance_versions_id_fk" 
   FOREIGN KEY ("document_version_id") REFERENCES "legal_compliance_versions"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "consent_assignment_rules" ADD CONSTRAINT "consent_assignment_rules_template_id_legal_compliance_templates_id_fk" 
   FOREIGN KEY ("template_id") REFERENCES "legal_compliance_templates"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "consent_assignment_rules" ADD CONSTRAINT "consent_assignment_rules_created_by_users_id_fk" 
   FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_consent_requirements" ADD CONSTRAINT "user_consent_requirements_user_id_users_id_fk" 
   FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_consent_requirements" ADD CONSTRAINT "user_consent_requirements_rule_id_consent_assignment_rules_id_fk" 
   FOREIGN KEY ("rule_id") REFERENCES "consent_assignment_rules"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_consent_requirements" ADD CONSTRAINT "user_consent_requirements_template_id_legal_compliance_templates_id_fk" 
   FOREIGN KEY ("template_id") REFERENCES "legal_compliance_templates"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_consent_requirements" ADD CONSTRAINT "user_consent_requirements_resolved_by_users_id_fk" 
   FOREIGN KEY ("resolved_by") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_consent_requirements" ADD CONSTRAINT "case_consent_requirements_case_id_cases_id_fk" 
   FOREIGN KEY ("case_id") REFERENCES "cases"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_consent_requirements" ADD CONSTRAINT "case_consent_requirements_rule_id_consent_assignment_rules_id_fk" 
   FOREIGN KEY ("rule_id") REFERENCES "consent_assignment_rules"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_consent_requirements" ADD CONSTRAINT "case_consent_requirements_template_id_legal_compliance_templates_id_fk" 
   FOREIGN KEY ("template_id") REFERENCES "legal_compliance_templates"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "case_consent_requirements" ADD CONSTRAINT "case_consent_requirements_required_from_user_id_users_id_fk" 
   FOREIGN KEY ("required_from_user_id") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_legal_compliance_templates_active" ON "legal_compliance_templates"("is_active");
CREATE INDEX IF NOT EXISTS "idx_legal_compliance_templates_created_by" ON "legal_compliance_templates"("created_by");

CREATE INDEX IF NOT EXISTS "idx_legal_compliance_versions_template" ON "legal_compliance_versions"("template_id");
CREATE INDEX IF NOT EXISTS "idx_legal_compliance_versions_active" ON "legal_compliance_versions"("is_active");
CREATE INDEX IF NOT EXISTS "idx_legal_compliance_versions_version" ON "legal_compliance_versions"("template_id", "version");

CREATE INDEX IF NOT EXISTS "idx_user_legal_agreements_user" ON "user_legal_agreements"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_legal_agreements_document_version" ON "user_legal_agreements"("document_version_id");
CREATE INDEX IF NOT EXISTS "idx_user_legal_agreements_consented_at" ON "user_legal_agreements"("consented_at");

CREATE INDEX IF NOT EXISTS "idx_consent_assignment_rules_template" ON "consent_assignment_rules"("template_id");
CREATE INDEX IF NOT EXISTS "idx_consent_assignment_rules_trigger" ON "consent_assignment_rules"("trigger_type");
CREATE INDEX IF NOT EXISTS "idx_consent_assignment_rules_active" ON "consent_assignment_rules"("is_active");

CREATE INDEX IF NOT EXISTS "idx_user_consent_requirements_user" ON "user_consent_requirements"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_consent_requirements_rule" ON "user_consent_requirements"("rule_id");
CREATE INDEX IF NOT EXISTS "idx_user_consent_requirements_status" ON "user_consent_requirements"("status");
CREATE INDEX IF NOT EXISTS "idx_user_consent_requirements_blocking" ON "user_consent_requirements"("is_blocking");

CREATE INDEX IF NOT EXISTS "idx_case_consent_requirements_case" ON "case_consent_requirements"("case_id");
CREATE INDEX IF NOT EXISTS "idx_case_consent_requirements_rule" ON "case_consent_requirements"("rule_id");
CREATE INDEX IF NOT EXISTS "idx_case_consent_requirements_status" ON "case_consent_requirements"("status");
CREATE INDEX IF NOT EXISTS "idx_case_consent_requirements_blocking" ON "case_consent_requirements"("is_blocking");

-- Add unique constraints for template versions
DO $$ BEGIN
 ALTER TABLE "legal_compliance_versions" ADD CONSTRAINT "legal_compliance_versions_template_version_unique" UNIQUE("template_id", "version");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;