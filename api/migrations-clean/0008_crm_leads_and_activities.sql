-- Migration: 0008_crm_leads_and_activities
-- Description: CRM leads, activities, and advanced CRM features
-- Dependencies: 0007_crm_organizations_and_contacts.sql

-- Create CRM lead-related enums
DO $$ BEGIN
 CREATE TYPE "lead_type" AS ENUM('Hospital', 'Clinic', 'Doctor', 'Referral', 'Other');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "lead_source" AS ENUM('Inbound', 'Outbound', 'Referral', 'Campaign', 'Other');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "lead_status" AS ENUM('New', 'Contacted', 'Qualified', 'Engaged', 'ProposalSent', 'Signed', 'Onboarded', 'ClosedLost');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "lead_stage" AS ENUM('Lead', 'Deal', 'ClosedWon', 'ClosedLost');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "owner_type" AS ENUM('User', 'Team');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "action_status" AS ENUM('Pending', 'Completed', 'Blocked');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "activity_type" AS ENUM('Call', 'Email', 'Meeting', 'InternalNote', 'Other');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "document_status" AS ENUM('Draft', 'Sent', 'Signed');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create CRM teams table
CREATE TABLE IF NOT EXISTS "crm_teams" (
	"team_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create CRM leads table
CREATE TABLE IF NOT EXISTS "crm_leads" (
	"lead_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"type" "lead_type" NOT NULL,
	"source" "lead_source" NOT NULL,
	"status" "lead_status" DEFAULT 'New' NOT NULL,
	"stage" "lead_stage" DEFAULT 'Lead' NOT NULL,
	"assigned_to_id" uuid,
	"assigned_to_type" "owner_type",
	"org_affiliation" varchar(255),
	"notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create CRM lead actions table
CREATE TABLE IF NOT EXISTS "crm_lead_actions" (
	"action_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"lead_id" uuid NOT NULL,
	"title" varchar(255) NOT NULL,
	"sequence" varchar(50),
	"status" "action_status" DEFAULT 'Pending' NOT NULL,
	"due_date" timestamp,
	"owner_id" uuid,
	"owner_type" "owner_type",
	"created_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp
);

-- Create CRM activity log table
CREATE TABLE IF NOT EXISTS "crm_activity_log" (
	"activity_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"lead_id" uuid NOT NULL,
	"type" "activity_type" NOT NULL,
	"date" timestamp NOT NULL,
	"summary" text,
	"created_by" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Create CRM documents table
CREATE TABLE IF NOT EXISTS "crm_documents" (
	"document_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"lead_id" uuid NOT NULL,
	"type" "document_type" NOT NULL,
	"status" "document_status" DEFAULT 'Draft' NOT NULL,
	"url" varchar(500) NOT NULL,
	"uploaded_by" uuid NOT NULL,
	"uploaded_at" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "crm_leads" ADD CONSTRAINT "crm_leads_assigned_to_id_users_id_fk" 
   FOREIGN KEY ("assigned_to_id") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_lead_actions" ADD CONSTRAINT "crm_lead_actions_lead_id_crm_leads_lead_id_fk" 
   FOREIGN KEY ("lead_id") REFERENCES "crm_leads"("lead_id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_lead_actions" ADD CONSTRAINT "crm_lead_actions_owner_id_users_id_fk" 
   FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_activity_log" ADD CONSTRAINT "crm_activity_log_lead_id_crm_leads_lead_id_fk" 
   FOREIGN KEY ("lead_id") REFERENCES "crm_leads"("lead_id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_activity_log" ADD CONSTRAINT "crm_activity_log_created_by_users_id_fk" 
   FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_documents" ADD CONSTRAINT "crm_documents_lead_id_crm_leads_lead_id_fk" 
   FOREIGN KEY ("lead_id") REFERENCES "crm_leads"("lead_id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_documents" ADD CONSTRAINT "crm_documents_uploaded_by_users_id_fk" 
   FOREIGN KEY ("uploaded_by") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_crm_teams_name" ON "crm_teams"("name");

CREATE INDEX IF NOT EXISTS "idx_crm_leads_type" ON "crm_leads"("type");
CREATE INDEX IF NOT EXISTS "idx_crm_leads_source" ON "crm_leads"("source");
CREATE INDEX IF NOT EXISTS "idx_crm_leads_status" ON "crm_leads"("status");
CREATE INDEX IF NOT EXISTS "idx_crm_leads_stage" ON "crm_leads"("stage");
CREATE INDEX IF NOT EXISTS "idx_crm_leads_assigned_to" ON "crm_leads"("assigned_to_id");
CREATE INDEX IF NOT EXISTS "idx_crm_leads_name" ON "crm_leads"("name");

CREATE INDEX IF NOT EXISTS "idx_crm_lead_actions_lead" ON "crm_lead_actions"("lead_id");
CREATE INDEX IF NOT EXISTS "idx_crm_lead_actions_status" ON "crm_lead_actions"("status");
CREATE INDEX IF NOT EXISTS "idx_crm_lead_actions_owner" ON "crm_lead_actions"("owner_id");
CREATE INDEX IF NOT EXISTS "idx_crm_lead_actions_due_date" ON "crm_lead_actions"("due_date");

CREATE INDEX IF NOT EXISTS "idx_crm_activity_log_lead" ON "crm_activity_log"("lead_id");
CREATE INDEX IF NOT EXISTS "idx_crm_activity_log_type" ON "crm_activity_log"("type");
CREATE INDEX IF NOT EXISTS "idx_crm_activity_log_date" ON "crm_activity_log"("date");
CREATE INDEX IF NOT EXISTS "idx_crm_activity_log_created_by" ON "crm_activity_log"("created_by");

CREATE INDEX IF NOT EXISTS "idx_crm_documents_lead" ON "crm_documents"("lead_id");
CREATE INDEX IF NOT EXISTS "idx_crm_documents_type" ON "crm_documents"("type");
CREATE INDEX IF NOT EXISTS "idx_crm_documents_status" ON "crm_documents"("status");
CREATE INDEX IF NOT EXISTS "idx_crm_documents_uploaded_by" ON "crm_documents"("uploaded_by");