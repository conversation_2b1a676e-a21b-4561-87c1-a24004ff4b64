-- Migration: 0009_specializations_system
-- Description: Doctor specializations and categories system
-- Dependencies: 0008_crm_leads_and_activities.sql

-- Create specialization categories table
CREATE TABLE IF NOT EXISTS "specialization_categories" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" text,
	"parent_category_id" uuid,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);

-- Create doctor specializations table
CREATE TABLE IF NOT EXISTS "doctor_specializations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(150) NOT NULL,
	"code" varchar(20),
	"description" text,
	"category_id" uuid,
	"requires_board_certification" boolean DEFAULT false,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);

-- Create doctor specialization assignments table
CREATE TABLE IF NOT EXISTS "doctor_specialization_assignments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"doctor_id" uuid NOT NULL,
	"specialization_id" uuid NOT NULL,
	"is_primary" boolean DEFAULT false,
	"board_certified" boolean DEFAULT false,
	"certification_date" date,
	"certification_body" varchar(200),
	"years_experience" integer DEFAULT 0,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "specialization_categories" ADD CONSTRAINT "specialization_categories_parent_category_id_specialization_categories_id_fk" 
   FOREIGN KEY ("parent_category_id") REFERENCES "specialization_categories"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_specializations" ADD CONSTRAINT "doctor_specializations_category_id_specialization_categories_id_fk" 
   FOREIGN KEY ("category_id") REFERENCES "specialization_categories"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_specialization_assignments" ADD CONSTRAINT "doctor_specialization_assignments_doctor_id_users_id_fk" 
   FOREIGN KEY ("doctor_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_specialization_assignments" ADD CONSTRAINT "doctor_specialization_assignments_specialization_id_doctor_specializations_id_fk" 
   FOREIGN KEY ("specialization_id") REFERENCES "doctor_specializations"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_specialization_categories_parent" ON "specialization_categories"("parent_category_id");
CREATE INDEX IF NOT EXISTS "idx_specialization_categories_active" ON "specialization_categories"("is_active");
CREATE INDEX IF NOT EXISTS "idx_specialization_categories_name" ON "specialization_categories"("name");

CREATE INDEX IF NOT EXISTS "idx_doctor_specializations_category" ON "doctor_specializations"("category_id");
CREATE INDEX IF NOT EXISTS "idx_doctor_specializations_active" ON "doctor_specializations"("is_active");
CREATE INDEX IF NOT EXISTS "idx_doctor_specializations_name" ON "doctor_specializations"("name");
CREATE INDEX IF NOT EXISTS "idx_doctor_specializations_code" ON "doctor_specializations"("code");

CREATE INDEX IF NOT EXISTS "idx_doctor_specialization_assignments_doctor" ON "doctor_specialization_assignments"("doctor_id");
CREATE INDEX IF NOT EXISTS "idx_doctor_specialization_assignments_specialization" ON "doctor_specialization_assignments"("specialization_id");
CREATE INDEX IF NOT EXISTS "idx_doctor_specialization_assignments_primary" ON "doctor_specialization_assignments"("doctor_id", "is_primary");
CREATE INDEX IF NOT EXISTS "idx_doctor_specialization_assignments_active" ON "doctor_specialization_assignments"("is_active");

-- Add unique constraints
DO $$ BEGIN
 ALTER TABLE "specialization_categories" ADD CONSTRAINT "specialization_categories_name_unique" UNIQUE("name");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_specializations" ADD CONSTRAINT "doctor_specializations_name_unique" UNIQUE("name");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_specializations" ADD CONSTRAINT "doctor_specializations_code_unique" UNIQUE("code");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_specialization_assignments" ADD CONSTRAINT "doctor_specialization_assignments_doctor_specialization_unique" UNIQUE("doctor_id", "specialization_id");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Insert some default specialization categories
INSERT INTO "specialization_categories" ("name", "description", "is_active") VALUES
('Primary Care', 'General medical care and preventive services', true),
('Internal Medicine', 'Diagnosis and treatment of adult diseases', true),
('Surgery', 'Surgical specialties and procedures', true),
('Pediatrics', 'Medical care for infants, children, and adolescents', true),
('Obstetrics & Gynecology', 'Women''s reproductive health and childbirth', true),
('Psychiatry', 'Mental health and behavioral disorders', true),
('Radiology', 'Medical imaging and diagnostic procedures', true),
('Pathology', 'Laboratory medicine and disease diagnosis', true),
('Emergency Medicine', 'Acute care and emergency treatment', true),
('Anesthesiology', 'Perioperative care and pain management', true)
ON CONFLICT (name) DO NOTHING;

-- Insert some common specializations
INSERT INTO "doctor_specializations" ("name", "code", "description", "category_id", "requires_board_certification", "is_active") VALUES
('Family Medicine', 'FM', 'Comprehensive primary care for all ages', (SELECT id FROM specialization_categories WHERE name = 'Primary Care'), true, true),
('Internal Medicine', 'IM', 'Adult internal medicine', (SELECT id FROM specialization_categories WHERE name = 'Internal Medicine'), true, true),
('Cardiology', 'CARD', 'Heart and cardiovascular system', (SELECT id FROM specialization_categories WHERE name = 'Internal Medicine'), true, true),
('Dermatology', 'DERM', 'Skin, hair, and nail conditions', (SELECT id FROM specialization_categories WHERE name = 'Internal Medicine'), true, true),
('General Surgery', 'GS', 'General surgical procedures', (SELECT id FROM specialization_categories WHERE name = 'Surgery'), true, true),
('Orthopedic Surgery', 'ORTHO', 'Musculoskeletal system surgery', (SELECT id FROM specialization_categories WHERE name = 'Surgery'), true, true),
('Pediatrics', 'PEDS', 'Medical care for children', (SELECT id FROM specialization_categories WHERE name = 'Pediatrics'), true, true),
('Obstetrics & Gynecology', 'OBGYN', 'Women''s health and childbirth', (SELECT id FROM specialization_categories WHERE name = 'Obstetrics & Gynecology'), true, true),
('Psychiatry', 'PSYCH', 'Mental health and psychiatric disorders', (SELECT id FROM specialization_categories WHERE name = 'Psychiatry'), true, true),
('Emergency Medicine', 'EM', 'Emergency and acute care', (SELECT id FROM specialization_categories WHERE name = 'Emergency Medicine'), true, true)
ON CONFLICT (name) DO NOTHING;