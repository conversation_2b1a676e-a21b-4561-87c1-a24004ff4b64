-- Migration: 0010_credentials_system
-- Description: Doctor credentials and verification system
-- Dependencies: 0009_specializations_system.sql

-- Create credential-related enums
DO $$ BEGIN
 CREATE TYPE "credential_type" AS ENUM('medical_license', 'board_certification', 'dea_registration', 'npi_number', 'hospital_privileges', 'malpractice_insurance', 'continuing_education', 'other');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "credential_status" AS ENUM('pending', 'verified', 'expired', 'revoked', 'under_review');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "verification_method" AS ENUM('manual', 'automated', 'third_party', 'document_upload');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create doctor credentials table
CREATE TABLE IF NOT EXISTS "doctor_credentials" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"doctor_id" uuid NOT NULL,
	"credential_type" "credential_type" NOT NULL,
	"credential_number" varchar(100) NOT NULL,
	"issuing_authority" varchar(255) NOT NULL,
	"issued_date" timestamp NOT NULL,
	"expiration_date" timestamp,
	"status" "credential_status" DEFAULT 'pending' NOT NULL,
	"verification_method" "verification_method" DEFAULT 'manual' NOT NULL,
	"verified_by" uuid,
	"verified_at" timestamp,
	"notes" text,
	"metadata" text, -- JSON string for additional data
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create credential documents table
CREATE TABLE IF NOT EXISTS "credential_documents" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"credential_id" uuid NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text,
	"file_name" varchar(255) NOT NULL,
	"original_file_name" varchar(255) NOT NULL,
	"file_path" varchar(500) NOT NULL,
	"file_size" integer NOT NULL,
	"mime_type" varchar(100) NOT NULL,
	"uploaded_by" uuid NOT NULL,
	"is_verified" boolean DEFAULT false NOT NULL,
	"verified_by" uuid,
	"verified_at" timestamp,
	"is_deleted" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create doctor profile URLs table
CREATE TABLE IF NOT EXISTS "doctor_profile_urls" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"doctor_id" uuid NOT NULL,
	"url_type" varchar(50) NOT NULL, -- e.g., 'linkedin', 'hospital_profile', 'practice_website'
	"url" varchar(500) NOT NULL,
	"display_name" varchar(100),
	"is_verified" boolean DEFAULT false NOT NULL,
	"verified_by" uuid,
	"verified_at" timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"sort_order" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "doctor_credentials" ADD CONSTRAINT "doctor_credentials_doctor_id_users_id_fk" 
   FOREIGN KEY ("doctor_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_credentials" ADD CONSTRAINT "doctor_credentials_verified_by_users_id_fk" 
   FOREIGN KEY ("verified_by") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "credential_documents" ADD CONSTRAINT "credential_documents_credential_id_doctor_credentials_id_fk" 
   FOREIGN KEY ("credential_id") REFERENCES "doctor_credentials"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "credential_documents" ADD CONSTRAINT "credential_documents_uploaded_by_users_id_fk" 
   FOREIGN KEY ("uploaded_by") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "credential_documents" ADD CONSTRAINT "credential_documents_verified_by_users_id_fk" 
   FOREIGN KEY ("verified_by") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_profile_urls" ADD CONSTRAINT "doctor_profile_urls_doctor_id_users_id_fk" 
   FOREIGN KEY ("doctor_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "doctor_profile_urls" ADD CONSTRAINT "doctor_profile_urls_verified_by_users_id_fk" 
   FOREIGN KEY ("verified_by") REFERENCES "users"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_doctor_credentials_doctor" ON "doctor_credentials"("doctor_id");
CREATE INDEX IF NOT EXISTS "idx_doctor_credentials_type" ON "doctor_credentials"("credential_type");
CREATE INDEX IF NOT EXISTS "idx_doctor_credentials_status" ON "doctor_credentials"("status");
CREATE INDEX IF NOT EXISTS "idx_doctor_credentials_expiration" ON "doctor_credentials"("expiration_date");
CREATE INDEX IF NOT EXISTS "idx_doctor_credentials_verified_by" ON "doctor_credentials"("verified_by");
CREATE INDEX IF NOT EXISTS "idx_doctor_credentials_active" ON "doctor_credentials"("is_active");

CREATE INDEX IF NOT EXISTS "idx_credential_documents_credential" ON "credential_documents"("credential_id");
CREATE INDEX IF NOT EXISTS "idx_credential_documents_uploaded_by" ON "credential_documents"("uploaded_by");
CREATE INDEX IF NOT EXISTS "idx_credential_documents_verified" ON "credential_documents"("is_verified");
CREATE INDEX IF NOT EXISTS "idx_credential_documents_deleted" ON "credential_documents"("is_deleted");

CREATE INDEX IF NOT EXISTS "idx_doctor_profile_urls_doctor" ON "doctor_profile_urls"("doctor_id");
CREATE INDEX IF NOT EXISTS "idx_doctor_profile_urls_type" ON "doctor_profile_urls"("url_type");
CREATE INDEX IF NOT EXISTS "idx_doctor_profile_urls_verified" ON "doctor_profile_urls"("is_verified");
CREATE INDEX IF NOT EXISTS "idx_doctor_profile_urls_active" ON "doctor_profile_urls"("is_active");
CREATE INDEX IF NOT EXISTS "idx_doctor_profile_urls_sort" ON "doctor_profile_urls"("doctor_id", "sort_order");

-- Add unique constraints
DO $$ BEGIN
 ALTER TABLE "doctor_credentials" ADD CONSTRAINT "doctor_credentials_doctor_type_number_unique" UNIQUE("doctor_id", "credential_type", "credential_number");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create a view for active credentials with expiration warnings
CREATE OR REPLACE VIEW "active_credentials_with_expiration" AS
SELECT 
    dc.*,
    u.first_name,
    u.last_name,
    u.email,
    CASE 
        WHEN dc.expiration_date IS NULL THEN 'no_expiration'
        WHEN dc.expiration_date < NOW() THEN 'expired'
        WHEN dc.expiration_date < NOW() + INTERVAL '30 days' THEN 'expiring_soon'
        WHEN dc.expiration_date < NOW() + INTERVAL '90 days' THEN 'expiring_in_90_days'
        ELSE 'valid'
    END as expiration_status,
    CASE 
        WHEN dc.expiration_date IS NOT NULL THEN 
            EXTRACT(days FROM dc.expiration_date - NOW())
        ELSE NULL
    END as days_until_expiration
FROM doctor_credentials dc
JOIN users u ON dc.doctor_id = u.id
WHERE dc.is_active = true;

-- Create a function to automatically update credential status based on expiration
CREATE OR REPLACE FUNCTION update_expired_credentials()
RETURNS void AS $$
BEGIN
    UPDATE doctor_credentials 
    SET status = 'expired', updated_at = NOW()
    WHERE expiration_date < NOW() 
    AND status != 'expired' 
    AND is_active = true;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to relevant tables
DO $$ BEGIN
    DROP TRIGGER IF EXISTS update_doctor_credentials_updated_at ON doctor_credentials;
    CREATE TRIGGER update_doctor_credentials_updated_at
        BEFORE UPDATE ON doctor_credentials
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EXCEPTION
    WHEN others THEN null;
END $$;

DO $$ BEGIN
    DROP TRIGGER IF EXISTS update_credential_documents_updated_at ON credential_documents;
    CREATE TRIGGER update_credential_documents_updated_at
        BEFORE UPDATE ON credential_documents
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EXCEPTION
    WHEN others THEN null;
END $$;

DO $$ BEGIN
    DROP TRIGGER IF EXISTS update_doctor_profile_urls_updated_at ON doctor_profile_urls;
    CREATE TRIGGER update_doctor_profile_urls_updated_at
        BEFORE UPDATE ON doctor_profile_urls
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EXCEPTION
    WHEN others THEN null;
END $$;