-- Migration: Initial OPAL Roles and Permissions
-- Description: Creates the basic roles and permissions for the healthcare platform
-- Dependencies: 0001_foundation_users_and_permissions.sql

-- Insert basic roles
INSERT INTO roles (id, name, display_name, description, is_active) VALUES
  ('550e8400-e29b-41d4-a716-************', 'patient', 'Patient', 'Healthcare patients seeking medical opinions', true),
  ('550e8400-e29b-41d4-a716-************', 'doctor', 'Doctor', 'Healthcare providers giving medical opinions', true),
  ('550e8400-e29b-41d4-a716-446655440003', 'agent', 'Agent', 'Customer service agents managing patient interactions', true),
  ('550e8400-e29b-41d4-a716-446655440004', 'admin', 'Administrator', 'System administrators with full access', true);

-- Insert basic permissions for cases
INSERT INTO permissions (id, name, display_name, description, resource, action, scope, filter_conditions) VALUES
  -- Cases permissions
  ('650e8400-e29b-41d4-a716-************', 'cases:read', 'Read Cases', 'View medical cases', 'cases', 'read', 'own', '{"patientId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-************', 'cases:write', 'Write Cases', 'Create and edit medical cases', 'cases', 'write', 'own', '{"patientId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440003', 'cases:read:assigned', 'Read Assigned Cases', 'View assigned medical cases', 'cases', 'read', 'assigned', null),
  ('650e8400-e29b-41d4-a716-446655440004', 'cases:write:assigned', 'Write Assigned Cases', 'Edit assigned medical cases', 'cases', 'write', 'assigned', null),
  ('650e8400-e29b-41d4-a716-446655440005', 'cases:read:all', 'Read All Cases', 'View all medical cases', 'cases', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-446655440006', 'cases:write:all', 'Write All Cases', 'Create and edit all medical cases', 'cases', 'write', 'global', null),
  
  -- Documents permissions
  ('650e8400-e29b-41d4-a716-************', 'documents:read', 'Read Documents', 'View medical documents', 'documents', 'read', 'own', '{"uploadedBy": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440008', 'documents:write', 'Write Documents', 'Upload and edit medical documents', 'documents', 'write', 'own', '{"uploadedBy": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440009', 'documents:delete', 'Delete Documents', 'Delete medical documents', 'documents', 'delete', 'own', '{"uploadedBy": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-************', 'documents:read:all', 'Read All Documents', 'View all medical documents', 'documents', 'read', 'global', null),
  
  -- Users permissions
  ('650e8400-e29b-41d4-a716-************', 'users:read', 'Read Users', 'View user profiles', 'users', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'users:write', 'Write Users', 'Create and edit user accounts', 'users', 'write', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'users:delete', 'Delete Users', 'Delete user accounts', 'users', 'delete', 'global', null),
  
  -- Appointments permissions
  ('650e8400-e29b-41d4-a716-************', 'appointments:read', 'Read Appointments', 'View appointments', 'appointments', 'read', 'own', null),
  ('650e8400-e29b-41d4-a716-************', 'appointments:write', 'Write Appointments', 'Create and edit appointments', 'appointments', 'write', 'own', null),
  ('650e8400-e29b-41d4-a716-************', 'appointments:read:all', 'Read All Appointments', 'View all appointments', 'appointments', 'read', 'global', null);

-- Assign permissions to roles
INSERT INTO role_permissions (role_id, permission_id) VALUES
  -- Patient permissions
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-************'), -- cases:read
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-************'), -- cases:write
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-************'), -- documents:read
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-446655440008'), -- documents:write
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-446655440009'), -- documents:delete
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-************'), -- appointments:read
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-************'), -- appointments:write
  
  -- Doctor permissions
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-446655440003'), -- cases:read:assigned
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-446655440004'), -- cases:write:assigned
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-************'), -- documents:read
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-446655440008'), -- documents:write
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-************'), -- appointments:read
  ('550e8400-e29b-41d4-a716-************', '650e8400-e29b-41d4-a716-************'), -- appointments:write
  
  -- Agent permissions
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440005'), -- cases:read:all
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440006'), -- cases:write:all
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-************'), -- documents:read:all
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-************'), -- users:read
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-************'), -- appointments:read:all
  
  -- Admin permissions (all permissions)
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- cases:read
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- cases:write
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440003'), -- cases:read:assigned
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440004'), -- cases:write:assigned
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440005'), -- cases:read:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440006'), -- cases:write:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- documents:read
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440008'), -- documents:write
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440009'), -- documents:delete
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- documents:read:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- users:read
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- users:write
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- users:delete
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- appointments:read
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- appointments:write
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'); -- appointments:read:all

-- Assign roles to existing users based on their current role field
INSERT INTO user_roles (user_id, role_id, assigned_by, assigned_at)
SELECT 
  u.id as user_id,
  r.id as role_id,
  u.id as assigned_by, -- Self-assigned during migration
  NOW() as assigned_at
FROM users u
CROSS JOIN roles r
WHERE u.role = r.name
  AND u.is_active = true
  AND r.is_active = true;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action);