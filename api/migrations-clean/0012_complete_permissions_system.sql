-- Migration: Complete Permissions System
-- Description: Comprehensive permissions system with all policies from PolicyEngine
-- Dependencies: 0011_initial_roles_and_permissions.sql

-- Clear existing permissions to rebuild completely
DELETE FROM role_permissions;
DELETE FROM permissions;

-- Insert comprehensive permissions for all resources and actions
INSERT INTO permissions (id, name, display_name, description, resource, action, scope, filter_conditions) VALUES
  -- Cases permissions (comprehensive)
  ('650e8400-e29b-41d4-a716-446655440001', 'cases:read', 'Read Cases', 'View medical cases', 'cases', 'read', 'own', '{"patientId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440002', 'cases:write', 'Write Cases', 'Create and edit medical cases', 'cases', 'write', 'own', '{"patientId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440003', 'cases:read:assigned', 'Read Assigned Cases', 'View assigned medical cases', 'cases', 'read', 'assigned', '{"assignedDoctor": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440004', 'cases:write:assigned', 'Write Assigned Cases', 'Edit assigned medical cases', 'cases', 'write', 'assigned', '{"assignedDoctor": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440005', 'cases:read:all', 'Read All Cases', 'View all medical cases', 'cases', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-446655440006', 'cases:write:all', 'Write All Cases', 'Create and edit all medical cases', 'cases', 'write', 'global', null),
  ('650e8400-e29b-41d4-a716-446655440007', 'cases:delete:all', 'Delete All Cases', 'Delete medical cases', 'cases', 'delete', 'global', null),
  
  -- Documents permissions (comprehensive)
  ('650e8400-e29b-41d4-a716-446655440010', 'documents:read', 'Read Documents', 'View medical documents', 'documents', 'read', 'own', '{"patientId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440011', 'documents:write', 'Write Documents', 'Upload and edit medical documents', 'documents', 'write', 'own', '{"patientId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440012', 'documents:delete', 'Delete Documents', 'Delete medical documents', 'documents', 'delete', 'own', '{"patientId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440013', 'documents:read:all', 'Read All Documents', 'View all medical documents', 'documents', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-446655440014', 'documents:write:all', 'Write All Documents', 'Upload and edit all medical documents', 'documents', 'write', 'global', null),
  ('650e8400-e29b-41d4-a716-446655440015', 'documents:delete:all', 'Delete All Documents', 'Delete all medical documents', 'documents', 'delete', 'global', null),
  
  -- Appointments permissions (comprehensive)
  ('650e8400-e29b-41d4-a716-446655440020', 'appointments:read', 'Read Appointments', 'View appointments', 'appointments', 'read', 'own', '{"patientId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440021', 'appointments:write', 'Write Appointments', 'Create and edit appointments', 'appointments', 'write', 'own', '{"patientId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440022', 'appointments:delete', 'Delete Appointments', 'Delete appointments', 'appointments', 'delete', 'own', '{"patientId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440023', 'appointments:read:assigned', 'Read Assigned Appointments', 'View assigned appointments', 'appointments', 'read', 'assigned', '{"assignedDoctor": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440024', 'appointments:write:assigned', 'Write Assigned Appointments', 'Manage assigned appointments', 'appointments', 'write', 'assigned', '{"assignedDoctor": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440025', 'appointments:read:all', 'Read All Appointments', 'View all appointments', 'appointments', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'appointments:write:all', 'Write All Appointments', 'Manage all appointments', 'appointments', 'write', 'global', null),
  
  -- Users permissions (comprehensive)
  ('650e8400-e29b-41d4-a716-************', 'users:read', 'Read Users', 'View user profiles', 'users', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'users:write', 'Write Users', 'Create and edit user accounts', 'users', 'write', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'users:delete', 'Delete Users', 'Delete user accounts', 'users', 'delete', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'users:read:own', 'Read Own Profile', 'View own user profile', 'users', 'read', 'own', '{"id": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-************', 'users:write:own', 'Write Own Profile', 'Edit own user profile', 'users', 'write', 'own', '{"id": "{{userId}}"}'),
  
  -- Doctor Credentials permissions (comprehensive)
  ('650e8400-e29b-41d4-a716-************', 'doctor_credentials:read', 'Read Own Credentials', 'View own doctor credentials', 'doctor_credentials', 'read', 'own', '{"doctorId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-************', 'doctor_credentials:create', 'Create Own Credentials', 'Create own doctor credentials', 'doctor_credentials', 'create', 'own', '{"doctorId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-************', 'doctor_credentials:update', 'Update Own Credentials', 'Update own doctor credentials', 'doctor_credentials', 'update', 'own', '{"doctorId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440043', 'doctor_credentials:delete', 'Delete Own Credentials', 'Delete own doctor credentials', 'doctor_credentials', 'delete', 'own', '{"doctorId": "{{userId}}"}'),
  ('650e8400-e29b-41d4-a716-446655440044', 'doctor_credentials:read:all', 'Read All Credentials', 'View all doctor credentials', 'doctor_credentials', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-446655440045', 'doctor_credentials:verify', 'Verify Credentials', 'Verify doctor credentials', 'doctor_credentials', 'verify', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'doctor_credentials:reject', 'Reject Credentials', 'Reject doctor credentials', 'doctor_credentials', 'reject', 'global', null),
  
  -- Admin permissions (comprehensive)
  ('650e8400-e29b-41d4-a716-************', 'admin:full_access', 'Full System Access', 'Complete administrative access', '*', '*', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'admin:user_management', 'User Management', 'Manage user accounts and roles', 'users', '*', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'admin:system_config', 'System Configuration', 'Configure system settings', 'system', '*', 'global', null),
  
  -- Audit and Compliance permissions
  ('650e8400-e29b-41d4-a716-************', 'audit:read', 'Read Audit Logs', 'View audit logs', 'audit', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'compliance:read', 'Read Compliance Data', 'View compliance information', 'compliance', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'compliance:write', 'Write Compliance Data', 'Manage compliance information', 'compliance', 'write', 'global', null),
  
  -- CRM permissions
  ('650e8400-e29b-41d4-a716-************', 'crm:read', 'Read CRM Data', 'View CRM information', 'crm', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-************', 'crm:write', 'Write CRM Data', 'Manage CRM information', 'crm', 'write', 'global', null),
  ('650e8400-e29b-41d4-a716-446655440072', 'organizations:read', 'Read Organizations', 'View organization data', 'organizations', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-446655440073', 'organizations:write', 'Write Organizations', 'Manage organization data', 'organizations', 'write', 'global', null),
  
  -- Specializations permissions
  ('650e8400-e29b-41d4-a716-446655440080', 'specializations:read', 'Read Specializations', 'View medical specializations', 'specializations', 'read', 'global', null),
  ('650e8400-e29b-41d4-a716-446655440081', 'specializations:write', 'Write Specializations', 'Manage medical specializations', 'specializations', 'write', 'global', null);

-- Assign permissions to roles (comprehensive role-based access control)
INSERT INTO role_permissions (role_id, permission_id) VALUES
  -- Patient permissions (own data access)
  ('550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440001'), -- cases:read
  ('550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440002'), -- cases:write
  ('550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440010'), -- documents:read
  ('550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440011'), -- documents:write
  ('550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440012'), -- documents:delete
  ('550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440020'), -- appointments:read
  ('550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440021'), -- appointments:write
  ('550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-************'), -- users:read:own
  ('550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-************'), -- users:write:own
  
  -- Doctor permissions (assigned cases + own credentials)
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440003'), -- cases:read:assigned
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440004'), -- cases:write:assigned
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440013'), -- documents:read:all (for assigned cases)
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440023'), -- appointments:read:assigned
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440024'), -- appointments:write:assigned
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-************'), -- users:read (for patient info)
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-************'), -- users:read:own
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-************'), -- users:write:own
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-************'), -- doctor_credentials:read
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-************'), -- doctor_credentials:create
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-************'), -- doctor_credentials:update
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440043'), -- doctor_credentials:delete
  ('550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440080'), -- specializations:read
  
  -- Agent permissions (read access for AI operations + CRM)
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440005'), -- cases:read:all
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440006'), -- cases:write:all
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440013'), -- documents:read:all
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440025'), -- appointments:read:all
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-************'), -- users:read
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-************'), -- crm:read
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-************'), -- crm:write
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440072'), -- organizations:read
  ('550e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440073'), -- organizations:write
  
  -- Admin permissions (full system access)
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- admin:full_access
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- admin:user_management
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- admin:system_config
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440005'), -- cases:read:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440006'), -- cases:write:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440007'), -- cases:delete:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440013'), -- documents:read:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440014'), -- documents:write:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440015'), -- documents:delete:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440025'), -- appointments:read:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- appointments:write:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- users:read
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- users:write
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- users:delete
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440044'), -- doctor_credentials:read:all
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440045'), -- doctor_credentials:verify
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- doctor_credentials:reject
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- audit:read
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- compliance:read
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- compliance:write
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- crm:read
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-************'), -- crm:write
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440072'), -- organizations:read
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440073'), -- organizations:write
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440080'), -- specializations:read
  ('550e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440081'); -- specializations:write

-- Add performance indexes for the comprehensive permissions system
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action_scope ON permissions(resource, action, scope);
CREATE INDEX IF NOT EXISTS idx_permissions_active ON permissions(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_role_permissions_active ON role_permissions(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_roles_active ON user_roles(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_roles_active ON roles(is_active) WHERE is_active = true;

-- Update schema_migrations to track this migration
INSERT INTO schema_migrations (version, filename, applied_at) VALUES ('0012', '0012_complete_permissions_system.sql', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW(), filename = '0012_complete_permissions_system.sql';