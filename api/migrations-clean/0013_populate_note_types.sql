-- Migration: 0013_populate_note_types
-- Description: Populate note_types table with initial clinical note types
-- Dependencies: 0003_core_cases_and_documents.sql

-- Insert initial note types
INSERT INTO "note_types" (
    "id",
    "key", 
    "name", 
    "description", 
    "icon", 
    "color", 
    "category", 
    "allowed_roles", 
    "requires_doctor", 
    "requires_permission", 
    "auto_save", 
    "auto_save_delay", 
    "rich_text", 
    "show_doctor_info", 
    "show_in_sidebar", 
    "placeholder", 
    "template", 
    "ai_enabled", 
    "ai_model", 
    "ai_prompt", 
    "sort_order", 
    "is_active", 
    "created_at", 
    "updated_at", 
    "created_by"
) VALUES 
-- Clinical Notes type (used by case creation)
(
    'c6867d50-6dc7-443d-aa14-061ab153d685',
    'clinical_notes',
    'Clinical Notes',
    'Initial clinical information provided by patient during case creation',
    'FileText',
    'blue',
    'clinical',
    '["patient", "doctor", "admin"]',
    false,
    null,
    true,
    2000,
    true,
    true,
    false,
    'Enter clinical information...',
    '{"symptoms": "", "pastMedicalHistory": "", "currentMedications": "", "caseDescription": ""}',
    false,
    null,
    null,
    1,
    true,
    now(),
    now(),
    null
),
-- Doctor Assessment type
(
    gen_random_uuid(),
    'doctor_assessment',
    'Doctor Assessment',
    'Medical assessment and diagnosis by assigned doctor',
    'Stethoscope',
    'green',
    'clinical',
    '["doctor", "admin"]',
    true,
    'case:write',
    true,
    2000,
    true,
    true,
    true,
    'Enter your medical assessment...',
    '{"assessment": "", "diagnosis": "", "recommendations": "", "followUp": ""}',
    true,
    'gpt-4',
    'Provide a comprehensive medical assessment based on the patient information.',
    2,
    true,
    now(),
    now(),
    null
),
-- Treatment Plan type
(
    gen_random_uuid(),
    'treatment_plan',
    'Treatment Plan',
    'Detailed treatment plan and medication recommendations',
    'Pill',
    'purple',
    'clinical',
    '["doctor", "admin"]',
    true,
    'case:write',
    true,
    2000,
    true,
    true,
    true,
    'Enter treatment plan...',
    '{"medications": "", "dosage": "", "duration": "", "instructions": "", "followUp": ""}',
    false,
    null,
    null,
    3,
    true,
    now(),
    now(),
    null
),
-- Progress Notes type
(
    gen_random_uuid(),
    'progress_notes',
    'Progress Notes',
    'Follow-up notes and patient progress tracking',
    'TrendingUp',
    'orange',
    'clinical',
    '["doctor", "admin"]',
    true,
    'case:write',
    true,
    2000,
    true,
    true,
    true,
    'Enter progress notes...',
    '{"progress": "", "symptoms": "", "response": "", "nextSteps": ""}',
    false,
    null,
    null,
    4,
    true,
    now(),
    now(),
    null
),
-- Administrative Notes type
(
    gen_random_uuid(),
    'admin_notes',
    'Administrative Notes',
    'Administrative notes and case management information',
    'Clipboard',
    'gray',
    'administrative',
    '["admin", "agent"]',
    false,
    'case:write',
    true,
    2000,
    true,
    false,
    true,
    'Enter administrative notes...',
    '{"notes": "", "actions": "", "contacts": ""}',
    false,
    null,
    null,
    5,
    true,
    now(),
    now(),
    null
)
ON CONFLICT (id) DO NOTHING;

-- Verify the clinical notes type was inserted
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM note_types WHERE id = 'c6867d50-6dc7-443d-aa14-061ab153d685') THEN
        RAISE EXCEPTION 'Failed to insert clinical_notes type with required ID';
    END IF;
    
    RAISE NOTICE 'Successfully populated note_types table with % types', (SELECT COUNT(*) FROM note_types);
END $$;