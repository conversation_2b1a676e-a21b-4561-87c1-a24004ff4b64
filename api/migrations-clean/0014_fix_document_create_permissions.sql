-- Migration: 0014_fix_document_create_permissions
-- Description: Add missing documents:create permissions for document upload functionality
-- Dependencies: 0013_populate_note_types.sql

-- Add documents:create permissions that are missing
INSERT INTO permissions (
    id,
    name,
    display_name,
    description,
    resource,
    action,
    scope,
    filter_conditions,
    is_active,
    created_at,
    updated_at
) VALUES 
-- Patient create permission (own scope)
(
    gen_random_uuid(),
    'documents:create',
    'Create Documents',
    'Create and upload documents',
    'documents',
    'create',
    'own',
    '{"patientId": "{{userId}}"}',
    true,
    now(),
    now()
),
-- Global create permission for admins/agents
(
    gen_random_uuid(),
    'documents:create:all',
    'Create All Documents',
    'Create and upload documents for any user',
    'documents',
    'create',
    'global',
    null,
    true,
    now(),
    now()
)
ON CONFLICT (name) DO NOTHING;

-- Assign documents:create permission to patient role
INSERT INTO role_permissions (
    id,
    role_id,
    permission_id,
    is_active,
    created_at
)
SELECT
    gen_random_uuid(),
    r.id,
    p.id,
    true,
    now()
FROM roles r, permissions p
WHERE r.name = 'patient'
AND p.name = 'documents:create'
AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp
    WHERE rp.role_id = r.id AND rp.permission_id = p.id
);

-- Assign documents:create:all permission to admin role
INSERT INTO role_permissions (
    id,
    role_id,
    permission_id,
    is_active,
    created_at
)
SELECT
    gen_random_uuid(),
    r.id,
    p.id,
    true,
    now()
FROM roles r, permissions p
WHERE r.name = 'admin'
AND p.name = 'documents:create:all'
AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp
    WHERE rp.role_id = r.id AND rp.permission_id = p.id
);

-- Assign documents:create:all permission to agent role
INSERT INTO role_permissions (
    id,
    role_id,
    permission_id,
    is_active,
    created_at
)
SELECT
    gen_random_uuid(),
    r.id,
    p.id,
    true,
    now()
FROM roles r, permissions p
WHERE r.name = 'agent'
AND p.name = 'documents:create:all'
AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp
    WHERE rp.role_id = r.id AND rp.permission_id = p.id
);

-- Assign documents:create:all permission to doctor role (doctors can upload documents for cases)
INSERT INTO role_permissions (
    id,
    role_id,
    permission_id,
    is_active,
    created_at
)
SELECT
    gen_random_uuid(),
    r.id,
    p.id,
    true,
    now()
FROM roles r, permissions p
WHERE r.name = 'doctor'
AND p.name = 'documents:create:all'
AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp
    WHERE rp.role_id = r.id AND rp.permission_id = p.id
);

-- Verify the permissions were added
DO $$
DECLARE
    patient_create_count INTEGER;
    admin_create_count INTEGER;
    agent_create_count INTEGER;
    doctor_create_count INTEGER;
BEGIN
    -- Count patient create permissions
    SELECT COUNT(*) INTO patient_create_count
    FROM roles r 
    JOIN role_permissions rp ON r.id = rp.role_id 
    JOIN permissions p ON rp.permission_id = p.id
    WHERE r.name = 'patient' AND p.name = 'documents:create';
    
    -- Count admin create permissions
    SELECT COUNT(*) INTO admin_create_count
    FROM roles r 
    JOIN role_permissions rp ON r.id = rp.role_id 
    JOIN permissions p ON rp.permission_id = p.id
    WHERE r.name = 'admin' AND p.name = 'documents:create:all';
    
    -- Count agent create permissions
    SELECT COUNT(*) INTO agent_create_count
    FROM roles r 
    JOIN role_permissions rp ON r.id = rp.role_id 
    JOIN permissions p ON rp.permission_id = p.id
    WHERE r.name = 'agent' AND p.name = 'documents:create:all';
    
    -- Count doctor create permissions
    SELECT COUNT(*) INTO doctor_create_count
    FROM roles r 
    JOIN role_permissions rp ON r.id = rp.role_id 
    JOIN permissions p ON rp.permission_id = p.id
    WHERE r.name = 'doctor' AND p.name = 'documents:create:all';
    
    RAISE NOTICE 'Document create permissions assigned: patient=%, admin=%, agent=%, doctor=%', 
        patient_create_count, admin_create_count, agent_create_count, doctor_create_count;
        
    IF patient_create_count = 0 THEN
        RAISE WARNING 'Patient role does not have documents:create permission';
    END IF;
END $$;