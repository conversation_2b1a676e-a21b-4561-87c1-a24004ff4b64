-- Fix document permissions to use case-based access instead of uploadedBy
-- Documents should be manageable by anyone who can access the associated case
-- This allows doctors, patients, and other case participants to manage documents

-- For documents attached to cases, use case-based permissions
-- For standalone documents (no caseId), fall back to uploadedBy

-- Update documents:delete permission to allow case-based access
UPDATE permissions
SET filter_conditions = '{"patientId": "{{userId}}"}'
WHERE name = 'documents:delete' AND scope = 'own';

-- Update documents:write permission to allow case-based access
UPDATE permissions
SET filter_conditions = '{"patientId": "{{userId}}"}'
WHERE name = 'documents:write' AND scope = 'own';

-- Update documents:read permission to allow case-based access
UPDATE permissions
SET filter_conditions = '{"patientId": "{{userId}}"}'
WHERE name = 'documents:read' AND scope = 'own';

-- Add permissions for doctors assigned to cases
INSERT INTO permissions (id, name, display_name, description, resource, action, scope, filter_conditions, is_active)
VALUES
  ('650e8400-e29b-41d4-a716-446655440030', 'documents:read:assigned', 'Read Assigned Case Documents', 'View documents for assigned cases', 'documents', 'read', 'assigned', '{"assignedDoctor": "{{userId}}"}', true),
  ('650e8400-e29b-41d4-a716-446655440031', 'documents:write:assigned', 'Write Assigned Case Documents', 'Edit documents for assigned cases', 'documents', 'write', 'assigned', '{"assignedDoctor": "{{userId}}"}', true),
  ('650e8400-e29b-41d4-a716-446655440032', 'documents:delete:assigned', 'Delete Assigned Case Documents', 'Delete documents for assigned cases', 'documents', 'delete', 'assigned', '{"assignedDoctor": "{{userId}}"}', true),
  ('650e8400-e29b-41d4-a716-446655440033', 'documents:delete:uploaded', 'Delete Uploaded Documents', 'Delete documents you uploaded', 'documents', 'delete', 'own', '{"uploadedBy": "{{userId}}"}', true),
  ('650e8400-e29b-41d4-a716-446655440034', 'documents:write:uploaded', 'Write Uploaded Documents', 'Edit documents you uploaded', 'documents', 'write', 'own', '{"uploadedBy": "{{userId}}"}', true),
  ('650e8400-e29b-41d4-a716-446655440035', 'documents:read:uploaded', 'Read Uploaded Documents', 'View documents you uploaded', 'documents', 'read', 'own', '{"uploadedBy": "{{userId}}"}', true)
ON CONFLICT (id) DO NOTHING;

-- Assign the new document permissions to appropriate roles
-- Doctors get assigned case document permissions
INSERT INTO role_permissions (role_id, permission_id, is_active)
SELECT r.id, p.id, true
FROM roles r, permissions p
WHERE p.name IN ('documents:read:assigned', 'documents:write:assigned', 'documents:delete:assigned')
  AND r.name = 'doctor'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- All roles get uploaded document permissions (fallback)
INSERT INTO role_permissions (role_id, permission_id, is_active)
SELECT r.id, p.id, true
FROM roles r, permissions p
WHERE p.name IN ('documents:delete:uploaded', 'documents:write:uploaded', 'documents:read:uploaded')
  AND r.name IN ('patient', 'doctor', 'admin', 'agent')
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Verify the changes
SELECT id, name, scope, filter_conditions
FROM permissions
WHERE resource = 'documents' AND action IN ('read', 'write', 'delete') AND scope = 'own';