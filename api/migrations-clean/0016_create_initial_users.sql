-- Migration: 0016_create_initial_users
-- Description: Create initial admin, doctor, and patient users for testing
-- Dependencies: 0015_fix_document_delete_permissions

-- Create initial users with hashed passwords
-- Password for all users: "Continuia" (hashed with bcrypt)
-- Note: In production, these should be changed immediately

INSERT INTO "users" (
    id,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    is_active,
    is_email_verified,
    created_at,
    updated_at
) VALUES
    (
        '550e8400-e29b-41d4-a716-446655440001',
        '<EMAIL>',
        '$2a$12$hzwunnCbGAl51ITI/rX.KunHEjlw/Vnk9KPFpaRDFChiV0D/WAsui', -- Continuia
        'Admin',
        'User',
        'admin',
        true,
        true,
        NOW(),
        NOW()
    ),
    (
        '550e8400-e29b-41d4-a716-446655440002',
        '<EMAIL>',
        '$2a$12$hzwunnCbGAl51ITI/rX.KunHEjlw/Vnk9KPFpaRDFChiV0D/WAsui', -- Continuia
        'Dr. <PERSON>',
        '<PERSON>',
        'doctor',
        true,
        true,
        NOW(),
        NOW()
    ),
    (
        '550e8400-e29b-41d4-a716-446655440003',
        '<EMAIL>',
        '$2a$12$hzwunnCbGAl51ITI/rX.KunHEjlw/Vnk9KPFpaRDFChiV0D/WAsui', -- Continuia
        'Shree',
        'Mandadi',
        'patient',
        true,
        true,
        NOW(),
        NOW()
    )
ON CONFLICT (email) DO UPDATE SET
    password_hash = EXCLUDED.password_hash,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    role = EXCLUDED.role,
    is_active = EXCLUDED.is_active,
    is_email_verified = EXCLUDED.is_email_verified,
    updated_at = NOW();

-- Assign roles to users using the modern RBAC system
-- First, ensure the roles exist (they should from previous migrations)
INSERT INTO "roles" (id, name, display_name, description, is_active)
VALUES 
    ('650e8400-e29b-41d4-a716-446655440001', 'admin', 'Administrator', 'Full system access', true),
    ('650e8400-e29b-41d4-a716-446655440002', 'doctor', 'Doctor', 'Medical professional access', true),
    ('650e8400-e29b-41d4-a716-446655440003', 'patient', 'Patient', 'Patient access', true),
    ('650e8400-e29b-41d4-a716-446655440004', 'agent', 'Agent', 'Support agent access', true)
ON CONFLICT (name) DO NOTHING;

-- Assign roles to users
INSERT INTO "user_roles" (id, user_id, role_id, assigned_at, is_active)
VALUES 
    (
        '750e8400-e29b-41d4-a716-446655440001',
        '550e8400-e29b-41d4-a716-446655440001', -- admin user
        (SELECT id FROM roles WHERE name = 'admin'),
        NOW(),
        true
    ),
    (
        '750e8400-e29b-41d4-a716-446655440002',
        '550e8400-e29b-41d4-a716-446655440002', -- doctor user
        (SELECT id FROM roles WHERE name = 'doctor'),
        NOW(),
        true
    ),
    (
        '750e8400-e29b-41d4-a716-446655440003',
        '550e8400-e29b-41d4-a716-446655440003', -- patient user
        (SELECT id FROM roles WHERE name = 'patient'),
        NOW(),
        true
    )
ON CONFLICT (user_id, role_id) DO UPDATE SET
    is_active = true,
    assigned_at = NOW();

-- Create a sample case for the patient to test document uploads
INSERT INTO "cases" (
    id,
    title,
    status,
    urgency_level,
    patient_id,
    created_at,
    updated_at
) VALUES (
    '1d988dcd-f4bb-4aa1-9166-f5e2d0134945',
    'Sample Medical Case',
    'draft',
    'medium',
    '550e8400-e29b-41d4-a716-446655440003', -- patient user
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    urgency_level = EXCLUDED.urgency_level,
    patient_id = EXCLUDED.patient_id,
    updated_at = NOW();

-- Verify the users were created
SELECT 
    u.id,
    u.email,
    u.first_name,
    u.last_name,
    u.role,
    u.is_active,
    r.name as assigned_role
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = true
LEFT JOIN roles r ON ur.role_id = r.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
ORDER BY u.email;

-- Display login instructions
DO $$
BEGIN
    RAISE NOTICE '=== Initial Users Created ===';
    RAISE NOTICE 'Admin: <EMAIL> / Continuia';
    RAISE NOTICE 'Doctor: <EMAIL> / Continuia';
    RAISE NOTICE 'Patient: <EMAIL> / Continuia';
    RAISE NOTICE '=== IMPORTANT: Change passwords in production! ===';
END $$;