-- Migration: 0017_crm_email_campaigns
-- Description: CRM email campaign management system with OPAL-style rules
-- Dependencies: 0016_create_initial_users.sql

-- Create campaign-related enums
DO $$ BEGIN
 CREATE TYPE "campaign_status" AS ENUM('Draft', 'Active', 'Paused', 'Completed', 'Archived');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "campaign_type" AS ENUM('Email', 'SMS', 'Push', 'InApp');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "trigger_type" AS ENUM('Manual', 'UserRegistration', 'CaseClosed', 'ContactCreated', 'CaseAging', 'LeadStatusChange', 'AppointmentScheduled', 'DocumentUploaded', 'Custom');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "template_type" AS ENUM('Welcome', 'FollowUp', 'Reminder', 'Notification', 'Marketing', 'Transactional', 'Custom');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "execution_status" AS ENUM('Pending', 'Sent', 'Failed', 'Bounced', 'Delivered', 'Opened', 'Clicked');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 CREATE TYPE "audience_type" AS ENUM('AllUsers', 'Doctors', 'Patients', 'Agents', 'Admins', 'Custom');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create email templates table
CREATE TABLE IF NOT EXISTS "crm_email_templates" (
	"template_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"type" "template_type" NOT NULL,
	"subject" varchar(500) NOT NULL,
	"html_content" text NOT NULL,
	"text_content" text,
	"variables" jsonb DEFAULT '[]'::jsonb,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_by" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create campaign rules table
CREATE TABLE IF NOT EXISTS "crm_campaign_rules" (
	"rule_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"rule_definition" jsonb NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_by" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create campaigns table
CREATE TABLE IF NOT EXISTS "crm_campaigns" (
	"campaign_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"type" "campaign_type" DEFAULT 'Email' NOT NULL,
	"status" "campaign_status" DEFAULT 'Draft' NOT NULL,
	"trigger_type" "trigger_type" NOT NULL,
	"trigger_config" jsonb,
	"audience_type" "audience_type" NOT NULL,
	"audience_rules" jsonb,
	"template_id" uuid,
	"rule_id" uuid,
	"start_date" timestamp,
	"end_date" timestamp,
	"send_limit_per_day" integer,
	"send_limit_per_hour" integer,
	"respect_unsubscribe" boolean DEFAULT true NOT NULL,
	"track_opens" boolean DEFAULT true NOT NULL,
	"track_clicks" boolean DEFAULT true NOT NULL,
	"tags" jsonb DEFAULT '[]'::jsonb,
	"metadata" jsonb DEFAULT '{}'::jsonb,
	"created_by" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create campaign executions table
CREATE TABLE IF NOT EXISTS "crm_campaign_executions" (
	"execution_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"campaign_id" uuid NOT NULL,
	"recipient_user_id" uuid,
	"recipient_email" varchar(255) NOT NULL,
	"recipient_name" varchar(255),
	"related_lead_id" uuid,
	"related_case_id" uuid,
	"status" "execution_status" DEFAULT 'Pending' NOT NULL,
	"scheduled_at" timestamp NOT NULL,
	"sent_at" timestamp,
	"delivered_at" timestamp,
	"opened_at" timestamp,
	"clicked_at" timestamp,
	"bounced_at" timestamp,
	"unsubscribed_at" timestamp,
	"subject" varchar(500),
	"html_content" text,
	"text_content" text,
	"open_count" integer DEFAULT 0 NOT NULL,
	"click_count" integer DEFAULT 0 NOT NULL,
	"tracking_pixel_url" varchar(500),
	"error_message" text,
	"retry_count" integer DEFAULT 0 NOT NULL,
	"max_retries" integer DEFAULT 3 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create campaign analytics table
CREATE TABLE IF NOT EXISTS "crm_campaign_analytics" (
	"analytics_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"campaign_id" uuid NOT NULL,
	"total_sent" integer DEFAULT 0 NOT NULL,
	"total_delivered" integer DEFAULT 0 NOT NULL,
	"total_bounced" integer DEFAULT 0 NOT NULL,
	"total_opened" integer DEFAULT 0 NOT NULL,
	"total_clicked" integer DEFAULT 0 NOT NULL,
	"total_unsubscribed" integer DEFAULT 0 NOT NULL,
	"delivery_rate" integer DEFAULT 0 NOT NULL,
	"open_rate" integer DEFAULT 0 NOT NULL,
	"click_rate" integer DEFAULT 0 NOT NULL,
	"bounce_rate" integer DEFAULT 0 NOT NULL,
	"unsubscribe_rate" integer DEFAULT 0 NOT NULL,
	"last_sent_at" timestamp,
	"last_updated_at" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create campaign triggers table
CREATE TABLE IF NOT EXISTS "crm_campaign_triggers" (
	"trigger_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"campaign_id" uuid NOT NULL,
	"trigger_event" varchar(255) NOT NULL,
	"entity_type" varchar(100),
	"entity_id" uuid,
	"scheduled_at" timestamp NOT NULL,
	"executed_at" timestamp,
	"context_data" jsonb DEFAULT '{}'::jsonb,
	"is_processed" boolean DEFAULT false NOT NULL,
	"error_message" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create unsubscribe list table
CREATE TABLE IF NOT EXISTS "crm_unsubscribe_list" (
	"unsubscribe_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" varchar(255) NOT NULL UNIQUE,
	"user_id" uuid,
	"campaign_id" uuid,
	"unsubscribe_reason" varchar(255),
	"unsubscribe_token" varchar(255) NOT NULL UNIQUE,
	"unsubscribe_all" boolean DEFAULT false NOT NULL,
	"unsubscribe_categories" jsonb DEFAULT '[]'::jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "crm_email_templates" ADD CONSTRAINT "crm_email_templates_created_by_users_id_fk" 
   FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_campaign_rules" ADD CONSTRAINT "crm_campaign_rules_created_by_users_id_fk" 
   FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_campaigns" ADD CONSTRAINT "crm_campaigns_template_id_crm_email_templates_template_id_fk" 
   FOREIGN KEY ("template_id") REFERENCES "crm_email_templates"("template_id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_campaigns" ADD CONSTRAINT "crm_campaigns_rule_id_crm_campaign_rules_rule_id_fk" 
   FOREIGN KEY ("rule_id") REFERENCES "crm_campaign_rules"("rule_id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_campaigns" ADD CONSTRAINT "crm_campaigns_created_by_users_id_fk" 
   FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_campaign_executions" ADD CONSTRAINT "crm_campaign_executions_campaign_id_crm_campaigns_campaign_id_fk" 
   FOREIGN KEY ("campaign_id") REFERENCES "crm_campaigns"("campaign_id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_campaign_executions" ADD CONSTRAINT "crm_campaign_executions_recipient_user_id_users_id_fk" 
   FOREIGN KEY ("recipient_user_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_campaign_analytics" ADD CONSTRAINT "crm_campaign_analytics_campaign_id_crm_campaigns_campaign_id_fk" 
   FOREIGN KEY ("campaign_id") REFERENCES "crm_campaigns"("campaign_id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_campaign_triggers" ADD CONSTRAINT "crm_campaign_triggers_campaign_id_crm_campaigns_campaign_id_fk" 
   FOREIGN KEY ("campaign_id") REFERENCES "crm_campaigns"("campaign_id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_unsubscribe_list" ADD CONSTRAINT "crm_unsubscribe_list_user_id_users_id_fk" 
   FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "crm_unsubscribe_list" ADD CONSTRAINT "crm_unsubscribe_list_campaign_id_crm_campaigns_campaign_id_fk" 
   FOREIGN KEY ("campaign_id") REFERENCES "crm_campaigns"("campaign_id") ON DELETE set null ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_crm_campaigns_status" ON "crm_campaigns"("status");
CREATE INDEX IF NOT EXISTS "idx_crm_campaigns_type" ON "crm_campaigns"("type");
CREATE INDEX IF NOT EXISTS "idx_crm_campaigns_trigger_type" ON "crm_campaigns"("trigger_type");
CREATE INDEX IF NOT EXISTS "idx_crm_campaigns_created_by" ON "crm_campaigns"("created_by");

CREATE INDEX IF NOT EXISTS "idx_crm_email_templates_type" ON "crm_email_templates"("type");
CREATE INDEX IF NOT EXISTS "idx_crm_email_templates_is_active" ON "crm_email_templates"("is_active");
CREATE INDEX IF NOT EXISTS "idx_crm_email_templates_created_by" ON "crm_email_templates"("created_by");

CREATE INDEX IF NOT EXISTS "idx_crm_campaign_rules_is_active" ON "crm_campaign_rules"("is_active");
CREATE INDEX IF NOT EXISTS "idx_crm_campaign_rules_created_by" ON "crm_campaign_rules"("created_by");

CREATE INDEX IF NOT EXISTS "idx_crm_campaign_executions_campaign_id" ON "crm_campaign_executions"("campaign_id");
CREATE INDEX IF NOT EXISTS "idx_crm_campaign_executions_status" ON "crm_campaign_executions"("status");
CREATE INDEX IF NOT EXISTS "idx_crm_campaign_executions_recipient_user_id" ON "crm_campaign_executions"("recipient_user_id");
CREATE INDEX IF NOT EXISTS "idx_crm_campaign_executions_scheduled_at" ON "crm_campaign_executions"("scheduled_at");

CREATE INDEX IF NOT EXISTS "idx_crm_campaign_analytics_campaign_id" ON "crm_campaign_analytics"("campaign_id");

CREATE INDEX IF NOT EXISTS "idx_crm_campaign_triggers_campaign_id" ON "crm_campaign_triggers"("campaign_id");
CREATE INDEX IF NOT EXISTS "idx_crm_campaign_triggers_trigger_event" ON "crm_campaign_triggers"("trigger_event");
CREATE INDEX IF NOT EXISTS "idx_crm_campaign_triggers_is_processed" ON "crm_campaign_triggers"("is_processed");

CREATE INDEX IF NOT EXISTS "idx_crm_unsubscribe_list_email" ON "crm_unsubscribe_list"("email");
CREATE INDEX IF NOT EXISTS "idx_crm_unsubscribe_list_user_id" ON "crm_unsubscribe_list"("user_id");
CREATE INDEX IF NOT EXISTS "idx_crm_unsubscribe_list_campaign_id" ON "crm_unsubscribe_list"("campaign_id");