-- Migration: Add missing columns to users table
-- This migration adds columns that exist in the schema but are missing from the database

DO $$
BEGIN
    -- Add profile_picture_url column to users table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'profile_picture_url'
    ) THEN
        ALTER TABLE "users" ADD COLUMN "profile_picture_url" varchar(500);
        RAISE NOTICE 'Added profile_picture_url column to users table';
    ELSE
        RAISE NOTICE 'profile_picture_url column already exists in users table';
    END IF;

    -- Add phone_number column to users table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'phone_number'
    ) THEN
        ALTER TABLE "users" ADD COLUMN "phone_number" varchar(20);
        RAISE NOTICE 'Added phone_number column to users table';
    ELSE
        RAISE NOTICE 'phone_number column already exists in users table';
    END IF;

    RAISE NOTICE 'Migration 0018 completed: Added missing user columns';
END $$;
