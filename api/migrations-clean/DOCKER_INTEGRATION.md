# Docker Integration Guide

This guide explains how to use the migration system with Dock<PERSON> for both development and production environments.

## 🐳 Docker Integration Features

### ✅ What's Included in Docker

- **PostgreSQL client** - For database connections
- **Bash shell** - For running migration scripts
- **Migration scripts** - Automatically copied and made executable
- **Global commands** - `migrate` and `validate-migrations` available system-wide
- **npm scripts** - Easy access via package.json

### 🚀 Quick Start with Docker

#### 1. Using Docker Compose (Recommended)

```bash
# Start the main application with database
docker compose up -d

# Run migrations in the API container
docker compose exec api migrate up

# Check migration status
docker compose exec api migrate status

# Validate migrations
docker compose exec api validate-migrations --quick
```

#### 2. Using Dedicated Migration Service

```bash
# Use the dedicated migration compose file
cd api
docker compose -f docker-compose.migrations.yml up -d postgres

# Run migrations
docker compose -f docker-compose.migrations.yml run --rm migrations migrate up

# Check status
docker compose -f docker-compose.migrations.yml run --rm migrations migrate status

# Rollback if needed
docker compose -f docker-compose.migrations.yml run --rm migrations migrate down 2
```

#### 3. Using npm Scripts in Container

```bash
# Run via npm scripts
docker compose exec api npm run migrate:up
docker compose exec api npm run migrate:status
docker compose exec api npm run migrate:validate-quick
```

## 📋 Available Commands

### Docker Compose Commands

```bash
# === Main Application Container ===
docker compose exec api migrate <command>
docker compose exec api validate-migrations [options]

# === Dedicated Migration Container ===
docker compose -f docker-compose.migrations.yml run --rm migrations migrate <command>
docker compose -f docker-compose.migrations.yml run --rm migrations validate-migrations [options]

# === npm Scripts ===
docker compose exec api npm run migrate:up
docker compose exec api npm run migrate:down
docker compose exec api npm run migrate:status
docker compose exec api npm run migrate:reset
docker compose exec api npm run migrate:validate
docker compose exec api npm run migrate:validate-quick
```

### Migration Commands

| Command | Description | Example |
|---------|-------------|---------|
| `migrate up` | Apply all migrations | `docker compose exec api migrate up` |
| `migrate up 0005` | Apply up to version 0005 | `docker compose exec api migrate up 0005` |
| `migrate down 2` | Rollback 2 migrations | `docker compose exec api migrate down 2` |
| `migrate down 0003` | Rollback to version 0003 | `docker compose exec api migrate down 0003` |
| `migrate status` | Show migration status | `docker compose exec api migrate status` |
| `migrate list` | List available migrations | `docker compose exec api migrate list` |
| `migrate reset` | Rollback all migrations | `docker compose exec api migrate reset` |

### Validation Commands

| Command | Description | Example |
|---------|-------------|---------|
| `validate-migrations` | Full validation | `docker compose exec api validate-migrations` |
| `validate-migrations --quick` | Quick validation | `docker compose exec api validate-migrations --quick` |
| `validate-migrations --syntax-only` | Syntax only | `docker compose exec api validate-migrations --syntax-only` |

## 🔧 Environment Configuration

### Environment Variables

Set these in your `.env` file or docker-compose.yml:

```bash
# Database Configuration
POSTGRES_HOST=postgres          # Use 'postgres' for Docker network
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password
POSTGRES_DB=continuia_clean

# For external database access
# POSTGRES_HOST=localhost       # Use 'localhost' for external access
```

### Docker Network

The migration system uses the `continuia` network. Ensure it exists:

```bash
# Create network if it doesn't exist
docker network create continuia
```

## 🏗️ Development Workflow

### 1. Initial Setup

```bash
# Start services
docker compose up -d

# Validate migrations
docker compose exec api npm run migrate:validate-quick

# Apply all migrations
docker compose exec api npm run migrate:up

# Verify status
docker compose exec api npm run migrate:status
```

### 2. Adding New Migrations

```bash
# Create new migration file (follow naming convention)
# Example: 0011_add_new_feature.sql

# Validate the new migration
docker compose exec api validate-migrations --quick

# Apply the new migration
docker compose exec api migrate up

# Verify it was applied
docker compose exec api migrate status
```

### 3. Testing Rollbacks

```bash
# Rollback last migration
docker compose exec api migrate down 1

# Check status
docker compose exec api migrate status

# Re-apply
docker compose exec api migrate up
```

## 🚀 Production Deployment

### 1. Pre-deployment Validation

```bash
# Build production image
docker build -t continuia-api:latest .

# Run validation in production-like environment
docker run --rm \
  -e POSTGRES_HOST=your-prod-host \
  -e POSTGRES_USER=your-prod-user \
  -e POSTGRES_PASSWORD=your-prod-password \
  -e POSTGRES_DB=your-prod-db \
  continuia-api:latest validate-migrations
```

### 2. Production Migration

```bash
# Apply migrations to production database
docker run --rm \
  -e POSTGRES_HOST=your-prod-host \
  -e POSTGRES_USER=your-prod-user \
  -e POSTGRES_PASSWORD=your-prod-password \
  -e POSTGRES_DB=your-prod-db \
  continuia-api:latest migrate up

# Verify status
docker run --rm \
  -e POSTGRES_HOST=your-prod-host \
  -e POSTGRES_USER=your-prod-user \
  -e POSTGRES_PASSWORD=your-prod-password \
  -e POSTGRES_DB=your-prod-db \
  continuia-api:latest migrate status
```

### 3. Emergency Rollback

```bash
# Rollback specific number of migrations
docker run --rm \
  -e POSTGRES_HOST=your-prod-host \
  -e POSTGRES_USER=your-prod-user \
  -e POSTGRES_PASSWORD=your-prod-password \
  -e POSTGRES_DB=your-prod-db \
  continuia-api:latest migrate down 2
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Permission Denied

```bash
# If you get permission errors, ensure scripts are executable
docker compose exec api chmod +x /app/migrations-clean/*.sh
```

#### 2. Database Connection Issues

```bash
# Test database connection
docker compose exec api psql -h postgres -U postgres -d continuia_clean -c "SELECT 1;"

# Check if database exists
docker compose exec postgres psql -U postgres -l
```

#### 3. Migration File Not Found

```bash
# List available migrations
docker compose exec api migrate list

# Check migration directory
docker compose exec api ls -la /app/migrations-clean/
```

#### 4. Network Issues

```bash
# Ensure continuia network exists
docker network ls | grep continuia

# Create network if missing
docker network create continuia
```

### Debug Mode

```bash
# Run with verbose output
docker compose exec api bash -x /app/migrations-clean/migrate.sh status

# Check migration table directly
docker compose exec api psql -h postgres -U postgres -d continuia_clean \
  -c "SELECT * FROM schema_migrations ORDER BY version;"
```

## 📊 Monitoring and Logging

### Migration Status Monitoring

```bash
# Create a monitoring script
cat > check-migrations.sh << 'EOF'
#!/bin/bash
echo "=== Migration Status Check ==="
docker compose exec -T api migrate status
echo ""
echo "=== Database Tables ==="
docker compose exec -T api psql -h postgres -U postgres -d continuia_clean \
  -c "\dt" -q
EOF

chmod +x check-migrations.sh
./check-migrations.sh
```

### Automated Validation

```bash
# Add to CI/CD pipeline
docker compose exec -T api validate-migrations --quick
if [ $? -eq 0 ]; then
  echo "✅ Migrations validated successfully"
  docker compose exec -T api migrate up
else
  echo "❌ Migration validation failed"
  exit 1
fi
```

## 🔐 Security Considerations

### 1. Database Credentials

- Use environment variables for database credentials
- Never hardcode passwords in migration files
- Use Docker secrets in production

### 2. Migration Access

- Limit migration execution to authorized personnel
- Use separate database user for migrations with appropriate permissions
- Log all migration activities

### 3. Backup Strategy

```bash
# Always backup before migrations
docker compose exec postgres pg_dump -U postgres continuia_clean > backup_$(date +%Y%m%d_%H%M%S).sql

# Apply migrations
docker compose exec api migrate up

# Verify success before cleaning up backup
```

## 📈 Best Practices

1. **Always validate** before applying migrations
2. **Test rollbacks** in development environment
3. **Backup database** before production migrations
4. **Use dedicated migration database** for testing
5. **Monitor migration performance** in production
6. **Keep migration files small** and focused
7. **Document complex migrations** with comments
8. **Use transactions** for complex operations
9. **Test with production-like data** volumes
10. **Have rollback plan** ready for production deployments