# Clean Drizzle Migrations

This directory contains restructured, clean migrations based on the current database schema.
These migrations are designed to be run on a fresh database and follow proper dependency order.

## 🏗️ Migration Structure

### Layer 1: Foundation (Core System)
- **0001_foundation_users_and_auth.sql** - Users, roles, permissions system
- **0002_foundation_audit_and_profiles.sql** - Audit logs and user profiles

### Layer 2: Core Business Logic
- **0003_core_cases_and_documents.sql** - Cases and medical documents
- **0004_core_appointments_and_opinions.sql** - Appointments and medical opinions

### Layer 3: Advanced Features
- **0005_advanced_case_management.sql** - Case doctors, notes, discussions
- **0006_advanced_legal_compliance.sql** - Legal compliance and consent management

### Layer 4: CRM System
- **0007_crm_organizations_and_contacts.sql** - CRM organizations and contacts
- **0008_crm_leads_and_activities.sql** - CRM leads, activities, and touchpoints

### Layer 5: Specializations and Credentials
- **0009_specializations_system.sql** - Doctor specializations and categories
- **0010_credentials_system.sql** - Doctor credentials and verification

## 🚀 Quick Start

### 1. Set Environment Variables
```bash
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=your_password
export POSTGRES_DB=continuia_clean
```

### 2. Run All Migrations
```bash
# Direct execution
./migrate.sh up

# Or via Docker (recommended)
docker compose exec api migrate up

# Or via npm scripts
docker compose exec api npm run migrate:up
```

### 3. Check Status
```bash
./migrate.sh status
# or
docker compose exec api migrate status
```

## 📋 Available Commands

### Migration Management (`migrate.sh`)

```bash
# Apply all migrations
./migrate.sh up

# Apply migrations up to specific version
./migrate.sh up 0005

# Rollback last 2 migrations
./migrate.sh down 2

# Rollback to specific version
./migrate.sh down 0003

# Show migration status
./migrate.sh status

# Reset all migrations (rollback everything)
./migrate.sh reset
```

### Validation (`validate-migrations.sh`)

```bash
# Full validation (requires database connection)
./validate-migrations.sh

# Quick validation (no database required)
./validate-migrations.sh --quick

# Syntax-only validation
./validate-migrations.sh --syntax-only
```

### Legacy Runner (`run-migrations.sh`)

```bash
# Simple forward-only migration runner
./run-migrations.sh

# Dry run to see what would be executed
./run-migrations.sh --dry-run
```

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `POSTGRES_HOST` | `localhost` | Database host |
| `POSTGRES_PORT` | `5432` | Database port |
| `POSTGRES_USER` | `postgres` | Database user |
| `POSTGRES_PASSWORD` | `postgres` | Database password |
| `POSTGRES_DB` | `continuia_clean` | Database name |

### Database Setup

1. **Create a new database** for testing:
   ```sql
   CREATE DATABASE continuia_clean;
   ```

2. **Ensure proper permissions**:
   ```sql
   GRANT ALL PRIVILEGES ON DATABASE continuia_clean TO your_user;
   ```

## 🧪 Testing Workflow

### 1. Validate Migrations
```bash
# Make scripts executable
chmod +x *.sh

# Run validation
./validate-migrations.sh
```

### 2. Test Forward Migration
```bash
# Apply all migrations
./migrate.sh up

# Verify status
./migrate.sh status
```

### 3. Test Rollback
```bash
# Rollback last few migrations
./migrate.sh down 3

# Check status
./migrate.sh status

# Re-apply
./migrate.sh up
```

### 4. Test Complete Reset
```bash
# Reset everything
./migrate.sh reset

# Verify clean state
./migrate.sh status
```

## 📊 Migration Features

### ✅ What's Included

- **Proper dependency order** - Tables created in correct sequence
- **Foreign key constraints** - All relationships properly defined
- **Indexes** - Performance indexes on key columns
- **Enums** - Consistent enum definitions across tables
- **Rollback support** - Each migration can be rolled back
- **Migration tracking** - Built-in version tracking
- **Validation** - Comprehensive validation tools
- **Error handling** - Graceful error handling and reporting

### 🔄 Rollback Strategy

Each migration includes a corresponding rollback definition that:
- Drops tables in reverse dependency order
- Removes enums, functions, and views
- Cleans up all created objects
- Maintains referential integrity

### 🛡️ Safety Features

- **Transaction safety** - Each migration runs in a transaction
- **Duplicate protection** - Prevents re-running applied migrations
- **Dependency validation** - Checks for missing dependencies
- **Syntax validation** - Validates SQL before execution
- **Checksum verification** - Detects migration file changes

## 🚨 Important Notes

### Before Production Use

1. **Test thoroughly** on a copy of your production data
2. **Backup your database** before running migrations
3. **Validate** all migrations pass without errors
4. **Review** the generated schema matches expectations
5. **Test rollbacks** to ensure they work correctly

### Migration Best Practices

- **Never modify** existing migration files after they've been applied
- **Always test** migrations on a copy of production data
- **Use transactions** for complex migrations
- **Add indexes** for performance-critical queries
- **Document** any manual steps required

### Troubleshooting

#### Connection Issues
```bash
# Test database connection
psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -c "SELECT 1;"
```

#### Permission Issues
```bash
# Grant necessary permissions
GRANT CREATE, USAGE ON SCHEMA public TO your_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_user;
```

#### Migration Conflicts
```bash
# Check current status
./migrate.sh status

# Reset if needed
./migrate.sh reset
```

## 📈 Schema Overview

The clean migrations create a comprehensive healthcare platform schema with:

- **41 tables** covering all business domains
- **25 enums** for consistent data types
- **Comprehensive indexing** for performance
- **Proper foreign key relationships**
- **HIPAA-compliant audit logging**
- **Flexible permission system**
- **Multi-doctor case management**
- **CRM integration**
- **Credential verification system**

## 🤝 Contributing

When adding new migrations:

1. Follow the naming convention: `NNNN_descriptive_name.sql`
2. Add corresponding rollback SQL to `migrate.sh`
3. Update the migration arrays in all scripts
4. Test both forward and rollback scenarios
5. Update this README with new migration details

## 📝 Migration Log

| Version | Description | Tables Added | Key Features |
|---------|-------------|--------------|--------------|
| 0001 | Users & Auth | users, roles, permissions, user_roles, role_permissions | RBAC system |
| 0002 | Audit & Profiles | audit_logs, user_profiles | HIPAA compliance |
| 0003 | Cases & Documents | cases, medical_documents, note_types, case_notes | Core business logic |
| 0004 | Appointments & Opinions | appointments, medical_opinions | Scheduling system |
| 0005 | Case Management | case_doctors, case_discussions, discussion_attachments | Multi-doctor workflow |
| 0006 | Legal Compliance | legal_compliance_*, consent_*, user_legal_agreements | Consent management |
| 0007 | CRM Organizations | crm_organizations, crm_contacts, crm_touchpoints | CRM foundation |
| 0008 | CRM Leads | crm_leads, crm_teams, crm_activity_log | Sales pipeline |
| 0009 | Specializations | specialization_categories, doctor_specializations | Medical specialties |
| 0010 | Credentials | doctor_credentials, credential_documents | Verification system |