#!/bin/bash

# Advanced Migration Management System
# Automatically discovers migrations by naming convention and supports rollbacks

set -e  # Exit on any error
set -o pipefail  # Exit on pipe failures

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST=${POSTGRES_HOST:-localhost}
DB_PORT=${POSTGRES_PORT:-5432}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASSWORD=${POSTGRES_PASSWORD:-postgres}
DB_NAME=${POSTGRES_DB:-continuia_clean}

# Migration directory
MIGRATION_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to discover migration files by naming convention
discover_migrations() {
    find "$MIGRATION_DIR" -name "[0-9][0-9][0-9][0-9]_*.sql" -type f | sort
}

# Function to extract version from migration filename
get_version_from_file() {
    local filepath="$1"
    basename "$filepath" | cut -d'_' -f1
}

# Function to get migration filename from version
get_file_from_version() {
    local version="$1"
    find "$MIGRATION_DIR" -name "${version}_*.sql" -type f | head -1
}

# Function to get ordered list of migration versions
get_migration_versions() {
    local order="$1"  # "asc" or "desc"
    
    local versions=()
    while IFS= read -r -d '' file; do
        local version=$(get_version_from_file "$file")
        versions+=("$version")
    done < <(find "$MIGRATION_DIR" -name "[0-9][0-9][0-9][0-9]_*.sql" -type f -print0 | sort -z)
    
    if [ "$order" = "desc" ]; then
        printf '%s\n' "${versions[@]}" | sort -r
    else
        printf '%s\n' "${versions[@]}" | sort
    fi
}

# Function to generate rollback SQL for a migration
generate_rollback_sql() {
    local migration_file="$1"
    local rollback_sql=""
    
    # Extract table names and generate DROP statements in reverse order
    local tables=$(grep -i "CREATE TABLE" "$migration_file" | grep -o "CREATE TABLE[^(]*" | awk '{print $NF}' | tr -d '"' | tac)
    local enums=$(grep -i "CREATE TYPE" "$migration_file" | grep -o '"[^"]*"' | tr -d '"' | tac)
    local views=$(grep -i "CREATE.*VIEW" "$migration_file" | grep -o "VIEW[^(]*" | awk '{print $NF}' | tr -d '"' | tac)
    local functions=$(grep -i "CREATE.*FUNCTION" "$migration_file" | grep -o "FUNCTION[^(]*" | awk '{print $NF}' | tr -d '"' | sed 's/()//g' | tac)
    
    # Build rollback SQL
    if [ -n "$views" ]; then
        while read -r view; do
            if [ -n "$view" ]; then
                rollback_sql="${rollback_sql}DROP VIEW IF EXISTS \"$view\" CASCADE; "
            fi
        done <<< "$views"
    fi
    
    if [ -n "$functions" ]; then
        while read -r func; do
            if [ -n "$func" ]; then
                rollback_sql="${rollback_sql}DROP FUNCTION IF EXISTS $func() CASCADE; "
            fi
        done <<< "$functions"
    fi
    
    if [ -n "$tables" ]; then
        while read -r table; do
            if [ -n "$table" ]; then
                rollback_sql="${rollback_sql}DROP TABLE IF EXISTS \"$table\" CASCADE; "
            fi
        done <<< "$tables"
    fi
    
    if [ -n "$enums" ]; then
        while read -r enum; do
            if [ -n "$enum" ]; then
                rollback_sql="${rollback_sql}DROP TYPE IF EXISTS \"$enum\" CASCADE; "
            fi
        done <<< "$enums"
    fi
    
    echo "$rollback_sql"
}

# Function to execute SQL command
execute_sql() {
    local sql_command="$1"
    local description="$2"
    
    if [ -n "$description" ]; then
        echo -e "${BLUE}$description${NC}"
    fi
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$sql_command" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to execute SQL file
execute_sql_file() {
    local sql_file="$1"
    local description="$2"
    
    if [ -n "$description" ]; then
        echo -e "${BLUE}$description${NC}"
    fi
    
    if [ ! -f "$sql_file" ]; then
        echo -e "${RED}✗ Migration file not found: $sql_file${NC}"
        return 1
    fi
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$sql_file" -v ON_ERROR_STOP=1; then
        return 0
    else
        return 1
    fi
}

# Function to check database connection
check_connection() {
    echo -e "${BLUE}Checking database connection...${NC}"
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Database connection successful${NC}"
        return 0
    else
        echo -e "${RED}✗ Cannot connect to database${NC}"
        return 1
    fi
}

# Function to create migration tracking table
create_migration_table() {
    echo -e "${BLUE}Setting up migration tracking...${NC}"
    execute_sql "
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version VARCHAR(14) PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            applied_at TIMESTAMP DEFAULT NOW(),
            checksum VARCHAR(64),
            rollback_sql TEXT
        );
    " "Creating migration tracking table"
    echo -e "${GREEN}✓ Migration tracking ready${NC}"
}

# Function to check if migration was applied
is_migration_applied() {
    local version=$1
    local count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM schema_migrations WHERE version = '$version';
    " 2>/dev/null | tr -d ' ')
    
    [ "$count" -gt 0 ]
}

# Function to record migration
record_migration() {
    local version=$1
    local migration_file="$2"
    local rollback_sql="$3"
    local filename=$(basename "$migration_file")
    local checksum=""
    
    if [ -f "$migration_file" ]; then
        checksum=$(md5sum "$migration_file" | cut -d' ' -f1)
    fi
    
    execute_sql "
        INSERT INTO schema_migrations (version, filename, checksum, rollback_sql) 
        VALUES ('$version', '$filename', '$checksum', '$rollback_sql')
        ON CONFLICT (version) DO UPDATE SET
            applied_at = NOW(),
            filename = EXCLUDED.filename,
            checksum = EXCLUDED.checksum,
            rollback_sql = EXCLUDED.rollback_sql;
    " "Recording migration $version"
}

# Function to remove migration record
remove_migration_record() {
    local version=$1
    execute_sql "DELETE FROM schema_migrations WHERE version = '$version';" "Removing migration record $version"
}

# Function to get applied migrations
get_applied_migrations() {
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT version FROM schema_migrations ORDER BY version;
    " 2>/dev/null | tr -d ' ' | grep -v '^$'
}

# Function to get rollback SQL for a version
get_rollback_sql() {
    local version=$1
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT rollback_sql FROM schema_migrations WHERE version = '$version';
    " 2>/dev/null | tr -d ' ' | grep -v '^$'
}

# Function to apply single migration
apply_migration() {
    local version=$1
    local migration_file=$(get_file_from_version "$version")
    
    if [ -z "$migration_file" ] || [ ! -f "$migration_file" ]; then
        echo -e "${RED}✗ Migration file not found for version: $version${NC}"
        return 1
    fi
    
    if is_migration_applied "$version"; then
        echo -e "${YELLOW}⚠ Migration $version already applied${NC}"
        return 0
    fi
    
    local filename=$(basename "$migration_file")
    echo -e "${BLUE}Applying migration $version: $filename${NC}"
    
    if execute_sql_file "$migration_file" "Executing $filename"; then
        local rollback_sql=$(generate_rollback_sql "$migration_file")
        record_migration "$version" "$migration_file" "$rollback_sql"
        echo -e "${GREEN}✓ Successfully applied migration $version${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to apply migration $version${NC}"
        return 1
    fi
}

# Function to rollback single migration
rollback_migration() {
    local version=$1
    
    if ! is_migration_applied "$version"; then
        echo -e "${YELLOW}⚠ Migration $version not applied, skipping rollback${NC}"
        return 0
    fi
    
    local rollback_sql=$(get_rollback_sql "$version")
    
    if [ -z "$rollback_sql" ]; then
        echo -e "${RED}✗ No rollback SQL found for migration $version${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Rolling back migration $version${NC}"
    
    if execute_sql "$rollback_sql" "Executing rollback for $version"; then
        remove_migration_record "$version"
        echo -e "${GREEN}✓ Successfully rolled back migration $version${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to rollback migration $version${NC}"
        return 1
    fi
}

# Function to migrate up
migrate_up() {
    # Temporarily disable exit on error for this function
    set +e
    local target_version=$1
    
    echo -e "${BLUE}Running migrations up${NC}"
    if [ -n "$target_version" ]; then
        echo -e "${BLUE}Target version: $target_version${NC}"
    fi
    echo ""
    
    local success_count=0
    local total_count=0
    local already_applied_count=0
    
    local versions_output
    versions_output=$(get_migration_versions asc)
    
    # Check if all migrations are already applied
    local all_applied=true
    while read -r version; do
        if [ -z "$version" ]; then continue; fi
        if ! is_migration_applied "$version"; then
            all_applied=false
            break
        fi
    done <<< "$versions_output"
    
    if [ "$all_applied" = true ]; then
        echo -e "${GREEN}✓ All migrations are already applied${NC}"
        echo ""
        echo -e "${GREEN}Applied 0 new migrations (10 already applied)${NC}"
        set -e  # Re-enable exit on error
        return 0
    fi
    
    while read -r version; do
        if [ -z "$version" ]; then continue; fi
        
        echo -e "${BLUE}Processing version: $version${NC}"
        ((total_count++))
        
        if [ -n "$target_version" ] && [ "$version" \> "$target_version" ]; then
            break
        fi
        
        if is_migration_applied "$version"; then
            echo -e "${YELLOW}⚠ Migration $version already applied${NC}"
            ((already_applied_count++))
            ((success_count++))
        else
            if apply_migration "$version"; then
                ((success_count++))
            else
                echo -e "${RED}Migration failed. Stopping execution.${NC}"
                set -e  # Re-enable exit on error
                return 1
            fi
        fi
        
        if [ -n "$target_version" ] && [ "$version" = "$target_version" ]; then
            break
        fi
    done <<< "$versions_output"
    
    echo ""
    local new_migrations=$((success_count - already_applied_count))
    echo -e "${GREEN}Applied $new_migrations new migrations ($already_applied_count already applied)${NC}"
    set -e  # Re-enable exit on error
    return 0
}

# Function to migrate down
migrate_down() {
    local target_version=$1
    local steps=${2:-1}
    
    echo -e "${BLUE}Running migrations down${NC}"
    if [ -n "$target_version" ]; then
        echo -e "${BLUE}Target version: $target_version${NC}"
    else
        echo -e "${BLUE}Rolling back $steps step(s)${NC}"
    fi
    echo ""
    
    local success_count=0
    local step_count=0
    
    while read -r version; do
        if [ -z "$version" ]; then continue; fi
        
        if [ -n "$target_version" ]; then
            if [ "$version" \< "$target_version" ]; then
                break
            fi
        else
            if [ $step_count -ge $steps ]; then
                break
            fi
        fi
        
        if is_migration_applied "$version"; then
            if rollback_migration "$version"; then
                ((success_count++))
                ((step_count++))
            else
                echo -e "${RED}Rollback failed. Stopping execution.${NC}"
                break
            fi
        fi
    done <<< "$(get_migration_versions desc)"
    
    echo ""
    echo -e "${GREEN}Rolled back $success_count migrations${NC}"
}

# Function to show migration status
show_status() {
    echo -e "${BLUE}=== Migration Status ===${NC}"
    echo ""
    
    local applied_migrations=$(get_applied_migrations)
    
    echo -e "${BLUE}Available migrations:${NC}"
    while read -r version; do
        if [ -z "$version" ]; then continue; fi
        
        local migration_file=$(get_file_from_version "$version")
        local filename=$(basename "$migration_file")
        local status="❌ Not Applied"
        
        if echo "$applied_migrations" | grep -q "^$version$"; then
            status="✅ Applied"
        fi
        
        echo -e "  $version: $filename - $status"
    done <<< "$(get_migration_versions asc)"
    
    echo ""
    if [ -n "$applied_migrations" ]; then
        echo -e "${GREEN}Applied migrations:${NC}"
        echo "$applied_migrations" | while read -r version; do
            if [ -n "$version" ]; then
                echo -e "  ✅ $version"
            fi
        done
    else
        echo -e "${YELLOW}No migrations applied${NC}"
    fi
}

# Function to list available migrations
list_migrations() {
    echo -e "${BLUE}=== Available Migrations ===${NC}"
    echo ""
    
    while read -r version; do
        if [ -z "$version" ]; then continue; fi
        
        local migration_file=$(get_file_from_version "$version")
        local filename=$(basename "$migration_file")
        echo -e "  ${BLUE}$version${NC}: $filename"
    done <<< "$(get_migration_versions asc)"
}

# Function to show help
show_help() {
    echo "Advanced Migration Management System"
    echo "Automatically discovers migrations by naming convention: NNNN_description.sql"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  up [version]       Apply migrations up to specified version (or all)"
    echo "  down [version]     Rollback migrations down to specified version"
    echo "  down [steps]       Rollback specified number of migrations"
    echo "  status             Show current migration status"
    echo "  list               List all available migrations"
    echo "  reset              Rollback all migrations"
    echo ""
    echo "Examples:"
    echo "  $0 up              # Apply all migrations"
    echo "  $0 up 0005         # Apply migrations up to 0005"
    echo "  $0 down 0003       # Rollback to migration 0003"
    echo "  $0 down 2          # Rollback last 2 migrations"
    echo "  $0 status          # Show migration status"
    echo "  $0 list            # List available migrations"
    echo "  $0 reset           # Rollback all migrations"
    echo ""
    echo "Migration Discovery:"
    echo "  - Automatically finds *.sql files matching pattern: NNNN_*.sql"
    echo "  - Sorts by version number (first 4 digits)"
    echo "  - Generates rollback SQL automatically"
    echo ""
    echo "Environment Variables:"
    echo "  POSTGRES_HOST      Database host (default: localhost)"
    echo "  POSTGRES_PORT      Database port (default: 5432)"
    echo "  POSTGRES_USER      Database user (default: postgres)"
    echo "  POSTGRES_PASSWORD  Database password (default: postgres)"
    echo "  POSTGRES_DB        Database name (default: continuia_clean)"
}

# Main execution
main() {
    local command=${1:-help}
    local arg2=$2
    
    case "$command" in
        up)
            if ! check_connection; then exit 1; fi
            create_migration_table
            migrate_up "$arg2"
            ;;
        down)
            if ! check_connection; then exit 1; fi
            create_migration_table
            if [[ "$arg2" =~ ^[0-9]+$ ]]; then
                migrate_down "" "$arg2"
            else
                migrate_down "$arg2"
            fi
            ;;
        status)
            if ! check_connection; then exit 1; fi
            create_migration_table
            show_status
            ;;
        list)
            list_migrations
            ;;
        reset)
            if ! check_connection; then exit 1; fi
            create_migration_table
            echo -e "${YELLOW}⚠ This will rollback ALL migrations. Are you sure? (y/N)${NC}"
            read -r confirmation
            if [[ "$confirmation" =~ ^[Yy]$ ]]; then
                migrate_down "0000"
            else
                echo -e "${BLUE}Operation cancelled${NC}"
            fi
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}Unknown command: $command${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"