#!/bin/bash

# Clean Drizzle Migrations Runner
# This script applies the clean, structured migrations to a fresh database

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST=${POSTGRES_HOST:-localhost}
DB_PORT=${POSTGRES_PORT:-5432}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASSWORD=${POSTGRES_PASSWORD:-postgres}
DB_NAME=${POSTGRES_DB:-continuia_clean}

# Migration files in order
MIGRATIONS=(
    "0001_foundation_users_and_auth.sql"
    "0002_foundation_audit_and_profiles.sql"
    "0003_core_cases_and_documents.sql"
    "0004_core_appointments_and_opinions.sql"
    "0005_advanced_case_management.sql"
    "0006_advanced_legal_compliance.sql"
    "0007_crm_organizations_and_contacts.sql"
    "0008_crm_leads_and_activities.sql"
    "0009_specializations_system.sql"
    "0010_credentials_system.sql"
)

echo -e "${BLUE}=== Clean Drizzle Migrations Runner ===${NC}"
echo -e "${YELLOW}Target Database: ${DB_HOST}:${DB_PORT}/${DB_NAME}${NC}"
echo ""

# Function to execute SQL file
execute_migration() {
    local migration_file=$1
    local migration_name=$(basename "$migration_file" .sql)
    
    echo -e "${BLUE}Applying migration: ${migration_name}${NC}"
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$migration_file" -v ON_ERROR_STOP=1 > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Successfully applied: ${migration_name}${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to apply: ${migration_name}${NC}"
        return 1
    fi
}

# Function to check database connection
check_connection() {
    echo -e "${BLUE}Checking database connection...${NC}"
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Database connection successful${NC}"
        return 0
    else
        echo -e "${RED}✗ Cannot connect to database${NC}"
        echo -e "${YELLOW}Please ensure the database exists and connection parameters are correct${NC}"
        return 1
    fi
}

# Function to create migration tracking table
create_migration_table() {
    echo -e "${BLUE}Creating migration tracking table...${NC}"
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        CREATE TABLE IF NOT EXISTS migration_history (
            id SERIAL PRIMARY KEY,
            migration_name VARCHAR(255) NOT NULL UNIQUE,
            applied_at TIMESTAMP DEFAULT NOW(),
            checksum VARCHAR(64)
        );
    " > /dev/null 2>&1
    echo -e "${GREEN}✓ Migration tracking table ready${NC}"
}

# Function to check if migration was already applied
is_migration_applied() {
    local migration_name=$1
    local count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM migration_history WHERE migration_name = '$migration_name';
    " 2>/dev/null | tr -d ' ')
    
    if [ "$count" -gt 0 ]; then
        return 0  # Migration already applied
    else
        return 1  # Migration not applied
    fi
}

# Function to record migration
record_migration() {
    local migration_name=$1
    local checksum=$(md5sum "$migration_name.sql" | cut -d' ' -f1)
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        INSERT INTO migration_history (migration_name, checksum) 
        VALUES ('$migration_name', '$checksum')
        ON CONFLICT (migration_name) DO NOTHING;
    " > /dev/null 2>&1
}

# Main execution
main() {
    echo -e "${BLUE}Starting migration process...${NC}"
    echo ""
    
    # Check connection
    if ! check_connection; then
        exit 1
    fi
    
    # Create migration tracking table
    create_migration_table
    
    echo ""
    echo -e "${BLUE}Applying migrations in order...${NC}"
    echo ""
    
    # Apply migrations
    local success_count=0
    local total_count=${#MIGRATIONS[@]}
    
    for migration in "${MIGRATIONS[@]}"; do
        local migration_name=$(basename "$migration" .sql)
        
        if is_migration_applied "$migration_name"; then
            echo -e "${YELLOW}⚠ Migration already applied: ${migration_name}${NC}"
            ((success_count++))
            continue
        fi
        
        if execute_migration "$migration"; then
            record_migration "$migration_name"
            ((success_count++))
        else
            echo -e "${RED}Migration failed. Stopping execution.${NC}"
            break
        fi
    done
    
    echo ""
    echo -e "${BLUE}=== Migration Summary ===${NC}"
    echo -e "${GREEN}Successfully applied: ${success_count}/${total_count} migrations${NC}"
    
    if [ $success_count -eq $total_count ]; then
        echo -e "${GREEN}🎉 All migrations completed successfully!${NC}"
        echo ""
        echo -e "${BLUE}Next steps:${NC}"
        echo -e "1. Verify the database schema matches your expectations"
        echo -e "2. Run any data seeding scripts if needed"
        echo -e "3. Update your application's database connection to point to this clean database"
        echo ""
        exit 0
    else
        echo -e "${RED}❌ Some migrations failed. Please check the errors above.${NC}"
        exit 1
    fi
}

# Help function
show_help() {
    echo "Clean Drizzle Migrations Runner"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  --dry-run      Show what would be executed without running"
    echo ""
    echo "Environment Variables:"
    echo "  POSTGRES_HOST      Database host (default: localhost)"
    echo "  POSTGRES_PORT      Database port (default: 5432)"
    echo "  POSTGRES_USER      Database user (default: postgres)"
    echo "  POSTGRES_PASSWORD  Database password (default: postgres)"
    echo "  POSTGRES_DB        Database name (default: continuia_clean)"
    echo ""
    echo "Example:"
    echo "  POSTGRES_DB=my_test_db ./run-migrations.sh"
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --dry-run)
        echo -e "${BLUE}=== DRY RUN MODE ===${NC}"
        echo -e "${YELLOW}The following migrations would be applied:${NC}"
        echo ""
        for migration in "${MIGRATIONS[@]}"; do
            echo -e "${BLUE}  - $migration${NC}"
        done
        echo ""
        echo -e "${YELLOW}Database: ${DB_HOST}:${DB_PORT}/${DB_NAME}${NC}"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        echo -e "${RED}Unknown option: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac