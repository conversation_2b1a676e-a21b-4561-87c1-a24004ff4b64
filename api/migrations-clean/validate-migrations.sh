#!/bin/bash

# Migration Validation Script
# Automatically discovers and validates migration files by naming convention

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST=${POSTGRES_HOST:-localhost}
DB_PORT=${POSTGRES_PORT:-5432}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASSWORD=${POSTGRES_PASSWORD:-postgres}
DB_NAME=${POSTGRES_DB:-continuia_test_validation}

# Migration directory
MIGRATION_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to discover migration files by naming convention
discover_migrations() {
    find "$MIGRATION_DIR" -name "[0-9][0-9][0-9][0-9]_*.sql" -type f | sort
}

# Validation results
declare -a VALIDATION_ERRORS
declare -a VALIDATION_WARNINGS
declare -a VALIDATION_SUCCESS

# Function to log validation result
log_result() {
    local type=$1
    local message=$2
    
    case $type in
        "error")
            VALIDATION_ERRORS+=("$message")
            echo -e "${RED}✗ ERROR: $message${NC}"
            ;;
        "warning")
            VALIDATION_WARNINGS+=("$message")
            echo -e "${YELLOW}⚠ WARNING: $message${NC}"
            ;;
        "success")
            VALIDATION_SUCCESS+=("$message")
            echo -e "${GREEN}✓ $message${NC}"
            ;;
        "info")
            echo -e "${BLUE}ℹ $message${NC}"
            ;;
    esac
}

# Function to validate file existence and discovery
validate_file_discovery() {
    echo -e "${BLUE}=== Validating Migration Discovery ===${NC}"
    
    local migrations=($(discover_migrations))
    local count=${#migrations[@]}
    
    if [ $count -eq 0 ]; then
        log_result "error" "No migration files found matching pattern NNNN_*.sql"
        return 1
    fi
    
    log_result "success" "Discovered $count migration files"
    
    for migration in "${migrations[@]}"; do
        local filename=$(basename "$migration")
        log_result "success" "Found migration: $filename"
    done
    echo ""
}

# Function to validate naming convention
validate_naming_convention() {
    echo -e "${BLUE}=== Validating Naming Convention ===${NC}"
    
    local expected_pattern="^[0-9]{4}_[a-z_]+\.sql$"
    local migrations=($(discover_migrations))
    
    for migration in "${migrations[@]}"; do
        local filename=$(basename "$migration")
        if [[ "$filename" =~ $expected_pattern ]]; then
            log_result "success" "Migration follows naming convention: $filename"
        else
            log_result "warning" "Migration doesn't follow convention: $filename"
        fi
    done
    echo ""
}

# Function to validate version sequence
validate_version_sequence() {
    echo -e "${BLUE}=== Validating Version Sequence ===${NC}"
    
    local migrations=($(discover_migrations))
    local prev_version=""
    local has_gaps=false
    
    for migration in "${migrations[@]}"; do
        local filename=$(basename "$migration")
        local version=$(echo "$filename" | cut -d'_' -f1)
        
        if [ -n "$prev_version" ]; then
            local prev_num=$(echo "$prev_version" | sed 's/^0*//')
            local curr_num=$(echo "$version" | sed 's/^0*//')
            
            if [ $((curr_num - prev_num)) -gt 1 ]; then
                log_result "warning" "Version gap detected: $prev_version -> $version"
                has_gaps=true
            fi
        fi
        
        prev_version="$version"
    done
    
    if [ "$has_gaps" = false ]; then
        log_result "success" "No version gaps detected"
    fi
    echo ""
}

# Function to validate SQL syntax
validate_sql_syntax() {
    echo -e "${BLUE}=== Validating SQL Syntax ===${NC}"
    
    # Create temporary database for syntax validation
    local temp_db="${DB_NAME}_syntax_test"
    local migrations=($(discover_migrations))
    
    # Create temp database
    if PGPASSWORD="$DB_PASSWORD" createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$temp_db" 2>/dev/null; then
        log_result "info" "Created temporary database: $temp_db"
        
        for migration in "${migrations[@]}"; do
            local filename=$(basename "$migration")
            if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$temp_db" -f "$migration" -v ON_ERROR_STOP=1 > /dev/null 2>&1; then
                log_result "success" "SQL syntax valid: $filename"
            else
                log_result "error" "SQL syntax error in: $filename"
            fi
        done
        
        # Clean up temp database
        PGPASSWORD="$DB_PASSWORD" dropdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$temp_db" 2>/dev/null
        log_result "info" "Cleaned up temporary database"
    else
        log_result "warning" "Could not create temporary database for syntax validation"
    fi
    echo ""
}

# Function to validate migration dependencies
validate_dependencies() {
    echo -e "${BLUE}=== Validating Dependencies ===${NC}"
    
    local migrations=($(discover_migrations))
    
    # Check for common dependency patterns
    for migration in "${migrations[@]}"; do
        local filename=$(basename "$migration")
        
        # Check for foreign key references to tables that should exist
        local fk_refs=$(grep -i "REFERENCES" "$migration" | grep -o "REFERENCES [a-zA-Z_][a-zA-Z0-9_]*" | cut -d' ' -f2 | sort -u)
        
        if [ -n "$fk_refs" ]; then
            log_result "info" "Foreign key dependencies in $filename:"
            echo "$fk_refs" | while read -r table; do
                if [ -n "$table" ]; then
                    echo -e "    ${BLUE}→ $table${NC}"
                fi
            done
        fi
    done
    echo ""
}

# Function to validate enum consistency
validate_enums() {
    echo -e "${BLUE}=== Validating Enum Consistency ===${NC}"
    
    local migrations=($(discover_migrations))
    
    # Extract all enum definitions
    local enum_file="/tmp/migration_enums.txt"
    > "$enum_file"
    
    for migration in "${migrations[@]}"; do
        grep -i "CREATE TYPE.*AS ENUM" "$migration" >> "$enum_file" 2>/dev/null || true
    done
    
    if [ -s "$enum_file" ]; then
        log_result "info" "Found enum definitions:"
        while read -r enum_def; do
            local enum_name=$(echo "$enum_def" | grep -o '"[^"]*"' | head -1 | tr -d '"')
            if [ -n "$enum_name" ]; then
                echo -e "    ${BLUE}→ $enum_name${NC}"
            fi
        done < "$enum_file"
        
        # Check for duplicate enum names
        local duplicates=$(grep -o '"[^"]*"' "$enum_file" | head -n -1 | sort | uniq -d)
        if [ -n "$duplicates" ]; then
            log_result "warning" "Potential duplicate enum names found:"
            echo "$duplicates" | while read -r dup; do
                echo -e "    ${YELLOW}→ $dup${NC}"
            done
        else
            log_result "success" "No duplicate enum names found"
        fi
    else
        log_result "info" "No enum definitions found"
    fi
    
    rm -f "$enum_file"
    echo ""
}

# Function to validate table creation order
validate_table_order() {
    echo -e "${BLUE}=== Validating Table Creation Order ===${NC}"
    
    local migrations=($(discover_migrations))
    local core_tables=("users" "roles" "permissions")
    local found_core=false
    
    for migration in "${migrations[@]}"; do
        local filename=$(basename "$migration")
        
        for table in "${core_tables[@]}"; do
            if grep -q "CREATE TABLE.*$table" "$migration"; then
                if [ "$found_core" = false ]; then
                    log_result "success" "Core tables found in early migration: $filename"
                    found_core=true
                fi
                break
            fi
        done
    done
    
    if [ "$found_core" = false ]; then
        log_result "warning" "Core tables (users, roles, permissions) not found in early migrations"
    fi
    echo ""
}

# Function to check for common issues
validate_common_issues() {
    echo -e "${BLUE}=== Checking for Common Issues ===${NC}"
    
    local migrations=($(discover_migrations))
    
    for migration in "${migrations[@]}"; do
        local filename=$(basename "$migration")
        
        # Check for missing IF NOT EXISTS
        local create_tables=$(grep -c "CREATE TABLE" "$migration" 2>/dev/null || echo "0")
        local if_not_exists=$(grep -c "CREATE TABLE IF NOT EXISTS" "$migration" 2>/dev/null || echo "0")
        
        if [ "$create_tables" -gt 0 ] && [ "$if_not_exists" -lt "$create_tables" ]; then
            log_result "warning" "$filename: Some CREATE TABLE statements missing 'IF NOT EXISTS'"
        fi
        
        # Check for missing CASCADE in foreign keys
        local fk_count=$(grep -c "FOREIGN KEY" "$migration" 2>/dev/null || echo "0")
        local cascade_count=$(grep -c "ON DELETE\|ON UPDATE" "$migration" 2>/dev/null || echo "0")
        
        if [ "$fk_count" -gt 0 ] && [ "$cascade_count" -eq 0 ]; then
            log_result "warning" "$filename: Foreign keys without CASCADE behavior specified"
        fi
        
        # Check for indexes
        local index_count=$(grep -c "CREATE INDEX" "$migration" 2>/dev/null || echo "0")
        if [ "$index_count" -gt 0 ]; then
            log_result "success" "$filename: Contains $index_count index definitions"
        fi
        
        # Check file size (very large files might indicate issues)
        local file_size=$(stat -f%z "$migration" 2>/dev/null || stat -c%s "$migration" 2>/dev/null || echo "0")
        if [ "$file_size" -gt 100000 ]; then  # 100KB
            log_result "warning" "$filename: Large migration file (${file_size} bytes) - consider splitting"
        fi
    done
    echo ""
}

# Function to validate rollback generation
validate_rollback_generation() {
    echo -e "${BLUE}=== Validating Rollback Generation ===${NC}"
    
    local migrations=($(discover_migrations))
    
    for migration in "${migrations[@]}"; do
        local filename=$(basename "$migration")
        
        # Check if migration creates objects that can be rolled back
        local creates_tables=$(grep -c "CREATE TABLE" "$migration" 2>/dev/null || echo "0")
        local creates_enums=$(grep -c "CREATE TYPE.*AS ENUM" "$migration" 2>/dev/null || echo "0")
        local creates_views=$(grep -c "CREATE.*VIEW" "$migration" 2>/dev/null || echo "0")
        local creates_functions=$(grep -c "CREATE.*FUNCTION" "$migration" 2>/dev/null || echo "0")
        
        local total_creates=0
        if [ "$creates_tables" -gt 0 ] 2>/dev/null; then
            total_creates=$((total_creates + creates_tables))
        fi
        if [ "$creates_enums" -gt 0 ] 2>/dev/null; then
            total_creates=$((total_creates + creates_enums))
        fi
        if [ "$creates_views" -gt 0 ] 2>/dev/null; then
            total_creates=$((total_creates + creates_views))
        fi
        if [ "$creates_functions" -gt 0 ] 2>/dev/null; then
            total_creates=$((total_creates + creates_functions))
        fi
        
        if [ "$total_creates" -gt 0 ]; then
            log_result "success" "$filename: Creates $total_creates objects (rollback possible)"
        else
            log_result "info" "$filename: No CREATE statements found"
        fi
    done
    echo ""
}

# Function to generate validation report
generate_report() {
    echo -e "${BLUE}=== Validation Report ===${NC}"
    echo ""
    
    local migrations=($(discover_migrations))
    local migration_count=${#migrations[@]}
    
    echo -e "${BLUE}Migration Discovery:${NC}"
    echo -e "  Found: $migration_count migration files"
    echo -e "  Pattern: NNNN_*.sql"
    echo -e "  Directory: $MIGRATION_DIR"
    echo ""
    
    echo -e "${GREEN}Successful validations: ${#VALIDATION_SUCCESS[@]}${NC}"
    echo -e "${YELLOW}Warnings: ${#VALIDATION_WARNINGS[@]}${NC}"
    echo -e "${RED}Errors: ${#VALIDATION_ERRORS[@]}${NC}"
    echo ""
    
    if [ ${#VALIDATION_ERRORS[@]} -gt 0 ]; then
        echo -e "${RED}ERRORS:${NC}"
        for error in "${VALIDATION_ERRORS[@]}"; do
            echo -e "  ${RED}• $error${NC}"
        done
        echo ""
    fi
    
    if [ ${#VALIDATION_WARNINGS[@]} -gt 0 ]; then
        echo -e "${YELLOW}WARNINGS:${NC}"
        for warning in "${VALIDATION_WARNINGS[@]}"; do
            echo -e "  ${YELLOW}• $warning${NC}"
        done
        echo ""
    fi
    
    if [ ${#VALIDATION_ERRORS[@]} -eq 0 ]; then
        echo -e "${GREEN}🎉 All critical validations passed!${NC}"
        echo -e "${BLUE}Migrations are ready for deployment.${NC}"
        return 0
    else
        echo -e "${RED}❌ Validation failed with ${#VALIDATION_ERRORS[@]} error(s).${NC}"
        echo -e "${BLUE}Please fix the errors before deploying migrations.${NC}"
        return 1
    fi
}

# Function to show help
show_help() {
    echo "Migration Validation Script"
    echo "Automatically discovers and validates migrations by naming convention"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --syntax-only    Only validate SQL syntax"
    echo "  --quick          Skip database-dependent validations"
    echo "  -h, --help       Show this help message"
    echo ""
    echo "Migration Discovery:"
    echo "  - Automatically finds *.sql files matching pattern: NNNN_*.sql"
    echo "  - Validates naming convention and version sequence"
    echo "  - No hardcoded migration lists required"
    echo ""
    echo "Environment Variables:"
    echo "  POSTGRES_HOST      Database host (default: localhost)"
    echo "  POSTGRES_PORT      Database port (default: 5432)"
    echo "  POSTGRES_USER      Database user (default: postgres)"
    echo "  POSTGRES_PASSWORD  Database password (default: postgres)"
    echo "  POSTGRES_DB        Database name (default: continuia_test_validation)"
}

# Main execution
main() {
    local syntax_only=false
    local quick_mode=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --syntax-only)
                syntax_only=true
                shift
                ;;
            --quick)
                quick_mode=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}Unknown option: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo -e "${BLUE}=== Migration Validation Started ===${NC}"
    echo -e "${YELLOW}Target: $MIGRATION_DIR${NC}"
    echo ""
    
    # Always run these validations
    validate_file_discovery
    validate_naming_convention
    validate_version_sequence
    validate_dependencies
    validate_enums
    validate_table_order
    validate_common_issues
    validate_rollback_generation
    
    # Database-dependent validations
    if [ "$syntax_only" = true ]; then
        echo -e "${BLUE}Skipping database validations (syntax-only mode)${NC}"
    elif [ "$quick_mode" = true ]; then
        echo -e "${BLUE}Skipping database validations (quick mode)${NC}"
    else
        # Check database connection
        if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "SELECT 1;" > /dev/null 2>&1; then
            validate_sql_syntax
        else
            log_result "warning" "Cannot connect to database - skipping syntax validation"
        fi
    fi
    
    # Generate final report
    generate_report
}

# Execute main function
main "$@"