{"name": "continuia-api", "version": "1.0.0", "description": "Continuia Healthcare Platform API", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch -r tsconfig-paths/register src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio", "db:seed": "tsx src/scripts/seed.ts", "db:seed-complete": "tsx src/scripts/seed-complete-database.ts", "db:seed-test-data": "tsx src/scripts/wipe-and-seed-test-data.ts", "db:auto-init": "tsx src/scripts/auto-initialize-database.ts", "db:validate": "tsx src/scripts/validate-db-config.ts", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {"@types/winston": "^2.4.4", "@types/ws": "^8.18.1", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "drizzle-orm": "^0.29.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "minio": "^8.0.5", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "nodemailer": "^6.9.8", "postgres": "^3.4.3", "rate-limiter-flexible": "^7.3.0", "socket.io": "^4.8.1", "uuid": "^9.0.1", "winston": "^3.17.0", "ws": "^8.18.3", "y-websocket": "^3.0.0", "yjs": "^13.6.27", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.13", "@types/node": "^20.9.0", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "axios": "^1.11.0", "drizzle-kit": "^0.20.6", "eslint": "^8.53.0", "form-data": "^4.0.4", "prettier": "^3.1.0", "tsconfig-paths": "^4.2.0", "tsx": "^4.1.4", "typescript": "^5.2.2"}}