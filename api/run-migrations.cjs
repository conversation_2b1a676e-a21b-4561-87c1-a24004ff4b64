#!/usr/bin/env node

/**
 * Migration Runner for Docker Environment
 * Executes migration scripts from within the container
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const MIGRATION_DIR = path.join(__dirname, 'migrations-clean');
const DATABASE_URL = process.env.DATABASE_URL || '********************************************/continuia';

// Parse DATABASE_URL for legacy script compatibility
const url = new URL(DATABASE_URL);
const POSTGRES_HOST = url.hostname;
const POSTGRES_PORT = url.port || '5432';
const POSTGRES_USER = url.username;
const POSTGRES_PASSWORD = url.password;
const POSTGRES_DB = url.pathname.slice(1); // Remove leading slash

// Colors for output
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function log(color, message) {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// Check if a command is available
function checkCommandAvailable(command) {
    try {
        execSync(`which ${command}`, { stdio: 'ignore' });
        return true;
    } catch (error) {
        return false;
    }
}

// Install required packages
function installRequiredPackages() {
    const packages = [];
    
    if (!checkCommandAvailable('psql')) {
        packages.push('postgresql-client');
    }
    
    if (!checkCommandAvailable('bash')) {
        packages.push('bash');
    }
    
    if (packages.length > 0) {
        log('blue', `Installing required packages: ${packages.join(', ')}...`);
        try {
            execSync(`apk add --no-cache ${packages.join(' ')}`, { stdio: 'inherit' });
            log('green', '✓ Required packages installed');
            return true;
        } catch (error) {
            log('red', '✗ Failed to install required packages');
            return false;
        }
    } else {
        log('green', '✓ All required packages available');
        return true;
    }
}

// Make migration scripts executable
function makeScriptsExecutable() {
    try {
        if (fs.existsSync(path.join(MIGRATION_DIR, 'migrate.sh'))) {
            execSync(`chmod +x ${MIGRATION_DIR}/*.sh`, { stdio: 'ignore' });
            log('green', '✓ Migration scripts made executable');
            return true;
        } else {
            log('red', '✗ Migration scripts not found');
            return false;
        }
    } catch (error) {
        log('red', '✗ Failed to make scripts executable');
        return false;
    }
}

// Execute migration command
function executeMigration(command, args = []) {
    const migrationScript = path.join(MIGRATION_DIR, 'migrate.sh');
    
    if (!fs.existsSync(migrationScript)) {
        log('red', '✗ Migration script not found at: ' + migrationScript);
        process.exit(1);
    }

    const env = {
        ...process.env,
        POSTGRES_HOST,
        POSTGRES_PORT,
        POSTGRES_USER,
        POSTGRES_PASSWORD,
        POSTGRES_DB
    };

    log('blue', `Executing: bash ${migrationScript} ${command} ${args.join(' ')}`);
    
    try {
        const result = spawn('bash', [migrationScript, command, ...args], {
            stdio: 'inherit',
            env,
            cwd: MIGRATION_DIR
        });

        result.on('close', (code) => {
            if (code === 0) {
                log('green', '✓ Migration command completed successfully');
                process.exit(0);
            } else {
                log('red', `✗ Migration command failed with exit code ${code}`);
                process.exit(code);
            }
        });

        result.on('error', (error) => {
            log('red', `✗ Failed to execute migration: ${error.message}`);
            process.exit(1);
        });

    } catch (error) {
        log('red', `✗ Error executing migration: ${error.message}`);
        process.exit(1);
    }
}

// Execute validation command
function executeValidation(args = []) {
    const validationScript = path.join(MIGRATION_DIR, 'validate-migrations.sh');
    
    if (!fs.existsSync(validationScript)) {
        log('red', '✗ Validation script not found at: ' + validationScript);
        process.exit(1);
    }

    const env = {
        ...process.env,
        POSTGRES_HOST,
        POSTGRES_PORT,
        POSTGRES_USER,
        POSTGRES_PASSWORD,
        POSTGRES_DB
    };

    log('blue', `Executing: bash ${validationScript} ${args.join(' ')}`);
    
    try {
        const result = spawn('bash', [validationScript, ...args], {
            stdio: 'inherit',
            env,
            cwd: MIGRATION_DIR
        });

        result.on('close', (code) => {
            if (code === 0) {
                log('green', '✓ Validation completed successfully');
                process.exit(0);
            } else {
                log('red', `✗ Validation failed with exit code ${code}`);
                process.exit(code);
            }
        });

        result.on('error', (error) => {
            log('red', `✗ Failed to execute validation: ${error.message}`);
            process.exit(1);
        });

    } catch (error) {
        log('red', `✗ Error executing validation: ${error.message}`);
        process.exit(1);
    }
}

// Main function
function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log(`
Migration Runner for Docker Environment

Usage: node run-migrations.cjs <command> [options]

Migration Commands:
  up [version]       Apply migrations up to specified version (or all)
  down [version]     Rollback migrations down to specified version
  down [steps]       Rollback specified number of migrations
  status             Show current migration status
  list               List all available migrations
  reset              Rollback all migrations

Validation Commands:
  validate           Full validation
  validate --quick   Quick validation
  validate --syntax-only  Syntax only validation

Examples:
  node run-migrations.cjs up
  node run-migrations.cjs status
  node run-migrations.cjs validate --quick
  node run-migrations.cjs down 2

Environment Variables:
  POSTGRES_HOST      Database host (default: postgres)
  POSTGRES_PORT      Database port (default: 5432)
  POSTGRES_USER      Database user (default: postgres)
  POSTGRES_PASSWORD  Database password (default: postgres)
  POSTGRES_DB        Database name (default: continuia)
        `);
        process.exit(0);
    }

    // Setup
    log('blue', '=== Migration Runner Setup ===');
    
    if (!installRequiredPackages()) {
        process.exit(1);
    }

    if (!makeScriptsExecutable()) {
        process.exit(1);
    }

    log('blue', `Database: ${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}`);
    log('blue', '');

    // Execute command
    const command = args[0];
    const commandArgs = args.slice(1);

    if (command === 'validate') {
        executeValidation(commandArgs);
    } else {
        executeMigration(command, commandArgs);
    }
}

// Handle process termination
process.on('SIGINT', () => {
    log('yellow', '\nMigration interrupted by user');
    process.exit(1);
});

process.on('SIGTERM', () => {
    log('yellow', '\nMigration terminated');
    process.exit(1);
});

// Run main function
main();