#!/bin/bash
set -e

echo "🔄 Starting Continuia API with automatic migrations..."
echo "⏳ Waiting for database to be ready..."
echo ""

# Wait for database to be ready
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
  if PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database is ready!"
    break
  fi
  echo "⏳ Waiting for database... (attempt $attempt/$max_attempts)"
  sleep 2
  attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
  echo "❌ Failed to connect to database after $max_attempts attempts"
  exit 1
fi

echo ""
# Run migrations
echo "🔄 Running database migrations..."
cd /app/migrations-clean
if ./migrate.sh up; then
  echo "✅ Migrations completed successfully!"
else
  echo "❌ Migration failed!"
  exit 1
fi

echo ""
# Start the application
echo "🚀 Starting API server..."
cd /app
exec npm run dev