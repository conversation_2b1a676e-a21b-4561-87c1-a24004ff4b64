import { sql } from 'drizzle-orm';
import { pgTable, text, timestamp, varchar, boolean, uuid, jsonb } from "drizzle-orm/pg-core";

import { logger } from '../utils/structuredLogger';
export async function up(db: any) {
  // Update user_role enum to include CRM roles
  await db.execute(sql`
    ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'Admin';
    ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'Sales';
    ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'Ops';
    ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'Agent';
    ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'Exec';
  `);

  // Create teams table
  await db.execute(sql`
    CREATE TABLE teams (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(100) NOT NULL,
      description TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create lead_types enum
  await db.execute(sql`
    CREATE TYPE lead_type AS ENUM ('individual', 'organization', 'referral');
  `);

  // Create lead_sources enum
  await db.execute(sql`
    CREATE TYPE lead_source AS ENUM ('website', 'referral', 'marketing', 'event', 'social', 'other');
  `);

  // Create lead_statuses enum
  await db.execute(sql`
    CREATE TYPE lead_status AS ENUM ('new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost', 'dormant');
  `);

  // Create lead_stages enum
  await db.execute(sql`
    CREATE TYPE lead_stage AS ENUM ('awareness', 'consideration', 'decision', 'retention', 'advocacy');
  `);

  // Create leads table
  await db.execute(sql`
    CREATE TABLE leads (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      type lead_type NOT NULL,
      source lead_source NOT NULL,
      status lead_status NOT NULL DEFAULT 'new',
      stage lead_stage NOT NULL DEFAULT 'awareness',
      title VARCHAR(255) NOT NULL,
      description TEXT,
      assigned_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
      assigned_team_id UUID REFERENCES teams(id) ON DELETE SET NULL,
      organization_name VARCHAR(255),
      organization_size VARCHAR(50),
      industry VARCHAR(100),
      website VARCHAR(255),
      annual_revenue VARCHAR(50),
      territory VARCHAR(100),
      notes TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
      created_by UUID REFERENCES users(id) ON DELETE SET NULL
    );
  `);

  // Create owner_types enum
  await db.execute(sql`
    CREATE TYPE owner_type AS ENUM ('user', 'team');
  `);

  // Create action_statuses enum
  await db.execute(sql`
    CREATE TYPE action_status AS ENUM ('pending', 'in_progress', 'completed', 'cancelled');
  `);

  // Create lead_actions table
  await db.execute(sql`
    CREATE TABLE lead_actions (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      sequence_number INTEGER NOT NULL,
      owner_type owner_type NOT NULL,
      owner_id UUID NOT NULL,
      status action_status NOT NULL DEFAULT 'pending',
      due_date TIMESTAMP,
      completed_at TIMESTAMP,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
      created_by UUID REFERENCES users(id) ON DELETE SET NULL
    );
  `);

  // Create contacts table
  await db.execute(sql`
    CREATE TABLE contacts (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
      first_name VARCHAR(100) NOT NULL,
      last_name VARCHAR(100) NOT NULL,
      email VARCHAR(255),
      phone VARCHAR(50),
      job_title VARCHAR(100),
      is_primary BOOLEAN NOT NULL DEFAULT false,
      notes TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create activity_types enum
  await db.execute(sql`
    CREATE TYPE activity_type AS ENUM ('call', 'email', 'meeting', 'note', 'task');
  `);

  // Create activity_log table
  await db.execute(sql`
    CREATE TABLE activity_log (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
      type activity_type NOT NULL,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      created_by UUID REFERENCES users(id) ON DELETE SET NULL
    );
  `);

  // Create document_types enum
  await db.execute(sql`
    CREATE TYPE document_type AS ENUM ('proposal', 'contract', 'credential', 'other');
  `);

  // Create document_statuses enum
  await db.execute(sql`
    CREATE TYPE document_status AS ENUM ('draft', 'pending', 'approved', 'rejected', 'expired');
  `);

  // Create documents table
  await db.execute(sql`
    CREATE TABLE documents (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
      type document_type NOT NULL,
      status document_status NOT NULL DEFAULT 'draft',
      title VARCHAR(255) NOT NULL,
      description TEXT,
      file_url VARCHAR(255) NOT NULL,
      file_name VARCHAR(255) NOT NULL,
      file_size INTEGER NOT NULL,
      file_type VARCHAR(100) NOT NULL,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
      uploaded_by UUID REFERENCES users(id) ON DELETE SET NULL
    );
  `);

  // Create indexes for performance
  await db.execute(sql`
    CREATE INDEX idx_leads_assigned_user_id ON leads(assigned_user_id);
    CREATE INDEX idx_leads_assigned_team_id ON leads(assigned_team_id);
    CREATE INDEX idx_leads_status ON leads(status);
    CREATE INDEX idx_leads_stage ON leads(stage);
    CREATE INDEX idx_lead_actions_lead_id ON lead_actions(lead_id);
    CREATE INDEX idx_lead_actions_owner_id ON lead_actions(owner_id);
    CREATE INDEX idx_lead_actions_status ON lead_actions(status);
    CREATE INDEX idx_contacts_lead_id ON contacts(lead_id);
    CREATE INDEX idx_activity_log_lead_id ON activity_log(lead_id);
    CREATE INDEX idx_documents_lead_id ON documents(lead_id);
  `);

  logger.debug('✅ Created CRM schema tables and indexes');
}

export async function down(db: any) {
  // Drop tables in reverse order to handle dependencies
  await db.execute(sql`DROP TABLE IF EXISTS documents CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS activity_log CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS contacts CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS lead_actions CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS leads CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS teams CASCADE;`);
  
  // Drop enums
  await db.execute(sql`DROP TYPE IF EXISTS document_status CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS document_type CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS activity_type CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS action_status CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS owner_type CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS lead_stage CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS lead_status CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS lead_source CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS lead_type CASCADE;`);
  
  logger.debug('✅ Dropped CRM schema tables and types');
}
