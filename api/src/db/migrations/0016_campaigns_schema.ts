import { sql } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
export async function up(db: any) {
  // Create campaign_status enum
  await db.execute(sql`
    CREATE TYPE campaign_status AS ENUM ('Draft', 'Active', 'Paused', 'Completed', 'Archived');
  `);

  // Create campaign_type enum
  await db.execute(sql`
    CREATE TYPE campaign_type AS ENUM ('Email', 'SMS', 'Push', 'InApp');
  `);

  // Create trigger_type enum
  await db.execute(sql`
    CREATE TYPE trigger_type AS ENUM ('Manual', 'UserRegistration', 'CaseClosed', 'ContactCreated', 'CaseAging', 'LeadStatusChange', 'AppointmentScheduled', 'DocumentUploaded', 'Custom');
  `);

  // Create template_type enum
  await db.execute(sql`
    CREATE TYPE template_type AS ENUM ('Welcome', 'FollowUp', 'Reminder', 'Notification', 'Marketing', 'Transactional', 'Custom');
  `);

  // Create execution_status enum
  await db.execute(sql`
    CREATE TYPE execution_status AS ENUM ('Pending', 'Sent', 'Failed', 'Bounced', 'Delivered', 'Opened', 'Clicked');
  `);

  // Create audience_type enum
  await db.execute(sql`
    CREATE TYPE audience_type AS ENUM ('AllUsers', 'Doctors', 'Patients', 'Agents', 'Admins', 'Custom');
  `);

  // Create email templates table
  await db.execute(sql`
    CREATE TABLE crm_email_templates (
      template_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(255) NOT NULL,
      description TEXT,
      type template_type NOT NULL,
      subject VARCHAR(500) NOT NULL,
      html_content TEXT NOT NULL,
      text_content TEXT,
      variables JSONB DEFAULT '[]'::jsonb,
      is_active BOOLEAN NOT NULL DEFAULT true,
      created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create campaign rules table
  await db.execute(sql`
    CREATE TABLE crm_campaign_rules (
      rule_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(255) NOT NULL,
      description TEXT,
      rule_definition JSONB NOT NULL,
      is_active BOOLEAN NOT NULL DEFAULT true,
      created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create campaigns table
  await db.execute(sql`
    CREATE TABLE crm_campaigns (
      campaign_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(255) NOT NULL,
      description TEXT,
      type campaign_type NOT NULL DEFAULT 'Email',
      status campaign_status NOT NULL DEFAULT 'Draft',
      trigger_type trigger_type NOT NULL,
      trigger_config JSONB,
      audience_type audience_type NOT NULL,
      audience_rules JSONB,
      template_id UUID REFERENCES crm_email_templates(template_id) ON DELETE SET NULL,
      rule_id UUID REFERENCES crm_campaign_rules(rule_id) ON DELETE SET NULL,
      start_date TIMESTAMP,
      end_date TIMESTAMP,
      send_limit_per_day INTEGER,
      send_limit_per_hour INTEGER,
      respect_unsubscribe BOOLEAN NOT NULL DEFAULT true,
      track_opens BOOLEAN NOT NULL DEFAULT true,
      track_clicks BOOLEAN NOT NULL DEFAULT true,
      tags JSONB DEFAULT '[]'::jsonb,
      metadata JSONB DEFAULT '{}'::jsonb,
      created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create campaign executions table
  await db.execute(sql`
    CREATE TABLE crm_campaign_executions (
      execution_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      campaign_id UUID NOT NULL REFERENCES crm_campaigns(campaign_id) ON DELETE CASCADE,
      recipient_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      recipient_email VARCHAR(255) NOT NULL,
      recipient_name VARCHAR(255),
      related_lead_id UUID,
      related_case_id UUID,
      status execution_status NOT NULL DEFAULT 'Pending',
      scheduled_at TIMESTAMP NOT NULL,
      sent_at TIMESTAMP,
      delivered_at TIMESTAMP,
      opened_at TIMESTAMP,
      clicked_at TIMESTAMP,
      bounced_at TIMESTAMP,
      unsubscribed_at TIMESTAMP,
      subject VARCHAR(500),
      html_content TEXT,
      text_content TEXT,
      open_count INTEGER NOT NULL DEFAULT 0,
      click_count INTEGER NOT NULL DEFAULT 0,
      tracking_pixel_url VARCHAR(500),
      error_message TEXT,
      retry_count INTEGER NOT NULL DEFAULT 0,
      max_retries INTEGER NOT NULL DEFAULT 3,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create campaign analytics table
  await db.execute(sql`
    CREATE TABLE crm_campaign_analytics (
      analytics_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      campaign_id UUID NOT NULL REFERENCES crm_campaigns(campaign_id) ON DELETE CASCADE,
      total_sent INTEGER NOT NULL DEFAULT 0,
      total_delivered INTEGER NOT NULL DEFAULT 0,
      total_bounced INTEGER NOT NULL DEFAULT 0,
      total_opened INTEGER NOT NULL DEFAULT 0,
      total_clicked INTEGER NOT NULL DEFAULT 0,
      total_unsubscribed INTEGER NOT NULL DEFAULT 0,
      delivery_rate INTEGER NOT NULL DEFAULT 0,
      open_rate INTEGER NOT NULL DEFAULT 0,
      click_rate INTEGER NOT NULL DEFAULT 0,
      bounce_rate INTEGER NOT NULL DEFAULT 0,
      unsubscribe_rate INTEGER NOT NULL DEFAULT 0,
      last_sent_at TIMESTAMP,
      last_updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create campaign triggers table
  await db.execute(sql`
    CREATE TABLE crm_campaign_triggers (
      trigger_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      campaign_id UUID NOT NULL REFERENCES crm_campaigns(campaign_id) ON DELETE CASCADE,
      trigger_event VARCHAR(255) NOT NULL,
      entity_type VARCHAR(100),
      entity_id UUID,
      scheduled_at TIMESTAMP NOT NULL,
      executed_at TIMESTAMP,
      context_data JSONB DEFAULT '{}'::jsonb,
      is_processed BOOLEAN NOT NULL DEFAULT false,
      error_message TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create unsubscribe list table
  await db.execute(sql`
    CREATE TABLE crm_unsubscribe_list (
      unsubscribe_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      email VARCHAR(255) NOT NULL UNIQUE,
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      campaign_id UUID REFERENCES crm_campaigns(campaign_id) ON DELETE SET NULL,
      unsubscribe_reason VARCHAR(255),
      unsubscribe_token VARCHAR(255) NOT NULL UNIQUE,
      unsubscribe_all BOOLEAN NOT NULL DEFAULT false,
      unsubscribe_categories JSONB DEFAULT '[]'::jsonb,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW()
    );
  `);

  // Create indexes for performance
  await db.execute(sql`
    CREATE INDEX idx_crm_campaigns_status ON crm_campaigns(status);
    CREATE INDEX idx_crm_campaigns_type ON crm_campaigns(type);
    CREATE INDEX idx_crm_campaigns_trigger_type ON crm_campaigns(trigger_type);
    CREATE INDEX idx_crm_campaigns_created_by ON crm_campaigns(created_by);
    
    CREATE INDEX idx_crm_email_templates_type ON crm_email_templates(type);
    CREATE INDEX idx_crm_email_templates_is_active ON crm_email_templates(is_active);
    CREATE INDEX idx_crm_email_templates_created_by ON crm_email_templates(created_by);
    
    CREATE INDEX idx_crm_campaign_rules_is_active ON crm_campaign_rules(is_active);
    CREATE INDEX idx_crm_campaign_rules_created_by ON crm_campaign_rules(created_by);
    
    CREATE INDEX idx_crm_campaign_executions_campaign_id ON crm_campaign_executions(campaign_id);
    CREATE INDEX idx_crm_campaign_executions_status ON crm_campaign_executions(status);
    CREATE INDEX idx_crm_campaign_executions_recipient_user_id ON crm_campaign_executions(recipient_user_id);
    CREATE INDEX idx_crm_campaign_executions_scheduled_at ON crm_campaign_executions(scheduled_at);
    
    CREATE INDEX idx_crm_campaign_analytics_campaign_id ON crm_campaign_analytics(campaign_id);
    
    CREATE INDEX idx_crm_campaign_triggers_campaign_id ON crm_campaign_triggers(campaign_id);
    CREATE INDEX idx_crm_campaign_triggers_trigger_event ON crm_campaign_triggers(trigger_event);
    CREATE INDEX idx_crm_campaign_triggers_is_processed ON crm_campaign_triggers(is_processed);
    
    CREATE INDEX idx_crm_unsubscribe_list_email ON crm_unsubscribe_list(email);
    CREATE INDEX idx_crm_unsubscribe_list_user_id ON crm_unsubscribe_list(user_id);
    CREATE INDEX idx_crm_unsubscribe_list_campaign_id ON crm_unsubscribe_list(campaign_id);
  `);

  logger.debug('✅ Created email campaigns schema tables and indexes');
}

export async function down(db: any) {
  // Drop tables in reverse order to handle dependencies
  await db.execute(sql`DROP TABLE IF EXISTS unsubscribes CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS campaign_triggers CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS campaign_analytics CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS campaign_executions CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS campaign_rules CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS campaigns CASCADE;`);
  await db.execute(sql`DROP TABLE IF EXISTS email_templates CASCADE;`);
  
  // Drop enums
  await db.execute(sql`DROP TYPE IF EXISTS execution_status CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS template_type CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS trigger_type CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS campaign_status CASCADE;`);
  await db.execute(sql`DROP TYPE IF EXISTS campaign_type CASCADE;`);
  
  logger.debug('✅ Dropped email campaigns schema tables and types');
}