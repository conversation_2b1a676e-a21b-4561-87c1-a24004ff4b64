import { pgTable, uuid, varchar, text, timestamp, boolean, pgEnum, integer, jsonb } from 'drizzle-orm/pg-core';
import { users } from './users';
import { leads } from './crm';

// Campaign enums
export const campaignStatusEnum = pgEnum('campaign_status', ['Draft', 'Active', 'Paused', 'Completed', 'Archived']);
export const campaignTypeEnum = pgEnum('campaign_type', ['Email', 'SMS', 'Push', 'InApp']);
export const triggerTypeEnum = pgEnum('trigger_type', ['Manual', 'UserRegistration', 'CaseClosed', 'ContactCreated', 'CaseAging', 'LeadStatusChange', 'AppointmentScheduled', 'DocumentUploaded', 'Custom']);
export const templateTypeEnum = pgEnum('template_type', ['Welcome', 'FollowUp', 'Reminder', 'Notification', 'Marketing', 'Transactional', 'Custom']);
export const executionStatusEnum = pgEnum('execution_status', ['Pending', 'Sent', 'Failed', 'Bounced', 'Delivered', 'Opened', 'Clicked']);
export const audienceTypeEnum = pgEnum('audience_type', ['AllUsers', 'Doctors', 'Patients', 'Agents', 'Admins', 'Custom']);

// Email Templates table
export const emailTemplates = pgTable('crm_email_templates', {
  template_id: uuid('template_id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  type: templateTypeEnum('type').notNull(),
  subject: varchar('subject', { length: 500 }).notNull(),
  html_content: text('html_content').notNull(),
  text_content: text('text_content'),
  variables: jsonb('variables').$type<string[]>().default([]), // Available template variables
  is_active: boolean('is_active').notNull().default(true),
  created_by: uuid('created_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Campaign Rules (OPAL-style) table
export const campaignRules = pgTable('crm_campaign_rules', {
  rule_id: uuid('rule_id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  // OPAL-style rule definition
  rule_definition: jsonb('rule_definition').$type<{
    conditions: Array<{
      field: string;
      operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in' | 'exists' | 'not_exists';
      value: any;
      logical_operator?: 'AND' | 'OR';
    }>;
    actions: Array<{
      type: 'send_email' | 'add_tag' | 'update_field' | 'create_task' | 'wait' | 'branch';
      parameters: Record<string, any>;
    }>;
  }>().notNull(),
  is_active: boolean('is_active').notNull().default(true),
  created_by: uuid('created_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Campaigns table
export const campaigns = pgTable('crm_campaigns', {
  campaign_id: uuid('campaign_id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  type: campaignTypeEnum('type').notNull().default('Email'),
  status: campaignStatusEnum('status').notNull().default('Draft'),
  
  // Trigger configuration
  trigger_type: triggerTypeEnum('trigger_type').notNull(),
  trigger_config: jsonb('trigger_config').$type<{
    event?: string;
    conditions?: Record<string, any>;
    delay_minutes?: number;
    recurring?: boolean;
    recurring_interval?: 'daily' | 'weekly' | 'monthly';
  }>(),
  
  // Audience targeting
  audience_type: audienceTypeEnum('audience_type').notNull(),
  audience_rules: jsonb('audience_rules').$type<{
    include_conditions?: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
    exclude_conditions?: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
    custom_query?: string;
  }>(),
  
  // Email template reference
  template_id: uuid('template_id').references(() => emailTemplates.template_id, { onDelete: 'set null' }),
  
  // Campaign rules reference
  rule_id: uuid('rule_id').references(() => campaignRules.rule_id, { onDelete: 'set null' }),
  
  // Scheduling
  start_date: timestamp('start_date'),
  end_date: timestamp('end_date'),
  
  // Settings
  send_limit_per_day: integer('send_limit_per_day'),
  send_limit_per_hour: integer('send_limit_per_hour'),
  respect_unsubscribe: boolean('respect_unsubscribe').notNull().default(true),
  track_opens: boolean('track_opens').notNull().default(true),
  track_clicks: boolean('track_clicks').notNull().default(true),
  
  // Metadata
  tags: jsonb('tags').$type<string[]>().default([]),
  metadata: jsonb('metadata').$type<Record<string, any>>().default({}),
  
  created_by: uuid('created_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Campaign Executions table (tracks individual email sends)
export const campaignExecutions = pgTable('crm_campaign_executions', {
  execution_id: uuid('execution_id').primaryKey().defaultRandom(),
  campaign_id: uuid('campaign_id').notNull().references(() => campaigns.campaign_id, { onDelete: 'cascade' }),
  
  // Recipient information
  recipient_user_id: uuid('recipient_user_id').references(() => users.id, { onDelete: 'cascade' }),
  recipient_email: varchar('recipient_email', { length: 255 }).notNull(),
  recipient_name: varchar('recipient_name', { length: 255 }),
  
  // Related entities
  related_lead_id: uuid('related_lead_id').references(() => leads.lead_id, { onDelete: 'set null' }),
  related_case_id: uuid('related_case_id'), // Reference to cases table
  
  // Execution details
  status: executionStatusEnum('status').notNull().default('Pending'),
  scheduled_at: timestamp('scheduled_at').notNull(),
  sent_at: timestamp('sent_at'),
  delivered_at: timestamp('delivered_at'),
  opened_at: timestamp('opened_at'),
  clicked_at: timestamp('clicked_at'),
  bounced_at: timestamp('bounced_at'),
  unsubscribed_at: timestamp('unsubscribed_at'),
  
  // Email content (snapshot at time of send)
  subject: varchar('subject', { length: 500 }),
  html_content: text('html_content'),
  text_content: text('text_content'),
  
  // Tracking
  open_count: integer('open_count').notNull().default(0),
  click_count: integer('click_count').notNull().default(0),
  tracking_pixel_url: varchar('tracking_pixel_url', { length: 500 }),
  
  // Error handling
  error_message: text('error_message'),
  retry_count: integer('retry_count').notNull().default(0),
  max_retries: integer('max_retries').notNull().default(3),
  
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Campaign Analytics table
export const campaignAnalytics = pgTable('crm_campaign_analytics', {
  analytics_id: uuid('analytics_id').primaryKey().defaultRandom(),
  campaign_id: uuid('campaign_id').notNull().references(() => campaigns.campaign_id, { onDelete: 'cascade' }),
  
  // Aggregate metrics
  total_sent: integer('total_sent').notNull().default(0),
  total_delivered: integer('total_delivered').notNull().default(0),
  total_bounced: integer('total_bounced').notNull().default(0),
  total_opened: integer('total_opened').notNull().default(0),
  total_clicked: integer('total_clicked').notNull().default(0),
  total_unsubscribed: integer('total_unsubscribed').notNull().default(0),
  
  // Calculated rates
  delivery_rate: integer('delivery_rate').notNull().default(0), // Percentage * 100
  open_rate: integer('open_rate').notNull().default(0), // Percentage * 100
  click_rate: integer('click_rate').notNull().default(0), // Percentage * 100
  bounce_rate: integer('bounce_rate').notNull().default(0), // Percentage * 100
  unsubscribe_rate: integer('unsubscribe_rate').notNull().default(0), // Percentage * 100
  
  // Time-based metrics
  last_sent_at: timestamp('last_sent_at'),
  last_updated_at: timestamp('last_updated_at').notNull().defaultNow(),
  
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Campaign Triggers table (tracks when campaigns should be executed)
export const campaignTriggers = pgTable('crm_campaign_triggers', {
  trigger_id: uuid('trigger_id').primaryKey().defaultRandom(),
  campaign_id: uuid('campaign_id').notNull().references(() => campaigns.campaign_id, { onDelete: 'cascade' }),
  
  // Trigger details
  trigger_event: varchar('trigger_event', { length: 255 }).notNull(),
  entity_type: varchar('entity_type', { length: 100 }), // 'user', 'case', 'lead', 'contact'
  entity_id: uuid('entity_id'), // ID of the entity that triggered this
  
  // Execution scheduling
  scheduled_at: timestamp('scheduled_at').notNull(),
  executed_at: timestamp('executed_at'),
  
  // Context data for template rendering
  context_data: jsonb('context_data').$type<Record<string, any>>().default({}),
  
  // Status
  is_processed: boolean('is_processed').notNull().default(false),
  error_message: text('error_message'),
  
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Unsubscribe Management table
export const unsubscribeList = pgTable('crm_unsubscribe_list', {
  unsubscribe_id: uuid('unsubscribe_id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  user_id: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }),
  
  // Unsubscribe details
  campaign_id: uuid('campaign_id').references(() => campaigns.campaign_id, { onDelete: 'set null' }),
  unsubscribe_reason: varchar('unsubscribe_reason', { length: 255 }),
  unsubscribe_token: varchar('unsubscribe_token', { length: 255 }).notNull().unique(),
  
  // Scope of unsubscribe
  unsubscribe_all: boolean('unsubscribe_all').notNull().default(false),
  unsubscribe_categories: jsonb('unsubscribe_categories').$type<string[]>().default([]),
  
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Type inference
export type EmailTemplate = typeof emailTemplates.$inferSelect;
export type NewEmailTemplate = typeof emailTemplates.$inferInsert;

export type CampaignRule = typeof campaignRules.$inferSelect;
export type NewCampaignRule = typeof campaignRules.$inferInsert;

export type Campaign = typeof campaigns.$inferSelect;
export type NewCampaign = typeof campaigns.$inferInsert;

export type CampaignExecution = typeof campaignExecutions.$inferSelect;
export type NewCampaignExecution = typeof campaignExecutions.$inferInsert;

export type CampaignAnalytics = typeof campaignAnalytics.$inferSelect;
export type NewCampaignAnalytics = typeof campaignAnalytics.$inferInsert;

export type CampaignTrigger = typeof campaignTriggers.$inferSelect;
export type NewCampaignTrigger = typeof campaignTriggers.$inferInsert;

export type UnsubscribeEntry = typeof unsubscribeList.$inferSelect;
export type NewUnsubscribeEntry = typeof unsubscribeList.$inferInsert;