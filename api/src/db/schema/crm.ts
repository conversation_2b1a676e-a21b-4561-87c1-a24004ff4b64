import { pgTable, uuid, varchar, text, timestamp, boolean, pgEnum, integer } from 'drizzle-orm/pg-core';
import { users } from './users';

// CRM Enums
export const leadTypeEnum = pgEnum('lead_type', ['Hospital', 'Clinic', 'Doctor', 'Referral', 'Other']);
export const leadSourceEnum = pgEnum('lead_source', ['Inbound', 'Outbound', 'Referral', 'Campaign', 'Other']);
export const leadStatusEnum = pgEnum('lead_status', ['New', 'Contacted', 'Qualified', 'Engaged', 'ProposalSent', 'Signed', 'Onboarded', 'ClosedLost']);
export const leadStageEnum = pgEnum('lead_stage', ['Lead', 'Deal', 'ClosedWon', 'ClosedLost']);
export const ownerTypeEnum = pgEnum('owner_type', ['User', 'Team']);
export const actionStatusEnum = pgEnum('action_status', ['Pending', 'Completed', 'Blocked']);
export const activityTypeEnum = pgEnum('activity_type', ['Call', 'Email', 'Meeting', 'InternalNote', 'Other']);
export const documentTypeEnum = pgEnum('document_type', ['Proposal', 'MoU', 'Credential', 'Contract', 'Other']);
export const documentStatusEnum = pgEnum('document_status', ['Draft', 'Sent', 'Signed']);

// Communication plan enums
export const communicationStepTypeEnum = pgEnum('communication_step_type', ['Email', 'Call', 'Meeting', 'Task', 'Other']);
export const communicationStepStatusEnum = pgEnum('communication_step_status', ['Pending', 'Completed', 'Skipped', 'Failed']);

// Organization and contact enums
export const organizationTypeEnum = pgEnum('organization_type', ['Hospital', 'Clinic', 'Insurance', 'Pharmacy', 'Other']);
export const contactStatusEnum = pgEnum('contact_status', ['Active', 'Inactive', 'Lead', 'Customer', 'Partner']);
export const touchpointTypeEnum = pgEnum('touchpoint_type', ['Call', 'Email', 'Meeting', 'Demo', 'Followup', 'Other']);

// Teams table
export const teams = pgTable('crm_teams', {
  team_id: uuid('team_id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Organizations table
export const organizations = pgTable('crm_organizations', {
  organization_id: uuid('organization_id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  type: organizationTypeEnum('type').notNull(),
  website: varchar('website', { length: 255 }),
  phone: varchar('phone', { length: 50 }),
  address: text('address'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 100 }),
  zip: varchar('zip', { length: 20 }),
  country: varchar('country', { length: 100 }),
  notes: text('notes'),
  created_by: uuid('created_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Leads table
export const leads = pgTable('crm_leads', {
  lead_id: uuid('lead_id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  type: leadTypeEnum('type').notNull(),
  source: leadSourceEnum('source').notNull(),
  status: leadStatusEnum('status').notNull().default('New'),
  stage: leadStageEnum('stage').notNull().default('Lead'),
  assigned_to_id: uuid('assigned_to_id'),
  assigned_to_type: ownerTypeEnum('assigned_to_type'),
  org_affiliation: varchar('org_affiliation', { length: 255 }),
  notes: text('notes'),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Lead actions table
export const leadActions = pgTable('crm_lead_actions', {
  action_id: uuid('action_id').primaryKey().defaultRandom(),
  lead_id: uuid('lead_id').notNull().references(() => leads.lead_id, { onDelete: 'cascade' }),
  title: varchar('title', { length: 255 }).notNull(),
  sequence: varchar('sequence', { length: 50 }),
  status: actionStatusEnum('status').notNull().default('Pending'),
  due_date: timestamp('due_date'),
  owner_id: uuid('owner_id'),
  owner_type: ownerTypeEnum('owner_type'),
  created_at: timestamp('created_at').notNull().defaultNow(),
  completed_at: timestamp('completed_at'),
});

// Contacts table
export const contacts = pgTable('crm_contacts', {
  contact_id: uuid('contact_id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  role: varchar('role', { length: 100 }),
  email: varchar('email', { length: 255 }),
  phone: varchar('phone', { length: 50 }),
  lead_id: uuid('lead_id').notNull().references(() => leads.lead_id, { onDelete: 'cascade' }),
  notes: text('notes'),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Touchpoints table
export const touchpoints = pgTable('crm_touchpoints', {
  touchpoint_id: uuid('touchpoint_id').primaryKey().defaultRandom(),
  contact_id: uuid('contact_id').notNull().references(() => contacts.contact_id, { onDelete: 'cascade' }),
  type: touchpointTypeEnum('type').notNull(),
  date: timestamp('date').notNull(),
  summary: text('summary').notNull(),
  details: text('details'),
  outcome: text('outcome'),
  follow_up_date: timestamp('follow_up_date'),
  created_by: uuid('created_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Activity log table
export const activityLog = pgTable('crm_activity_log', {
  activity_id: uuid('activity_id').primaryKey().defaultRandom(),
  lead_id: uuid('lead_id').notNull().references(() => leads.lead_id, { onDelete: 'cascade' }),
  type: activityTypeEnum('type').notNull(),
  date: timestamp('date').notNull(),
  summary: text('summary'),
  created_by: uuid('created_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').notNull().defaultNow(),
});

// Documents table
export const documents = pgTable('crm_documents', {
  document_id: uuid('document_id').primaryKey().defaultRandom(),
  lead_id: uuid('lead_id').notNull().references(() => leads.lead_id, { onDelete: 'cascade' }),
  type: documentTypeEnum('type').notNull(),
  status: documentStatusEnum('status').notNull().default('Draft'),
  url: varchar('url', { length: 500 }).notNull(),
  uploaded_by: uuid('uploaded_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  uploaded_at: timestamp('uploaded_at').notNull().defaultNow(),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Communication Plans table
export const communicationPlans = pgTable('crm_communication_plans', {
  plan_id: uuid('plan_id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  is_active: boolean('is_active').notNull().default(true),
  created_by: uuid('created_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Communication Plan Steps table
export const communicationPlanSteps = pgTable('crm_communication_plan_steps', {
  step_id: uuid('step_id').primaryKey().defaultRandom(),
  plan_id: uuid('plan_id').notNull().references(() => communicationPlans.plan_id, { onDelete: 'cascade' }),
  sequence: integer('sequence').notNull(),
  type: communicationStepTypeEnum('type').notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content'),
  delay_days: integer('delay_days').notNull().default(0),
  is_active: boolean('is_active').notNull().default(true),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Contact Plan Assignments table
export const contactPlanAssignments = pgTable('crm_contact_plan_assignments', {
  assignment_id: uuid('assignment_id').primaryKey().defaultRandom(),
  contact_id: uuid('contact_id').notNull().references(() => contacts.contact_id, { onDelete: 'cascade' }),
  plan_id: uuid('plan_id').notNull().references(() => communicationPlans.plan_id, { onDelete: 'cascade' }),
  assigned_by: uuid('assigned_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  status: varchar('status', { length: 50 }).notNull().default('active'),
  start_date: timestamp('start_date').notNull().defaultNow(),
  end_date: timestamp('end_date'),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Referrals table
export const referrals = pgTable('crm_referrals', {
  referral_id: uuid('referral_id').primaryKey().defaultRandom(),
  lead_id: uuid('lead_id').notNull().references(() => leads.lead_id, { onDelete: 'cascade' }),
  referrer_name: varchar('referrer_name', { length: 255 }).notNull(),
  referrer_email: varchar('referrer_email', { length: 255 }),
  referrer_phone: varchar('referrer_phone', { length: 50 }),
  referrer_organization: varchar('referrer_organization', { length: 255 }),
  referral_date: timestamp('referral_date').notNull().defaultNow(),
  notes: text('notes'),
  status: varchar('status', { length: 50 }).notNull().default('pending'),
  created_by: uuid('created_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Type inference from Drizzle tables
export type Team = typeof teams.$inferSelect;
export type NewTeam = typeof teams.$inferInsert;

export type Lead = typeof leads.$inferSelect;
export type NewLead = typeof leads.$inferInsert;

export type LeadAction = typeof leadActions.$inferSelect;
export type NewLeadAction = typeof leadActions.$inferInsert;

export type Contact = typeof contacts.$inferSelect;
export type NewContact = typeof contacts.$inferInsert;

export type Activity = typeof activityLog.$inferSelect;
export type NewActivity = typeof activityLog.$inferInsert;

export type Document = typeof documents.$inferSelect;
export type NewDocument = typeof documents.$inferInsert;

export type CommunicationPlan = typeof communicationPlans.$inferSelect;
export type NewCommunicationPlan = typeof communicationPlans.$inferInsert;

export type CommunicationPlanStep = typeof communicationPlanSteps.$inferSelect;
export type NewCommunicationPlanStep = typeof communicationPlanSteps.$inferInsert;
