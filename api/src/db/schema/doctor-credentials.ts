import { pgTable, uuid, varchar, text, timestamp, boolean, pgEnum, integer } from 'drizzle-orm/pg-core';
import { users } from './users';

// Credential status enum
export const credentialStatusEnum = pgEnum('credential_status', [
  'pending', 'verified', 'expired', 'revoked', 'under_review'
]);

// Credential type enum
export const credentialTypeEnum = pgEnum('credential_type', [
  'medical_license', 'board_certification', 'dea_registration', 'npi_number', 
  'hospital_privileges', 'malpractice_insurance', 'continuing_education', 'other'
]);

// Verification method enum
export const verificationMethodEnum = pgEnum('verification_method', [
  'manual', 'automated', 'third_party', 'document_upload'
]);

// Doctor credentials table
export const doctorCredentials = pgTable('doctor_credentials', {
  id: uuid('id').primaryKey().defaultRandom(),
  doctorId: uuid('doctor_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  credentialType: credentialTypeEnum('credential_type').notNull(),
  credentialNumber: varchar('credential_number', { length: 100 }).notNull(),
  issuingAuthority: varchar('issuing_authority', { length: 255 }).notNull(),
  issuedDate: timestamp('issued_date').notNull(),
  expirationDate: timestamp('expiration_date'),
  status: credentialStatusEnum('status').notNull().default('pending'),
  verificationMethod: verificationMethodEnum('verification_method').notNull().default('manual'),
  verifiedBy: uuid('verified_by').references(() => users.id),
  verifiedAt: timestamp('verified_at'),
  notes: text('notes'),
  metadata: text('metadata'), // JSON string for additional credential-specific data
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Doctor profile URLs table
export const doctorProfileUrls = pgTable('doctor_profile_urls', {
  id: uuid('id').primaryKey().defaultRandom(),
  doctorId: uuid('doctor_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  urlType: varchar('url_type', { length: 50 }).notNull(), // 'linkedin', 'hospital_profile', 'practice_website', 'research_profile', etc.
  url: varchar('url', { length: 500 }).notNull(),
  displayName: varchar('display_name', { length: 100 }),
  isVerified: boolean('is_verified').notNull().default(false),
  verifiedBy: uuid('verified_by').references(() => users.id),
  verifiedAt: timestamp('verified_at'),
  isActive: boolean('is_active').notNull().default(true),
  sortOrder: integer('sort_order').notNull().default(0),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Credential documents table - integrated with existing storage system
export const credentialDocuments = pgTable('credential_documents', {
  id: uuid('id').primaryKey().defaultRandom(),
  credentialId: uuid('credential_id').notNull().references(() => doctorCredentials.id, { onDelete: 'cascade' }),
  title: varchar('title', { length: 255 }).notNull(), // Align with medicalDocuments schema
  description: text('description'), // Add description field like medicalDocuments
  fileName: varchar('file_name', { length: 255 }).notNull(),
  originalFileName: varchar('original_file_name', { length: 255 }).notNull(),
  filePath: varchar('file_path', { length: 500 }).notNull(), // MinIO storage path
  fileSize: integer('file_size').notNull(),
  mimeType: varchar('mime_type', { length: 100 }).notNull(),
  uploadedBy: uuid('uploaded_by').notNull().references(() => users.id),
  isVerified: boolean('is_verified').notNull().default(false),
  verifiedBy: uuid('verified_by').references(() => users.id),
  verifiedAt: timestamp('verified_at'),
  isDeleted: boolean('is_deleted').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Type inference from Drizzle tables
export type DoctorCredential = typeof doctorCredentials.$inferSelect;
export type NewDoctorCredential = typeof doctorCredentials.$inferInsert;
export type DoctorProfileUrl = typeof doctorProfileUrls.$inferSelect;
export type NewDoctorProfileUrl = typeof doctorProfileUrls.$inferInsert;
export type CredentialDocument = typeof credentialDocuments.$inferSelect;
export type NewCredentialDocument = typeof credentialDocuments.$inferInsert;