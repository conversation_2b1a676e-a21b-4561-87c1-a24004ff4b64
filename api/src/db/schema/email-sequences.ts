import { pgTable, uuid, varchar, text, timestamp, boolean, pgEnum, integer, jsonb } from 'drizzle-orm/pg-core';
import { users } from './users';
import { emailTemplates, campaigns } from './campaigns';

// Email Sequence enums
export const sequenceStatusEnum = pgEnum('sequence_status', ['Draft', 'Active', 'Paused', 'Completed', 'Archived']);
export const sequenceStepTypeEnum = pgEnum('sequence_step_type', ['Email', 'Wait', 'Condition', 'Action']);
export const sequenceStepStatusEnum = pgEnum('sequence_step_status', ['Pending', 'Scheduled', 'Sent', 'Failed', 'Skipped', 'Completed']);

// Email Sequences table - defines a series of timed emails
export const emailSequences = pgTable('crm_email_sequences', {
  sequence_id: uuid('sequence_id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  status: sequenceStatusEnum('status').notNull().default('Draft'),
  
  // Trigger configuration - when this sequence should start
  trigger_campaign_id: uuid('trigger_campaign_id').references(() => campaigns.campaign_id, { onDelete: 'cascade' }),
  trigger_event: varchar('trigger_event', { length: 255 }).notNull(), // 'user_registration', 'case_closed', etc.
  
  // Sequence settings
  max_subscribers: integer('max_subscribers'), // Optional limit
  respect_unsubscribe: boolean('respect_unsubscribe').notNull().default(true),
  respect_business_hours: boolean('respect_business_hours').notNull().default(false),
  business_hours_start: varchar('business_hours_start', { length: 5 }).default('09:00'), // HH:MM format
  business_hours_end: varchar('business_hours_end', { length: 5 }).default('17:00'), // HH:MM format
  business_days_only: boolean('business_days_only').notNull().default(false),
  timezone: varchar('timezone', { length: 50 }).default('UTC'),
  
  // Analytics
  total_subscribers: integer('total_subscribers').notNull().default(0),
  active_subscribers: integer('active_subscribers').notNull().default(0),
  completed_subscribers: integer('completed_subscribers').notNull().default(0),
  
  // Metadata
  tags: jsonb('tags').$type<string[]>().default([]),
  metadata: jsonb('metadata').$type<Record<string, any>>().default({}),
  
  created_by: uuid('created_by').notNull().references(() => users.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Email Sequence Steps table - defines each step in a sequence
export const emailSequenceSteps = pgTable('crm_email_sequence_steps', {
  step_id: uuid('step_id').primaryKey().defaultRandom(),
  sequence_id: uuid('sequence_id').notNull().references(() => emailSequences.sequence_id, { onDelete: 'cascade' }),
  
  // Step configuration
  step_order: integer('step_order').notNull(), // Order within the sequence (1, 2, 3, etc.)
  step_type: sequenceStepTypeEnum('step_type').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  
  // Timing configuration
  delay_amount: integer('delay_amount').notNull().default(0), // Number of delay units
  delay_unit: varchar('delay_unit', { length: 20 }).notNull().default('hours'), // 'minutes', 'hours', 'days', 'weeks'
  
  // Email step configuration (when step_type = 'Email')
  template_id: uuid('template_id').references(() => emailTemplates.template_id, { onDelete: 'set null' }),
  subject_override: varchar('subject_override', { length: 500 }), // Override template subject
  
  // Wait step configuration (when step_type = 'Wait')
  wait_until_time: varchar('wait_until_time', { length: 5 }), // Wait until specific time (HH:MM)
  wait_until_day: varchar('wait_until_day', { length: 10 }), // Wait until specific day ('monday', 'tuesday', etc.)
  
  // Condition step configuration (when step_type = 'Condition')
  condition_rules: jsonb('condition_rules').$type<{
    conditions: Array<{
      field: string;
      operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in' | 'exists' | 'not_exists';
      value: any;
      logical_operator?: 'AND' | 'OR';
    }>;
    true_step_id?: string; // Step to go to if condition is true
    false_step_id?: string; // Step to go to if condition is false
  }>(),
  
  // Action step configuration (when step_type = 'Action')
  action_config: jsonb('action_config').$type<{
    action_type: 'add_tag' | 'remove_tag' | 'update_field' | 'create_task' | 'send_webhook' | 'move_to_sequence';
    parameters: Record<string, any>;
  }>(),
  
  // Step settings
  is_active: boolean('is_active').notNull().default(true),
  skip_weekends: boolean('skip_weekends').notNull().default(false),
  
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Email Sequence Subscribers table - tracks who is in each sequence
export const emailSequenceSubscribers = pgTable('crm_email_sequence_subscribers', {
  subscriber_id: uuid('subscriber_id').primaryKey().defaultRandom(),
  sequence_id: uuid('sequence_id').notNull().references(() => emailSequences.sequence_id, { onDelete: 'cascade' }),
  
  // Subscriber information
  user_id: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }),
  email: varchar('email', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }),
  
  // Sequence progress
  current_step_id: uuid('current_step_id').references(() => emailSequenceSteps.step_id, { onDelete: 'set null' }),
  current_step_order: integer('current_step_order').notNull().default(1),
  next_execution_at: timestamp('next_execution_at'), // When the next step should execute
  
  // Status tracking
  status: varchar('status', { length: 50 }).notNull().default('active'), // 'active', 'paused', 'completed', 'unsubscribed', 'bounced'
  started_at: timestamp('started_at').notNull().defaultNow(),
  completed_at: timestamp('completed_at'),
  paused_at: timestamp('paused_at'),
  unsubscribed_at: timestamp('unsubscribed_at'),
  
  // Context data for personalization
  context_data: jsonb('context_data').$type<Record<string, any>>().default({}),
  
  // Analytics
  emails_sent: integer('emails_sent').notNull().default(0),
  emails_opened: integer('emails_opened').notNull().default(0),
  emails_clicked: integer('emails_clicked').notNull().default(0),
  
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Email Sequence Executions table - tracks individual step executions
export const emailSequenceExecutions = pgTable('crm_email_sequence_executions', {
  execution_id: uuid('execution_id').primaryKey().defaultRandom(),
  subscriber_id: uuid('subscriber_id').notNull().references(() => emailSequenceSubscribers.subscriber_id, { onDelete: 'cascade' }),
  step_id: uuid('step_id').notNull().references(() => emailSequenceSteps.step_id, { onDelete: 'cascade' }),
  
  // Execution details
  status: sequenceStepStatusEnum('status').notNull().default('Pending'),
  scheduled_at: timestamp('scheduled_at').notNull(),
  executed_at: timestamp('executed_at'),
  
  // Email execution details (for Email steps)
  campaign_execution_id: uuid('campaign_execution_id'), // Reference to crm_campaign_executions
  email_sent: boolean('email_sent').notNull().default(false),
  email_delivered: boolean('email_delivered').notNull().default(false),
  email_opened: boolean('email_opened').notNull().default(false),
  email_clicked: boolean('email_clicked').notNull().default(false),
  email_bounced: boolean('email_bounced').notNull().default(false),
  
  // Error handling
  error_message: text('error_message'),
  retry_count: integer('retry_count').notNull().default(0),
  max_retries: integer('max_retries').notNull().default(3),
  
  // Result data (for Condition and Action steps)
  result_data: jsonb('result_data').$type<Record<string, any>>().default({}),
  
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Email Sequence Analytics table - aggregate analytics for sequences
export const emailSequenceAnalytics = pgTable('crm_email_sequence_analytics', {
  analytics_id: uuid('analytics_id').primaryKey().defaultRandom(),
  sequence_id: uuid('sequence_id').notNull().references(() => emailSequences.sequence_id, { onDelete: 'cascade' }),
  step_id: uuid('step_id').references(() => emailSequenceSteps.step_id, { onDelete: 'cascade' }), // null for overall sequence analytics
  
  // Time period for analytics
  period_start: timestamp('period_start').notNull(),
  period_end: timestamp('period_end').notNull(),
  period_type: varchar('period_type', { length: 20 }).notNull(), // 'daily', 'weekly', 'monthly', 'overall'
  
  // Subscriber metrics
  new_subscribers: integer('new_subscribers').notNull().default(0),
  active_subscribers: integer('active_subscribers').notNull().default(0),
  completed_subscribers: integer('completed_subscribers').notNull().default(0),
  unsubscribed_subscribers: integer('unsubscribed_subscribers').notNull().default(0),
  
  // Email metrics (for email steps)
  emails_sent: integer('emails_sent').notNull().default(0),
  emails_delivered: integer('emails_delivered').notNull().default(0),
  emails_opened: integer('emails_opened').notNull().default(0),
  emails_clicked: integer('emails_clicked').notNull().default(0),
  emails_bounced: integer('emails_bounced').notNull().default(0),
  
  // Calculated rates (percentage * 100)
  completion_rate: integer('completion_rate').notNull().default(0),
  unsubscribe_rate: integer('unsubscribe_rate').notNull().default(0),
  delivery_rate: integer('delivery_rate').notNull().default(0),
  open_rate: integer('open_rate').notNull().default(0),
  click_rate: integer('click_rate').notNull().default(0),
  bounce_rate: integer('bounce_rate').notNull().default(0),
  
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

// Type inference
export type EmailSequence = typeof emailSequences.$inferSelect;
export type NewEmailSequence = typeof emailSequences.$inferInsert;

export type EmailSequenceStep = typeof emailSequenceSteps.$inferSelect;
export type NewEmailSequenceStep = typeof emailSequenceSteps.$inferInsert;

export type EmailSequenceSubscriber = typeof emailSequenceSubscribers.$inferSelect;
export type NewEmailSequenceSubscriber = typeof emailSequenceSubscribers.$inferInsert;

export type EmailSequenceExecution = typeof emailSequenceExecutions.$inferSelect;
export type NewEmailSequenceExecution = typeof emailSequenceExecutions.$inferInsert;

export type EmailSequenceAnalytics = typeof emailSequenceAnalytics.$inferSelect;
export type NewEmailSequenceAnalytics = typeof emailSequenceAnalytics.$inferInsert;