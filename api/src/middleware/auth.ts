import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { db } from '../db/index.js';
import { users } from '../db/schema/users.js';
import { eq } from 'drizzle-orm';
import { AppError, asyncHandler } from './errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';

// Extend Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        roles: string[];
        firstName: string;
        lastName: string;
        isActive: boolean;
        sessionId: string;
      };
    }
  }
}

// JWT authentication middleware
export const authMiddleware = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new AppError('Access token required', 401);
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix

  if (!process.env.JWT_SECRET) {
    throw new AppError('JWT_SECRET not configured', 500);
  }

  try {
    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET) as any;
    
    // Get user from database
    const [user] = await db
      .select({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        isActive: users.isActive,
      })
      .from(users)
      .where(eq(users.id, decoded.userId))
      .limit(1);

    if (!user) {
      throw new AppError('User not found', 401);
    }

    if (!user.isActive) {
      throw new AppError('Account is deactivated', 401);
    }

    // Attach user to request
    req.user = {
      ...user,
      roles: decoded.roles || ['patient'], // Use roles from token or fallback to default role
      sessionId: decoded.sessionId,
    };
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      // Log invalid token attempt
      logAuditEvent(
        null,
        'INVALID_TOKEN_ACCESS',
        'auth',
        {
          error: error.message,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.originalUrl,
        }
      );
      throw new AppError('Invalid token', 401);
    } else if (error instanceof jwt.TokenExpiredError) {
      // Log expired token attempt
      logAuditEvent(
        null,
        'EXPIRED_TOKEN_ACCESS',
        'auth',
        {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.originalUrl,
        }
      );
      throw new AppError('Token expired', 401);
    }
    throw error;
  }
});

// Role-based authorization middleware
export const requireRole = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new AppError('Authentication required', 401);
    }

    if (!allowedRoles.some(role => req.user!.roles.includes(role))) {
      // Log unauthorized access attempt
      logAuditEvent(
        req.user!.id,
        'UNAUTHORIZED_ACCESS',
        'auth',
        {
          userRoles: req.user!.roles,
          requiredRoles: allowedRoles,
          endpoint: req.originalUrl,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        }
      );
      throw new AppError('Insufficient permissions', 403);
    }

    next();
  };
};

// Optional auth middleware (doesn't throw if no token)
export const optionalAuth = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return next();
  }

  const token = authHeader.substring(7);

  if (!process.env.JWT_SECRET) {
    return next();
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET) as any;
    
    const [user] = await db
      .select({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        isActive: users.isActive,
      })
      .from(users)
      .where(eq(users.id, decoded.userId))
      .limit(1);

    if (user && user.isActive) {
      req.user = {
        ...user,
        roles: decoded.roles || ['patient'], // Use roles from token or fallback to default role
        sessionId: decoded.sessionId,
      };
    }
  } catch (error) {
    // Silently ignore token errors for optional auth
  }

  next();
});
