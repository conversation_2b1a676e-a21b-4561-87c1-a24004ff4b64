import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { db } from '../db/index.js';
import { users } from '../db/schema/users.js';
import { eq } from 'drizzle-orm';
import { AppError, asyncHandler } from './errorHandler.js';
import { logAuditEvent, logger } from '../utils/logger.js';
import { securityEventLogger } from './requestMonitoring.js';
import { PermissionService, type UserPermissionContext, type FilterContext } from '../services/permissionService.js';

// Token blacklist for logout/revocation (in production, use Redis)
const tokenBlacklist = new Set<string>();

// Failed login attempts tracking (in production, use Redis)
const failedAttempts = new Map<string, { count: number; lastAttempt: number; lockedUntil?: number }>();

// Session tracking for concurrent session limits
const activeSessions = new Map<string, Set<string>>();

// Enhanced JWT payload interface
interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  sessionId: string;
  iat: number;
  exp: number;
}

// Extend Request type to include enhanced user context with permissions
declare global {
  namespace Express {
    interface Request {
      user?: UserPermissionContext & {
        sessionId: string;
      };
      permissionContext?: FilterContext;
    }
  }
}

// Generate secure session ID
function generateSessionId(): string {
  return crypto.randomBytes(32).toString('hex');
}

// Check if account is locked due to failed attempts
function isAccountLocked(identifier: string): boolean {
  const attempts = failedAttempts.get(identifier);
  if (!attempts) return false;
  
  const now = Date.now();
  if (attempts.lockedUntil && attempts.lockedUntil > now) {
    return true;
  }
  
  // Clear expired locks
  if (attempts.lockedUntil && attempts.lockedUntil <= now) {
    failedAttempts.delete(identifier);
  }
  
  return false;
}

// Record failed login attempt
function recordFailedAttempt(identifier: string): void {
  const now = Date.now();
  const attempts = failedAttempts.get(identifier) || { count: 0, lastAttempt: 0 };
  
  // Reset count if last attempt was more than 15 minutes ago
  if (now - attempts.lastAttempt > 15 * 60 * 1000) {
    attempts.count = 0;
  }
  
  attempts.count++;
  attempts.lastAttempt = now;
  
  // Lock account after 5 failed attempts
  if (attempts.count >= 5) {
    attempts.lockedUntil = now + (30 * 60 * 1000); // 30 minutes lockout
    logger.warn(`Account locked due to failed attempts: ${identifier}`);
  }
  
  failedAttempts.set(identifier, attempts);
}

// Clear failed attempts on successful login
function clearFailedAttempts(identifier: string): void {
  failedAttempts.delete(identifier);
}

// Enhanced authentication middleware with additional security features
export const enhancedAuthMiddleware = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    securityEventLogger.logFailedAuth(req, 'MISSING_TOKEN');
    throw new AppError('Access token required', 401);
  }

  const token = authHeader.substring(7);

  // Check if token is blacklisted
  if (tokenBlacklist.has(token)) {
    securityEventLogger.logFailedAuth(req, 'BLACKLISTED_TOKEN');
    throw new AppError('Token has been revoked', 401);
  }

  if (!process.env.JWT_SECRET) {
    throw new AppError('JWT_SECRET not configured', 500);
  }

  try {
    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET) as JWTPayload;
    
    // Additional token validation
    if (!decoded.sessionId) {
      securityEventLogger.logFailedAuth(req, 'INVALID_TOKEN_FORMAT');
      throw new AppError('Invalid token format', 401);
    }

    // Check if session is still active
    const userSessions = activeSessions.get(decoded.userId);
    if (!userSessions || !userSessions.has(decoded.sessionId)) {
      securityEventLogger.logFailedAuth(req, 'SESSION_NOT_FOUND', { sessionId: decoded.sessionId });
      throw new AppError('Session not found or expired', 401);
    }
    
    // Get user from database with additional security checks
    const [user] = await db
      .select({
        id: users.id,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        role: users.role,
        isActive: users.isActive,
        lastLoginAt: users.lastLoginAt,
      })
      .from(users)
      .where(eq(users.id, decoded.userId))
      .limit(1);

    if (!user) {
      securityEventLogger.logFailedAuth(req, 'USER_NOT_FOUND', { userId: decoded.userId });
      throw new AppError('User not found', 401);
    }

    if (!user.isActive) {
      securityEventLogger.logFailedAuth(req, 'ACCOUNT_DEACTIVATED', { userId: decoded.userId });
      throw new AppError('Account is deactivated', 401);
    }

    // Check for suspicious login patterns
    const timeSinceLastLogin = user.lastLoginAt ? Date.now() - new Date(user.lastLoginAt).getTime() : 0;
    const isNewDevice = !req.get('User-Agent')?.includes('known-device'); // Simplified device tracking
    
    if (timeSinceLastLogin > 30 * 24 * 60 * 60 * 1000 && isNewDevice) { // 30 days + new device
      securityEventLogger.logSuspiciousActivity(req, 'LOGIN_FROM_NEW_DEVICE_AFTER_LONG_ABSENCE', {
        userId: user.id,
        timeSinceLastLogin,
        userAgent: req.get('User-Agent'),
      });
    }

    // Get user's complete permission context
    const permissionService = PermissionService.getInstance();
    const userPermissionContext = await permissionService.getUserPermissionContext(user.id);
    
    if (!userPermissionContext) {
      securityEventLogger.logFailedAuth(req, 'PERMISSION_CONTEXT_NOT_FOUND', { userId: user.id });
      throw new AppError('User permission context not found', 401);
    }

    // Attach enhanced user info with permissions to request
    req.user = {
      ...userPermissionContext,
      sessionId: decoded.sessionId,
    };

    // Set up permission context for filtering
    req.permissionContext = {
      userId: user.id,
      userRoles: userPermissionContext.roles.map(r => r.name),
      endpoint: req.originalUrl,
      method: req.method,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
    };

    // Log successful authentication for audit
    logAuditEvent(
      user.id,
      'SUCCESSFUL_AUTHENTICATION',
      'auth',
      {
        sessionId: decoded.sessionId,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
      }
    );

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      securityEventLogger.logFailedAuth(req, 'INVALID_TOKEN', { error: error.message });
      throw new AppError('Invalid token', 401);
    } else if (error instanceof jwt.TokenExpiredError) {
      securityEventLogger.logFailedAuth(req, 'EXPIRED_TOKEN');
      throw new AppError('Token expired', 401);
    }
    throw error;
  }
});

// Enhanced permission-based authorization middleware
export const requirePermission = (permissionName: string) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      securityEventLogger.logAccessViolation(req, 'NO_USER_CONTEXT');
      throw new AppError('Authentication required', 401);
    }

    const permissionService = PermissionService.getInstance();
    const permissionCheck = await permissionService.hasPermission(
      req.user.id,
      permissionName,
      req.permissionContext
    );

    if (!permissionCheck.granted) {
      securityEventLogger.logAccessViolation(req, 'INSUFFICIENT_PERMISSION', {
        permissionName,
        reason: permissionCheck.reason,
      });
      throw new AppError('Insufficient permissions', 403);
    }

    // Additional security check: verify session is still valid
    const userSessions = activeSessions.get(req.user.id);
    if (!userSessions || !userSessions.has(req.user.sessionId)) {
      securityEventLogger.logAccessViolation(req, 'INVALID_SESSION');
      throw new AppError('Session invalid', 401);
    }

    next();
  });
};

// Enhanced role-based authorization (legacy support)
export const enhancedRequireRole = (allowedRoles: string[]) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      securityEventLogger.logAccessViolation(req, 'NO_USER_CONTEXT');
      throw new AppError('Authentication required', 401);
    }

    const userRoleNames = req.user.roles.map(r => r.name);
    const hasRequiredRole = allowedRoles.some(role => userRoleNames.includes(role));

    if (!hasRequiredRole) {
      securityEventLogger.logAccessViolation(req, 'INSUFFICIENT_ROLE', {
        userRoles: userRoleNames,
        requiredRoles: allowedRoles,
      });
      throw new AppError('Insufficient permissions', 403);
    }

    // Additional security check: verify session is still valid
    const userSessions = activeSessions.get(req.user.id);
    if (!userSessions || !userSessions.has(req.user.sessionId)) {
      securityEventLogger.logAccessViolation(req, 'INVALID_SESSION');
      throw new AppError('Session invalid', 401);
    }

    next();
  });
};

// Resource-based permission check
export const requireResourcePermission = (resource: string, action: string) => {
  return asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      securityEventLogger.logAccessViolation(req, 'NO_USER_CONTEXT');
      throw new AppError('Authentication required', 401);
    }

    const permissionService = PermissionService.getInstance();
    const permissionCheck = await permissionService.hasResourcePermission(
      req.user.id,
      resource,
      action,
      req.permissionContext
    );

    if (!permissionCheck.granted) {
      securityEventLogger.logAccessViolation(req, 'INSUFFICIENT_RESOURCE_PERMISSION', {
        resource,
        action,
        reason: permissionCheck.reason,
      });
      throw new AppError('Insufficient permissions', 403);
    }

    next();
  });
};

// Login attempt validation middleware
export const loginAttemptValidator = (req: Request, res: Response, next: NextFunction) => {
  const identifier = req.body.email || req.ip; // Use email or IP as identifier
  
  if (isAccountLocked(identifier)) {
    const attempts = failedAttempts.get(identifier);
    const lockTimeRemaining = attempts?.lockedUntil ? Math.ceil((attempts.lockedUntil - Date.now()) / 1000 / 60) : 0;
    
    securityEventLogger.logFailedAuth(req, 'ACCOUNT_LOCKED', {
      identifier,
      lockTimeRemaining,
    });
    
    return res.status(429).json({
      error: 'Account Locked',
      message: `Too many failed login attempts. Try again in ${lockTimeRemaining} minutes.`,
      lockTimeRemaining,
      timestamp: new Date().toISOString(),
    });
  }
  
  next();
};

// Session management functions
export const sessionManager = {
  createSession: (userId: string): string => {
    const sessionId = generateSessionId();
    
    if (!activeSessions.has(userId)) {
      activeSessions.set(userId, new Set());
    }
    
    const userSessions = activeSessions.get(userId)!;
    
    // Limit concurrent sessions (max 5 per user)
    if (userSessions.size >= 5) {
      // Remove oldest session (simplified - in production, track timestamps)
      const oldestSession = userSessions.values().next().value;
      userSessions.delete(oldestSession);
      logger.info(`Removed oldest session for user ${userId} due to session limit`);
    }
    
    userSessions.add(sessionId);
    return sessionId;
  },

  destroySession: (userId: string, sessionId: string): void => {
    const userSessions = activeSessions.get(userId);
    if (userSessions) {
      userSessions.delete(sessionId);
      if (userSessions.size === 0) {
        activeSessions.delete(userId);
      }
    }
  },

  destroyAllUserSessions: (userId: string): void => {
    activeSessions.delete(userId);
  },

  blacklistToken: (token: string): void => {
    tokenBlacklist.add(token);
    // In production, set expiration time and use Redis
  },
};

// Export utility functions for use in auth routes
export const authUtils = {
  recordFailedAttempt,
  clearFailedAttempts,
  isAccountLocked,
};
