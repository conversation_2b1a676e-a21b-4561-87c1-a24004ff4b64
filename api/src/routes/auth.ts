import { Router, Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { z } from 'zod';
import { db } from '../db/index.js';
import { users } from '../db/schema/users.js';
import { roles as rolesTable, userRoles } from '../db/schema/permissions.js';
import { eq } from 'drizzle-orm';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { logger } from '../utils/structuredLogger';
import { SecurityLogger, AuditLogger } from '../utils/securityLogger';
import { sessionManager } from '../middleware/opalAuth.js';
import CampaignTriggerService from '../services/campaignTriggers.js';

const router = Router();

// Validation schemas
const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  name: z.string().min(1, 'Name is required'),
  role: z.enum(['patient', 'doctor', 'agent', 'admin']).optional().default('patient'),
  // Role removed from registration - all new users default to 'patient'
  // Role assignment must be done by admin through separate endpoint
});

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

// JWT utility functions
function generateToken(userId: string, email: string, roles: string[], sessionId: string): string {
  if (!process.env.JWT_SECRET) {
    throw new AppError('JWT_SECRET not configured', 500);
  }
  
  return jwt.sign(
    { 
      userId, 
      email, 
      roles, 
      sessionId 
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
}

// Register endpoint
router.post('/register', asyncHandler(async (req: Request, res: Response) => {
  const validatedData = registerSchema.parse(req.body);
  
  // Split name into firstName and lastName
  const nameParts = validatedData.name.trim().split(' ');
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';
  
  // Check if user already exists
  const existingUser = await db
    .select()
    .from(users)
    .where(eq(users.email, validatedData.email))
    .limit(1);

  if (existingUser.length > 0) {
    throw new AppError('User already exists with this email', 409);
  }

  // Hash password
  const saltRounds = 12;
  const passwordHash = await bcrypt.hash(validatedData.password, saltRounds);

  // Create user - all new registrations default to 'patient' role
  const [newUser] = await db
    .insert(users)
    .values({
      email: validatedData.email,
      passwordHash,
      firstName,
      lastName,
      role: 'patient', // Always default to patient for new registrations
    })
    .returning({
      id: users.id,
      email: users.email,
      firstName: users.firstName,
      lastName: users.lastName,
      role: users.role,
      createdAt: users.createdAt,
    });

  // Assign the user to the patient role in the permissions system
  const [patientRole] = await db
    .select()
    .from(rolesTable)
    .where(eq(rolesTable.name, 'patient'))
    .limit(1);

  if (patientRole) {
    await db
      .insert(userRoles)
      .values({
        userId: newUser.id,
        roleId: patientRole.id,
        assignedBy: newUser.id, // Self-assigned during registration
        assignedAt: new Date(),
      });
  }

  // Create session for OPAL authentication
  const sessionId = sessionManager.createSession(newUser.id);
  
  // Use the actual database role field
  const roles = [newUser.role];
  
  // Generate JWT token
  const token = generateToken(newUser.id, newUser.email, roles, sessionId);
  
  // Log audit event for user registration
  AuditLogger.logResourceOperation(req, 'CREATE', 'user', newUser.id, newUser.id, {
    email: newUser.email,
    role: roles[0],
  });

  // Log security event for successful registration
  SecurityLogger.logAuthSuccess(req, newUser.id, 'registration');

  // 🎯 TRIGGER CAMPAIGN: User Registration
  // This is where the magic happens! When a user registers, trigger any active campaigns
  await CampaignTriggerService.onUserRegistered(newUser.id, {
    email: newUser.email,
    firstName: newUser.firstName,
    lastName: newUser.lastName,
    role: newUser.role,
  });

  // Set HTTP-only cookie for authentication
  res.cookie('auth_token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax', // Allow cross-origin in development
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    path: '/',
    domain: process.env.NODE_ENV === 'production' ? undefined : 'localhost' // Set domain for localhost in development
  });

  res.status(201).json({
    message: 'User registered successfully',
    user: newUser,
    token, // Still return token for backward compatibility
  });
}));

// Login endpoint
router.post('/login', asyncHandler(async (req: Request, res: Response) => {
  const validatedData = loginSchema.parse(req.body);

  // Find user by email
  const [user] = await db
    .select()
    .from(users)
    .where(eq(users.email, validatedData.email))
    .limit(1);

  if (!user) {
    throw new AppError('Invalid email or password', 401);
  }

  // Check if user is active
  if (!user.isActive) {
    throw new AppError('Account is deactivated', 401);
  }

  // Check if user has a password (not social login only)
  if (!user.passwordHash) {
    SecurityLogger.logAuthFailure(req, user.email, 'no_password_set');
    throw new AppError('Invalid email or password', 401);
  }

  // Verify password
  const isValidPassword = await bcrypt.compare(validatedData.password, user.passwordHash);
  if (!isValidPassword) {
    // Log failed login attempt
    SecurityLogger.logAuthFailure(req, user.email, 'invalid_password');
    throw new AppError('Invalid email or password', 401);
  }

  // Update last login time
  await db
    .update(users)
    .set({ lastLoginAt: new Date() })
    .where(eq(users.id, user.id));

  // Create session for OPAL authentication
  const sessionId = sessionManager.createSession(user.id);

  // Use the actual database role field
  const roles = [user.role];
  
  // Generate JWT token with session ID and proper format
  const token = generateToken(user.id, user.email, roles, sessionId);
  
  // Log successful login
  SecurityLogger.logAuthSuccess(req, user.id, 'password');

  // Return user data (excluding password hash)
  const { passwordHash, ...userWithoutPassword } = user;

  // Set HTTP-only cookie for authentication
  res.cookie('auth_token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax', // Allow cross-origin in development
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    path: '/',
    domain: process.env.NODE_ENV === 'production' ? undefined : 'localhost' // Set domain for localhost in development
  });

  res.json({
    message: 'Login successful',
    user: userWithoutPassword,
    token, // Still return token for backward compatibility
  });
}));

// Logout endpoint (for audit logging and session cleanup)
router.post('/logout', asyncHandler(async (req: Request, res: Response) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      
      // Destroy the session if it exists
      if (decoded.sessionId) {
        sessionManager.destroySession(decoded.userId, decoded.sessionId);
      }
      
      // Log successful logout
      req.logger.info('User logout successful', {
        userId: decoded.userId,
        sessionId: decoded.sessionId,
      });
    } catch (error) {
      // Token invalid, but still log the logout attempt
      req.logger.warn('Logout attempt with invalid token', {
        error: 'invalid_token',
      });
    }
  }

  // Clear the HTTP-only cookie
  res.clearCookie('auth_token', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
    path: '/',
    domain: process.env.NODE_ENV === 'production' ? undefined : 'localhost'
  });

  res.json({ message: 'Logout successful' });
}));

// Get current user endpoint (for token validation)
router.get('/me', asyncHandler(async (req: Request, res: Response) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    throw new AppError('No token provided', 401);
  }

  try {
    // Verify and decode the JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    
    // Find user by ID from token
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, decoded.userId))
      .limit(1);

    if (!user) {
      throw new AppError('User not found', 404);
    }

    if (!user.isActive) {
      throw new AppError('Account is deactivated', 401);
    }

    // Return user data (excluding password hash)
    const { passwordHash, ...userWithoutPassword } = user;
    
    res.json({
      message: 'User data retrieved successfully',
      data: userWithoutPassword,
    });
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new AppError('Invalid token', 401);
    }
    throw error;
  }
}));

export { router as authRoutes };
