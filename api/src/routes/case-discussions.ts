import express from 'express';
import { db } from '../db';
import { caseDiscussions, discussionAttachments, medicalDocuments, cases, caseDoctors, users } from '../db/schema';
import { opalAuthMiddleware } from '../middleware/opalAuth';
import { CaseDoctorService } from '../services/caseDoctorService';
import { eq, and, desc } from 'drizzle-orm';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { storageService } from '../services/storageService';

import { logger } from '../utils/structuredLogger';
const router = express.Router();

// Set up multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});

// Validation schemas
const createDiscussionSchema = z.object({
  content: z.string().min(1),
  isVisibleToPatient: z.boolean().optional().default(true),
});

const updateDiscussionSchema = z.object({
  content: z.string().min(1),
  isRead: z.boolean().optional(),
});

// Helper function to extract user ID and role from request (simplified for opalAuthMiddleware)
const extractUserInfo = (req: express.Request) => {
  const opalUser = (req as any).user;
  const userId = opalUser?.id;
  const userRoles = opalUser?.roles;
  const userRole = userRoles?.[0] || 'patient';
  
  return { userId, userRole };
};

// Get all discussions for a case
router.get('/cases/:caseId/discussions', opalAuthMiddleware, async (req, res) => {
  try {
    const { caseId } = req.params;
    const { userId, userRole } = extractUserInfo(req);

    if (!userId) {
      logger.info('User authentication failed - no user ID found in request', { requestId: 'context-needed' }, { data: undefined });
      return res.status(401).json({ message: 'Unauthorized' });
    }
    
    // Check if user has access to this case
    const [caseRecord] = await db
      .select()
      .from(cases)
      .where(eq(cases.id, caseId))
      .limit(1);
    
    if (!caseRecord) {
      return res.status(404).json({ message: 'Case not found' });
    }
    
    // Verify user has permission to access this case
    let hasAccess = false;
    
    if (userRole === 'admin' || userRole === 'agent') {
      hasAccess = true;
    } else if (userRole === 'doctor') {
      // Check if doctor is assigned to this case
      const doctorAssignment = await db
        .select()
        .from(caseDoctors)
        .where(and(
          eq(caseDoctors.caseId, caseId),
          eq(caseDoctors.doctorId, userId)
        ))
        .limit(1);
      hasAccess = doctorAssignment.length > 0;
    } else if (userRole === 'patient') {
      // Patients can only access their own cases
      hasAccess = caseRecord.patientId === userId;
    }
    
    if (!hasAccess) {
      return res.status(403).json({ message: 'You do not have permission to access this case' });
    }

    // Build the where conditions based on user role
    const whereConditions = [
      eq(caseDiscussions.caseId, caseId),
      eq(caseDiscussions.isDeleted, false)
    ];

    // If user is a patient, only show discussions visible to patients
    if (userRole === 'patient') {
      whereConditions.push(eq(caseDiscussions.isVisibleToPatient, true));
    }

    // Get all discussions for the case with proper user information
    const discussions = await db
      .select({
        id: caseDiscussions.id,
        caseId: caseDiscussions.caseId,
        authorId: caseDiscussions.authorId,
        content: caseDiscussions.content,
        hasAttachments: caseDiscussions.hasAttachments,
        isRead: caseDiscussions.isRead,
        isDeleted: caseDiscussions.isDeleted,
        isVisibleToPatient: caseDiscussions.isVisibleToPatient,
        createdAt: caseDiscussions.createdAt,
        updatedAt: caseDiscussions.updatedAt,
        // Author information from users table
        authorFirstName: users.firstName,
        authorLastName: users.lastName,
        authorRole: users.role,
      })
      .from(caseDiscussions)
      .leftJoin(users, eq(caseDiscussions.authorId, users.id))
      .where(and(...whereConditions))
      .orderBy(desc(caseDiscussions.createdAt));

    // Return discussions with actual author information
    const discussionsWithAuthorInfo = discussions.map(discussion => ({
      id: discussion.id,
      caseId: discussion.caseId,
      authorId: discussion.authorId,
      content: discussion.content,
      hasAttachments: discussion.hasAttachments,
      isRead: discussion.isRead,
      isDeleted: discussion.isDeleted,
      isVisibleToPatient: discussion.isVisibleToPatient,
      createdAt: discussion.createdAt,
      updatedAt: discussion.updatedAt,
      author: {
        id: discussion.authorId,
        firstName: discussion.authorFirstName || 'Unknown',
        lastName: discussion.authorLastName || 'User',
        role: discussion.authorRole || 'patient',
      },
      attachments: [], // Simplified - no attachments for now to avoid any potential joins
    }));

    return res.status(200).json(discussionsWithAuthorInfo);
  } catch (error) {
    logger.error('Error fetching case discussions:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Create a new case discussion
router.post('/cases/:caseId/discussions', opalAuthMiddleware, async (req, res) => {
  try {
    const { caseId } = req.params;
    const { userId, userRole } = extractUserInfo(req);

    if (!userId) {
      logger.info('User authentication failed - no user ID found in request', { requestId: 'context-needed' }, { data: undefined });
      return res.status(401).json({ message: 'Unauthorized' });
    }
    
    // Check if user has access to this case
    const [caseRecord] = await db
      .select()
      .from(cases)
      .where(eq(cases.id, caseId))
      .limit(1);
    
    if (!caseRecord) {
      return res.status(404).json({ message: 'Case not found' });
    }
    
    // Verify user has permission to access this case
    let hasAccess = false;
    
    if (userRole === 'admin' || userRole === 'agent') {
      hasAccess = true;
    } else if (userRole === 'doctor') {
      // Check if doctor is assigned to this case
      const doctorAssignment = await db
        .select()
        .from(caseDoctors)
        .where(and(
          eq(caseDoctors.caseId, caseId),
          eq(caseDoctors.doctorId, userId)
        ))
        .limit(1);
      hasAccess = doctorAssignment.length > 0;
    } else if (userRole === 'patient') {
      // Patients can only access their own cases
      hasAccess = caseRecord.patientId === userId;
    }
    
    if (!hasAccess) {
      return res.status(403).json({ message: 'You do not have permission to access this case' });
    }

    // Validate request body
    const validationResult = createDiscussionSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        message: 'Invalid request data', 
        errors: validationResult.error.errors 
      });
    }

    let { content, isVisibleToPatient } = validationResult.data;

    // For doctors: check acceptance status and restrict public messages if not accepted
    if (userRole === 'doctor') {
      const hasAccepted = await CaseDoctorService.hasDoctorAcceptedCase(userId, caseId);
      if (!hasAccepted && isVisibleToPatient) {
        return res.status(403).json({
          message: 'You must accept the case assignment before sending public messages. You can only send private messages to other doctors until you accept the case.',
          code: 'DOCTOR_NOT_ACCEPTED_PUBLIC_MESSAGE'
        });
      }
    }

    // Create the discussion
    const [newDiscussion] = await db.insert(caseDiscussions).values({
      id: uuidv4(),
      caseId,
      authorId: userId,
      content,
      hasAttachments: false,
      isVisibleToPatient,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    // Get the author's actual name and role from the users table
    const [author] = await db
      .select({
        firstName: users.firstName,
        lastName: users.lastName,
        role: users.role,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    // Return discussion with actual author info
    return res.status(201).json({
      ...newDiscussion,
      author: {
        id: userId,
        firstName: author?.firstName || 'Unknown',
        lastName: author?.lastName || 'User',
        role: author?.role || 'patient',
      },
      attachments: [],
    });
  } catch (error) {
    logger.error('Error creating case discussion:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Upload file attachment with new discussion (matches frontend expectation)
router.post('/cases/:caseId/discussions/upload', opalAuthMiddleware, upload.single('file'), async (req, res) => {
  try {
    const { caseId } = req.params;
    const { userId, userRole } = extractUserInfo(req);
    const file = req.file;
    const { content, isVisibleToPatient } = req.body;

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (!file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // Check if user has access to this case
    const [caseRecord] = await db
      .select()
      .from(cases)
      .where(eq(cases.id, caseId))
      .limit(1);
    
    if (!caseRecord) {
      return res.status(404).json({ message: 'Case not found' });
    }
    
    // Verify user has permission to access this case
    let hasAccess = false;
    
    if (userRole === 'admin' || userRole === 'agent') {
      hasAccess = true;
    } else if (userRole === 'doctor') {
      // Check if doctor is assigned to this case
      const doctorAssignment = await db
        .select()
        .from(caseDoctors)
        .where(and(
          eq(caseDoctors.caseId, caseId),
          eq(caseDoctors.doctorId, userId)
        ))
        .limit(1);
      hasAccess = doctorAssignment.length > 0;
    } else if (userRole === 'patient') {
      // Patients can only access their own cases
      hasAccess = caseRecord.patientId === userId;
    }
    
    if (!hasAccess) {
      return res.status(403).json({ message: 'You do not have permission to access this case' });
    }

    // For doctors: check acceptance status and restrict public messages if not accepted
    const finalIsVisibleToPatient = isVisibleToPatient === 'true' || isVisibleToPatient === true;
    if (userRole === 'doctor') {
      const hasAccepted = await CaseDoctorService.hasDoctorAcceptedCase(userId, caseId);
      if (!hasAccepted && finalIsVisibleToPatient) {
        return res.status(403).json({
          message: 'You must accept the case assignment before sending public messages. You can only send private messages to other doctors until you accept the case.',
          code: 'DOCTOR_NOT_ACCEPTED_PUBLIC_MESSAGE'
        });
      }
    }

    // Create the discussion first
    const [newDiscussion] = await db.insert(caseDiscussions).values({
      id: uuidv4(),
      caseId,
      authorId: userId,
      content: content || `Shared a file: ${file.originalname}`,
      hasAttachments: true,
      isVisibleToPatient: finalIsVisibleToPatient,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    // Upload file to storage service
    const uploadResult = await storageService.uploadFile({
      buffer: file.buffer,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    }, userId);

    // Create document record
    const [document] = await db.insert(medicalDocuments).values({
      id: uuidv4(),
      caseId: caseId,
      uploadedBy: userId,
      title: file.originalname,
      fileName: uploadResult.fileName,
      originalFileName: uploadResult.originalFileName,
      filePath: uploadResult.filePath,
      fileSize: uploadResult.fileSize,
      mimeType: uploadResult.mimeType,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    // Create attachment record
    await db.insert(discussionAttachments).values({
      id: uuidv4(),
      discussionId: newDiscussion.id,
      documentId: document.id,
      createdAt: new Date(),
    });

    // Get the author's actual name and role from the users table
    const [author] = await db
      .select({
        firstName: users.firstName,
        lastName: users.lastName,
        role: users.role,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    // Return discussion with actual author info and attachment
    return res.status(201).json({
      ...newDiscussion,
      author: {
        id: userId,
        firstName: author?.firstName || 'Unknown',
        lastName: author?.lastName || 'User',
        role: author?.role || 'patient',
      },
      attachments: [{
        id: document.id,
        filename: document.originalFileName,
        fileSize: document.fileSize,
        fileType: document.mimeType,
        url: `/api/storage/documents/${document.id}/stream`
      }],
    });
  } catch (error) {
    logger.error('Error uploading file attachment to case discussion:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Upload file attachment for a case discussion
router.post('/discussions/:discussionId/attachments', opalAuthMiddleware, upload.single('file'), async (req, res) => {
  try {
    const { discussionId } = req.params;
    const { userId, userRole } = extractUserInfo(req);
    const file = req.file;

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (!file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // Get the discussion to ensure it exists
    const [discussion] = await db
      .select()
      .from(caseDiscussions)
      .where(eq(caseDiscussions.id, discussionId))
      .limit(1);

    if (!discussion) {
      return res.status(404).json({ message: 'Discussion not found' });
    }

    // Check if user has access to this case
    const [caseRecord] = await db
      .select()
      .from(cases)
      .where(eq(cases.id, discussion.caseId))
      .limit(1);
    
    if (!caseRecord) {
      return res.status(404).json({ message: 'Case not found' });
    }
    
    // Verify user has permission to access this case
    let hasAccess = false;
    
    if (userRole === 'admin' || userRole === 'agent') {
      hasAccess = true;
    } else if (userRole === 'doctor') {
      // Check if doctor is assigned to this case
      const doctorAssignment = await db
        .select()
        .from(caseDoctors)
        .where(and(
          eq(caseDoctors.caseId, discussion.caseId),
          eq(caseDoctors.doctorId, userId)
        ))
        .limit(1);
      hasAccess = doctorAssignment.length > 0;
    } else if (userRole === 'patient') {
      // Patients can only access their own cases
      hasAccess = caseRecord.patientId === userId;
    }
    
    if (!hasAccess) {
      return res.status(403).json({ message: 'You do not have permission to access this case' });
    }

    // Only allow the author to add attachments
    if (discussion.authorId !== userId) {
      return res.status(403).json({ message: 'You can only add attachments to your own discussions' });
    }

    // Upload file to storage service
    const uploadResult = await storageService.uploadFile({
      buffer: file.buffer,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    }, userId);

    // Create document record
    const [document] = await db.insert(medicalDocuments).values({
      id: uuidv4(),
      caseId: discussion.caseId,
      uploadedBy: userId,
      title: file.originalname,
      fileName: uploadResult.fileName,
      originalFileName: uploadResult.originalFileName,
      filePath: uploadResult.filePath,
      fileSize: uploadResult.fileSize,
      mimeType: uploadResult.mimeType,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    // Create attachment record
    await db.insert(discussionAttachments).values({
      id: uuidv4(),
      discussionId,
      documentId: document.id,
      createdAt: new Date(),
    });

    // Update discussion to indicate it has attachments
    await db.update(caseDiscussions)
      .set({ hasAttachments: true, updatedAt: new Date() })
      .where(eq(caseDiscussions.id, discussionId));

    return res.status(201).json(document);
  } catch (error) {
    logger.error('Error uploading file attachment to case discussion:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Update case discussion or mark as read
router.patch('/discussions/:discussionId', opalAuthMiddleware, async (req, res) => {
  try {
    const { discussionId } = req.params;
    const { userId, userRole } = extractUserInfo(req);

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Validate request body
    const validationResult = updateDiscussionSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        message: 'Invalid request data', 
        errors: validationResult.error.errors 
      });
    }

    const { content, isRead } = validationResult.data;

    // Get the discussion to ensure it exists
    const [discussion] = await db
      .select()
      .from(caseDiscussions)
      .where(eq(caseDiscussions.id, discussionId))
      .limit(1);

    if (!discussion) {
      return res.status(404).json({ message: 'Discussion not found' });
    }
    
    // Check if user has access to this case
    const [caseRecord] = await db
      .select()
      .from(cases)
      .where(eq(cases.id, discussion.caseId))
      .limit(1);
    
    if (!caseRecord) {
      return res.status(404).json({ message: 'Case not found' });
    }
    
    // Verify user has permission to access this case
    let hasAccess = false;
    
    if (userRole === 'admin' || userRole === 'agent') {
      hasAccess = true;
    } else if (userRole === 'doctor') {
      // Check if doctor is assigned to this case
      const doctorAssignment = await db
        .select()
        .from(caseDoctors)
        .where(and(
          eq(caseDoctors.caseId, discussion.caseId),
          eq(caseDoctors.doctorId, userId)
        ))
        .limit(1);
      hasAccess = doctorAssignment.length > 0;
    } else if (userRole === 'patient') {
      // Patients can only access their own cases
      hasAccess = caseRecord.patientId === userId;
    }
    
    if (!hasAccess) {
      return res.status(403).json({ message: 'You do not have permission to access this case' });
    }

    // Update the discussion
    const updateData: any = { updatedAt: new Date() };
    
    if (content !== undefined) {
      // Only allow editing content if the user is the author
      if (discussion.authorId !== userId) {
        return res.status(403).json({ message: 'You can only edit your own discussions' });
      }
      updateData.content = content;
    }
    
    if (isRead !== undefined) {
      updateData.isRead = isRead;
    }

    const [updatedDiscussion] = await db.update(caseDiscussions)
      .set(updateData)
      .where(eq(caseDiscussions.id, discussionId))
      .returning();

    return res.status(200).json(updatedDiscussion);
  } catch (error) {
    logger.error('Error updating case discussion:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

// Delete a case discussion (soft delete)
router.delete('/discussions/:discussionId', opalAuthMiddleware, async (req, res) => {
  try {
    const { discussionId } = req.params;
    const { userId, userRole } = extractUserInfo(req);

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the discussion to ensure it exists
    const [discussion] = await db
      .select()
      .from(caseDiscussions)
      .where(eq(caseDiscussions.id, discussionId))
      .limit(1);

    if (!discussion) {
      return res.status(404).json({ message: 'Discussion not found' });
    }
    
    // Check if user has access to this case
    const [caseRecord] = await db
      .select()
      .from(cases)
      .where(eq(cases.id, discussion.caseId))
      .limit(1);
    
    if (!caseRecord) {
      return res.status(404).json({ message: 'Case not found' });
    }
    
    // Verify user has permission to access this case
    let hasAccess = false;
    
    if (userRole === 'admin' || userRole === 'agent') {
      hasAccess = true;
    } else if (userRole === 'doctor') {
      // Check if doctor is assigned to this case
      const doctorAssignment = await db
        .select()
        .from(caseDoctors)
        .where(and(
          eq(caseDoctors.caseId, discussion.caseId),
          eq(caseDoctors.doctorId, userId)
        ))
        .limit(1);
      hasAccess = doctorAssignment.length > 0;
    } else if (userRole === 'patient') {
      // Patients can only access their own cases
      hasAccess = caseRecord.patientId === userId;
    }
    
    if (!hasAccess) {
      return res.status(403).json({ message: 'You do not have permission to access this case' });
    }

    // Only allow deletion if the user is the author
    if (discussion.authorId !== userId) {
      return res.status(403).json({ message: 'You can only delete your own discussions' });
    }

    // Soft delete the discussion
    await db.update(caseDiscussions)
      .set({ isDeleted: true, updatedAt: new Date() })
      .where(eq(caseDiscussions.id, discussionId));

    return res.status(200).json({ message: 'Discussion deleted successfully' });
  } catch (error) {
    logger.error('Error deleting case discussion:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
});

export default router;
