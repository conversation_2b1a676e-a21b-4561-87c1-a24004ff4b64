import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../db/index.js';
import { eq, and, desc, like, or, asc, inArray, sql } from 'drizzle-orm';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { opalAuthMiddleware, requireRole } from '../middleware/opalAuth.js';
import { logAuditEvent } from '../utils/logger.js';
import CampaignTriggerService from '../services/campaignTriggers.js';
import { 
  organizations as crmOrganizations,
  contacts as crmContacts,
  touchpoints as crmTouchpoints,
  communicationPlans,
  communicationPlanSteps,
  contactPlanAssignments,
  referrals,
  organizationTypeEnum,
  contactStatusEnum,
  touchpointTypeEnum
} from '../db/schema/crm.js';
import { users } from '../db/schema/users.js';

import { logger } from '../utils/structuredLogger';
// Define User type directly to fix import error
interface User {
  id: string;
  email: string;
  role: string;
  firstName: string;
  lastName: string;
}

const router = Router();

// ===== Organization Validation Schemas =====
const createOrganizationSchema = z.object({
  name: z.string().min(1, 'Organization name is required'),
  type: z.enum(['hospital', 'clinic', 'private_practice', 'insurance', 'pharmacy', 'other']),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  country: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email('Invalid email address').optional(),
  website: z.string().url('Invalid website URL').optional(),
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

const updateOrganizationSchema = createOrganizationSchema.partial();

// ===== Organization Endpoints =====

// Create organization (admin and doctor roles only)
router.post('/organizations', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  const validatedData = createOrganizationSchema.parse(req.body);
  
  const [newOrganization] = await db
    .insert(crmOrganizations)
    .values({
      ...validatedData,
      createdBy: userId,
    })
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ORGANIZATION_CREATED',
    'crm_organizations',
    {
      organizationId: newOrganization.id,
      organizationType: newOrganization.type,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.status(201).json({
    message: 'Organization created successfully',
    organization: newOrganization,
  });
}));

// Get all organizations with filtering and pagination
router.get('/organizations', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Extract query parameters
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search as string;
  const type = req.query.type as typeof organizationTypeEnum.enumValues[number];
  const sortBy = req.query.sortBy as string || 'name';
  const sortOrder = req.query.sortOrder as string || 'asc';
  
  // Build query
  let query = db.select().from(crmOrganizations);
  
  // Apply filters
  if (search) {
    query = query.where(
      or(
        like(crmOrganizations.name, `%${search}%`),
        like(crmOrganizations.city, `%${search}%`),
        like(crmOrganizations.state, `%${search}%`)
      )
    );
  }
  
  if (type) {
    query = query.where(eq(crmOrganizations.type, type));
  }
  
  // Apply role-based access control
  if (userRole === 'doctor') {
    // Doctors can only see organizations they created or are associated with
    query = query.where(eq(crmOrganizations.createdBy, userId));
    // TODO: Add logic to show organizations the doctor is associated with through contacts or referrals
  }
  
  // Get total count for pagination
  const totalCountResult = await db
    .select({ count: sql`count(*)` })
    .from(crmOrganizations)
    .execute();
  
  const total = Number(totalCountResult[0]?.count || 0);
  
  // Apply sorting
  if (sortBy === 'name') {
    query = sortOrder === 'desc' 
      ? query.orderBy(desc(crmOrganizations.name))
      : query.orderBy(asc(crmOrganizations.name));
  } else if (sortBy === 'createdAt') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(crmOrganizations.createdAt))
      : query.orderBy(asc(crmOrganizations.createdAt));
  }
  
  // Apply pagination
  query = query.limit(limit).offset(offset);
  
  // Execute query
  const organizations = await query.execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ORGANIZATIONS_ACCESSED',
    'crm_organizations',
    {
      filters: { search, type, sortBy, sortOrder },
      pagination: { page, limit },
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    organizations,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get organization by ID
router.get('/organizations/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const organizationId = req.params.id;
  
  // Build query
  let query = db
    .select()
    .from(crmOrganizations)
    .where(eq(crmOrganizations.id, organizationId));
  
  // Apply role-based access control
  if (userRole === 'doctor') {
    // Doctors can only see organizations they created or are associated with
    query = query.where(eq(crmOrganizations.createdBy, userId));
    // TODO: Add logic to show organizations the doctor is associated with through contacts or referrals
  }
  
  // Execute query
  const organization = await query.execute();
  
  if (!organization || organization.length === 0) {
    throw new AppError('Organization not found', 404);
  }
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ORGANIZATION_ACCESSED',
    'crm_organizations',
    {
      organizationId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    organization: organization[0],
  });
}));

// Update organization by ID
router.put('/organizations/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const organizationId = req.params.id;
  
  const validatedData = updateOrganizationSchema.parse(req.body);
  
  // Check if organization exists and user has access
  const existingOrg = await db
    .select()
    .from(crmOrganizations)
    .where(eq(crmOrganizations.id, organizationId))
    .execute();
  
  if (!existingOrg || existingOrg.length === 0) {
    throw new AppError('Organization not found', 404);
  }
  
  // Apply role-based access control
  if (userRole === 'doctor' && existingOrg[0].createdBy !== userId) {
    throw new AppError('You do not have permission to update this organization', 403);
  }
  
  // Update organization
  const [updatedOrganization] = await db
    .update(crmOrganizations)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(eq(crmOrganizations.id, organizationId))
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ORGANIZATION_UPDATED',
    'crm_organizations',
    {
      organizationId,
      changes: validatedData,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Organization updated successfully',
    organization: updatedOrganization,
  });
}));

// Delete organization by ID (admin only)
router.delete('/organizations/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const organizationId = req.params.id;
  
  // Check if organization exists
  const existingOrg = await db
    .select()
    .from(crmOrganizations)
    .where(eq(crmOrganizations.id, organizationId))
    .execute();
  
  if (!existingOrg || existingOrg.length === 0) {
    throw new AppError('Organization not found', 404);
  }
  
  // Delete organization (soft delete by setting isActive to false)
  const [deletedOrganization] = await db
    .update(crmOrganizations)
    .set({
      isActive: false,
      updatedAt: new Date(),
    })
    .where(eq(crmOrganizations.id, organizationId))
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ORGANIZATION_DELETED',
    'crm_organizations',
    {
      organizationId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Organization deleted successfully',
    organization: deletedOrganization,
  });
}));

// ===== Contact Validation Schemas =====
const createContactSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address').optional(),
  phone: z.string().optional(),
  organizationId: z.string().uuid('Invalid organization ID').optional(),
  jobTitle: z.string().optional(),
  status: z.enum(['lead', 'prospect', 'customer', 'inactive']).default('lead'),
  source: z.string().optional(),
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

const updateContactSchema = createContactSchema.partial();

// ===== Contact Endpoints =====

// Create contact (admin and doctor roles only)
router.post('/contacts', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  const validatedData = createContactSchema.parse(req.body);
  
  // If organizationId is provided, verify it exists
  if (validatedData.organizationId) {
    const organization = await db
      .select()
      .from(crmOrganizations)
      .where(eq(crmOrganizations.id, validatedData.organizationId))
      .execute();
    
    if (!organization || organization.length === 0) {
      throw new AppError('Organization not found', 404);
    }
    
    // If doctor, verify they have access to this organization
    if (userRole === 'doctor') {
      if (organization[0].createdBy !== userId) {
        throw new AppError('You do not have permission to associate with this organization', 403);
      }
    }
  }
  
  const [newContact] = await db
    .insert(crmContacts)
    .values({
      ...validatedData,
      createdBy: userId,
    })
    .returning();
  
  // 🎯 TRIGGER CAMPAIGN: Contact Creation
  // When a new contact is created, trigger any active campaigns
  await CampaignTriggerService.onContactCreated(newContact.id, {
    firstName: newContact.firstName,
    lastName: newContact.lastName,
    email: newContact.email,
    phone: newContact.phone,
    status: newContact.status,
    organizationId: newContact.organizationId,
  });
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACT_CREATED',
    'crm_contacts',
    {
      contactId: newContact.id,
      contactStatus: newContact.status,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.status(201).json({
    message: 'Contact created successfully',
    contact: newContact,
  });
}));

// Get all contacts with filtering and pagination
router.get('/contacts', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Extract query parameters
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search as string;
  const status = req.query.status as contactStatus;
  const organizationId = req.query.organizationId as string;
  const sortBy = req.query.sortBy as string || 'lastName';
  const sortOrder = req.query.sortOrder as string || 'asc';
  
  // Build query
  let query = db.select({
    ...crmContacts,
    organizationName: crmOrganizations.name,
  })
  .from(crmContacts)
  .leftJoin(crmOrganizations, eq(crmContacts.organizationId, crmOrganizations.id));
  
  // Apply filters
  if (search) {
    query = query.where(
      or(
        like(crmContacts.firstName, `%${search}%`),
        like(crmContacts.lastName, `%${search}%`),
        like(crmContacts.email, `%${search}%`)
      )
    );
  }
  
  if (status) {
    query = query.where(eq(crmContacts.status, status));
  }
  
  if (organizationId) {
    query = query.where(eq(crmContacts.organizationId, organizationId));
  }
  
  // Apply role-based access control
  if (userRole === 'doctor') {
    // Doctors can only see contacts they created
    query = query.where(eq(crmContacts.createdBy, userId));
  }
  
  // Get total count for pagination
  const totalCountResult = await db
    .select({ count: sql`count(*)` })
    .from(crmContacts)
    .execute();
  
  const total = Number(totalCountResult[0]?.count || 0);
  
  // Apply sorting
  if (sortBy === 'lastName') {
    query = sortOrder === 'desc' 
      ? query.orderBy(desc(crmContacts.lastName))
      : query.orderBy(asc(crmContacts.lastName));
  } else if (sortBy === 'createdAt') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(crmContacts.createdAt))
      : query.orderBy(asc(crmContacts.createdAt));
  }
  
  // Apply pagination
  query = query.limit(limit).offset(offset);
  
  // Execute query
  const contacts = await query.execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACTS_ACCESSED',
    'crm_contacts',
    {
      filters: { search, status, organizationId, sortBy, sortOrder },
      pagination: { page, limit },
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    contacts,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get contact by ID
router.get('/contacts/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const contactId = req.params.id;
  
  // Build query with organization info
  let query = db.select({
    ...crmContacts,
    organization: crmOrganizations,
  })
  .from(crmContacts)
  .leftJoin(crmOrganizations, eq(crmContacts.organizationId, crmOrganizations.id))
  .where(eq(crmContacts.id, contactId));
  
  // Apply role-based access control
  if (userRole === 'doctor') {
    // Doctors can only see contacts they created
    query = query.where(eq(crmContacts.createdBy, userId));
  }
  
  // Execute query
  const result = await query.execute();
  
  if (!result || result.length === 0) {
    throw new AppError('Contact not found', 404);
  }
  
  // Format the response
  const contact = result[0];
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACT_ACCESSED',
    'crm_contacts',
    {
      contactId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    contact,
  });
}));

// Update contact by ID
router.put('/contacts/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const contactId = req.params.id;
  
  const validatedData = updateContactSchema.parse(req.body);
  
  // Check if contact exists and user has access
  const existingContact = await db
    .select()
    .from(crmContacts)
    .where(eq(crmContacts.id, contactId))
    .execute();
  
  if (!existingContact || existingContact.length === 0) {
    throw new AppError('Contact not found', 404);
  }
  
  // Apply role-based access control
  if (userRole === 'doctor' && existingContact[0].createdBy !== userId) {
    throw new AppError('You do not have permission to update this contact', 403);
  }
  
  // If organizationId is provided, verify it exists
  if (validatedData.organizationId) {
    const organization = await db
      .select()
      .from(crmOrganizations)
      .where(eq(crmOrganizations.id, validatedData.organizationId))
      .execute();
    
    if (!organization || organization.length === 0) {
      throw new AppError('Organization not found', 404);
    }
    
    // If doctor, verify they have access to this organization
    if (userRole === 'doctor') {
      if (organization[0].createdBy !== userId) {
        throw new AppError('You do not have permission to associate with this organization', 403);
      }
    }
  }
  
  // Update contact
  const [updatedContact] = await db
    .update(crmContacts)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(eq(crmContacts.id, contactId))
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACT_UPDATED',
    'crm_contacts',
    {
      contactId,
      changes: validatedData,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Contact updated successfully',
    contact: updatedContact,
  });
}));

// Delete contact by ID (admin only)
router.delete('/contacts/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const contactId = req.params.id;
  
  // Check if contact exists
  const existingContact = await db
    .select()
    .from(crmContacts)
    .where(eq(crmContacts.id, contactId))
    .execute();
  
  if (!existingContact || existingContact.length === 0) {
    throw new AppError('Contact not found', 404);
  }
  
  // Delete contact (soft delete by setting status to inactive)
  const [deletedContact] = await db
    .update(crmContacts)
    .set({
      status: 'inactive',
      updatedAt: new Date(),
    })
    .where(eq(crmContacts.id, contactId))
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACT_DELETED',
    'crm_contacts',
    {
      contactId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Contact deleted successfully',
    contact: deletedContact,
  });
}));

// ===== Touchpoint Validation Schemas =====
const createTouchpointSchema = z.object({
  contactId: z.string().uuid('Invalid contact ID'),
  organizationId: z.string().uuid('Invalid organization ID').optional(),
  type: z.enum(touchpointTypeEnum.enumValues).default('other'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  date: z.coerce.date(),
  outcome: z.string().optional(),
  followUpRequired: z.boolean().default(false),
  followUpDate: z.coerce.date().optional(),
});

const updateTouchpointSchema = createTouchpointSchema.partial();

// ===== Communication Plan Validation Schemas =====
const createCommunicationPlanSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  targetAudience: z.string().optional(),
  duration: z.number().int().positive().optional(),
  isActive: z.boolean().default(true),
});

const updateCommunicationPlanSchema = createCommunicationPlanSchema.partial();

// ===== Communication Plan Step Validation Schemas =====
const createCommunicationPlanStepSchema = z.object({
  planId: z.string().uuid('Invalid plan ID'),
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  type: z.enum(touchpointTypeEnum.enumValues).default('email'),
  dayOffset: z.number().int().min(0, 'Day offset must be a positive number'),
  template: z.string().optional(),
});

const updateCommunicationPlanStepSchema = createCommunicationPlanStepSchema.partial();

// ===== Contact Plan Assignment Validation Schemas =====
const createContactPlanAssignmentSchema = z.object({
  contactId: z.string().uuid('Invalid contact ID'),
  planId: z.string().uuid('Invalid plan ID'),
  startDate: z.coerce.date().default(() => new Date()),
  endDate: z.coerce.date().optional(),
  isActive: z.boolean().default(true),
  progress: z.number().int().min(0).max(100).default(0),
});

const updateContactPlanAssignmentSchema = createContactPlanAssignmentSchema.partial();

// ===== Communication Plan Routes =====

// GET /api/crm/communication-plans - Get all communication plans
router.get('/communication-plans', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    // Add role-based filtering
    const user = (req.user as unknown) as User;
    const isAdmin = user.role === 'admin';
    
    let query = db
      .select()
      .from(communicationPlans)
      .leftJoin(users, eq(communicationPlans.createdBy, users.id))
      .orderBy(communicationPlans.createdAt);
    
    // If not admin, only show plans created by the user
    if (!isAdmin) {
      query = (query as any).where(eq(communicationPlans.createdBy, user.id));
    }
    
    const plans = await query;
    
    // Format the response
    const formattedPlans = plans.map(plan => ({
      ...plan.crm_communication_plans,
      createdBy: plan.users ? {
        id: plan.users.id,
        firstName: plan.users.firstName,
        lastName: plan.users.lastName,
        email: plan.users.email
      } : null
    }));
    
    // Log the action
    logAuditEvent(
      user.id,
      'read',
      'communication_plans',
      { description: 'Retrieved communication plans list' }
    );
    
    return res.status(200).json({
      plans: formattedPlans,
      total: formattedPlans.length
    });
  } catch (error) {
    logger.error('Error fetching communication plans:', error);
    return res.status(500).json({ error: 'Failed to fetch communication plans' });
  }
});

// GET /api/crm/communication-plans/:id - Get a specific communication plan
router.get('/communication-plans/:id', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const user = (req.user as unknown) as User;
    const isAdmin = user.role === 'admin';
    
    // Get the plan with creator info
    const planResult = await db
      .select()
      .from(communicationPlans)
      .leftJoin(users, eq(communicationPlans.createdBy, users.id))
      .where(eq(communicationPlans.id, id));
    
    if (!planResult || planResult.length === 0) {
      return res.status(404).json({ error: 'Communication plan not found' });
    }
    
    const plan = planResult[0];
    
    // Check permissions - only admin or creator can view
    if (!isAdmin && plan.crm_communication_plans.createdBy !== user.id) {
      return res.status(403).json({ error: 'You do not have permission to view this plan' });
    }
    
    // Get the plan steps
    const steps = await db
      .select()
      .from(communicationPlanSteps)
      .where(eq(communicationPlanSteps.planId, id))
      .orderBy(communicationPlanSteps.dayOffset);
    
    // Format the response
    const formattedPlan = {
      ...plan.crm_communication_plans,
      createdBy: plan.users ? {
        id: plan.users.id,
        firstName: plan.users.firstName,
        lastName: plan.users.lastName,
        email: plan.users.email
      } : null,
      steps: steps
    };
    
    // Log the action
    logAuditEvent(
      user.id,
      'read',
      'communication_plans',
      {
        resourceId: id,
        description: `Viewed communication plan: ${plan.crm_communication_plans.name}`
      }
    );
    
    return res.status(200).json(formattedPlan);
  } catch (error) {
    logger.error('Error fetching communication plan:', error);
    return res.status(500).json({ error: 'Failed to fetch communication plan' });
  }
});

// GET /api/crm/communication-plans/:id/assignments - Get assignments for a specific plan
router.get('/communication-plans/:id/assignments', opalAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const user = (req.user as unknown) as User;
    const isAdmin = user.role === 'admin';
    
    // Check if plan exists and user has permission
    const plan = await db
      .select()
      .from(communicationPlans)
      .where(eq(communicationPlans.id, id))
      .limit(1);
    
    if (!plan || plan.length === 0) {
      return res.status(404).json({ error: 'Communication plan not found' });
    }
    
    // Check permissions - only admin or creator can view
    if (!isAdmin && plan[0].createdBy !== user.id) {
      return res.status(403).json({ error: 'You do not have permission to view this plan\'s assignments' });
    }
    
    // Get assignments with contact info
    const assignments = await db
      .select()
      .from(contactPlanAssignments)
      .leftJoin(crmContacts, eq(contactPlanAssignments.contactId, crmContacts.id))
      .leftJoin(users, eq(contactPlanAssignments.assignedBy, users.id))
      .where(eq(contactPlanAssignments.planId, id));
    
    // Format the response
    const formattedAssignments = assignments.map(assignment => ({
      ...assignment.crm_contact_plan_assignments,
      contact: assignment.crm_contacts ? {
        id: assignment.crm_contacts.id,
        firstName: assignment.crm_contacts.firstName,
        lastName: assignment.crm_contacts.lastName,
        email: assignment.crm_contacts.email
      } : null,
      assignedBy: assignment.users ? {
        id: assignment.users.id,
        firstName: assignment.users.firstName,
        lastName: assignment.users.lastName,
        email: assignment.users.email
      } : null
    }));
    
    // Log the action
    logAuditEvent(
      user.id,
      'read',
      'contact_plan_assignments',
      {
        resourceId: id,
        description: `Retrieved assignments for communication plan: ${plan[0].name}`
      }
    );
    
    return res.status(200).json({
      assignments: formattedAssignments,
      total: formattedAssignments.length
    });
  } catch (error) {
    logger.error('Error fetching plan assignments:', error);
    return res.status(500).json({ error: 'Failed to fetch plan assignments' });
  }
});

// ===== Communication Plan Steps Endpoints =====

// POST /api/crm/communication-plan-steps - Create a communication plan step
router.post('/communication-plan-steps', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const user = (req.user as unknown) as User;
  const validatedData = createCommunicationPlanStepSchema.parse(req.body);
  
  // Verify plan exists
  const plan = await db
    .select()
    .from(communicationPlans)
    .where(eq(communicationPlans.id, validatedData.planId))
    .limit(1);
  
  if (!plan || plan.length === 0) {
    throw new AppError('Communication plan not found', 404);
  }
  
  // Create the step
  const [newStep] = await db
    .insert(communicationPlanSteps)
    .values({
      ...validatedData,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    .returning();
  
  // Log the action
  logAuditEvent(
    user.id,
    'create',
    'communication_plan_steps',
    {
      resourceId: newStep.id,
      planId: validatedData.planId,
      description: `Created step: ${validatedData.name} for plan: ${plan[0].name}`
    }
  );
  
  return res.status(201).json(newStep);
}));

// PUT /api/crm/communication-plan-steps/:id - Update a communication plan step
router.put('/communication-plan-steps/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const user = (req.user as unknown) as User;
  const { id } = req.params;
  const validatedData = updateCommunicationPlanStepSchema.parse(req.body);
  
  // Verify step exists
  const existingStep = await db
    .select()
    .from(communicationPlanSteps)
    .where(eq(communicationPlanSteps.id, id))
    .limit(1);
  
  if (!existingStep || existingStep.length === 0) {
    throw new AppError('Communication plan step not found', 404);
  }
  
  // Update the step
  const [updatedStep] = await db
    .update(communicationPlanSteps)
    .set({
      ...validatedData,
      updatedAt: new Date()
    })
    .where(eq(communicationPlanSteps.id, id))
    .returning();
  
  // Log the action
  logAuditEvent(
    user.id,
    'update',
    'communication_plan_steps',
    {
      resourceId: id,
      planId: updatedStep.planId,
      description: `Updated step: ${updatedStep.name}`
    }
  );
  
  return res.status(200).json(updatedStep);
}));

// DELETE /api/crm/communication-plan-steps/:id - Delete a communication plan step
router.delete('/communication-plan-steps/:id', opalAuthMiddleware, requireRole(['admin']), asyncHandler(async (req: Request, res: Response) => {
  const user = (req.user as unknown) as User;
  const { id } = req.params;
  
  // Verify step exists
  const existingStep = await db
    .select()
    .from(communicationPlanSteps)
    .where(eq(communicationPlanSteps.id, id))
    .limit(1);
  
  if (!existingStep || existingStep.length === 0) {
    throw new AppError('Communication plan step not found', 404);
  }
  
  // Delete the step
  await db
    .delete(communicationPlanSteps)
    .where(eq(communicationPlanSteps.id, id));
  
  // Log the action
  logAuditEvent(
    user.id,
    'delete',
    'communication_plan_steps',
    {
      resourceId: id,
      planId: existingStep[0].planId,
      description: `Deleted step: ${existingStep[0].name}`
    }
  );
  
  return res.status(200).json({
    message: 'Communication plan step deleted successfully',
    id
  });
}));

// ===== Contact Plan Assignment Endpoints =====

// POST /api/crm/contact-plan-assignments - Create a contact plan assignment
router.post('/contact-plan-assignments', opalAuthMiddleware, requireRole(['admin', 'doctor']), asyncHandler(async (req: Request, res: Response) => {
  const user = (req.user as unknown) as User;
  const validatedData = createContactPlanAssignmentSchema.parse(req.body);
  
  // Verify plan exists
  const plan = await db
    .select()
    .from(communicationPlans)
    .where(eq(communicationPlans.id, validatedData.planId))
    .limit(1);
  
  if (!plan || plan.length === 0) {
    throw new AppError('Communication plan not found', 404);
  }
  
  // Verify contact exists
  const contact = await db
    .select()
    .from(crmContacts)
    .where(eq(crmContacts.id, validatedData.contactId))
    .limit(1);
  
  if (!contact || contact.length === 0) {
    throw new AppError('Contact not found', 404);
  }
  
  // Apply role-based access control for doctors
  if (user.role === 'doctor') {
    // Doctors can only assign plans to contacts they created or are assigned to
    const hasAccess = contact[0].createdBy === user.id || contact[0].assignedToId === user.id;
    if (!hasAccess) {
      throw new AppError('You do not have permission to assign plans to this contact', 403);
    }
  }
  
  // Create the assignment
  const [newAssignment] = await db
    .insert(contactPlanAssignments)
    .values({
      ...validatedData,
      assignedBy: user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    .returning();
  
  // Log the action
  logAuditEvent(
    user.id,
    'create',
    'contact_plan_assignments',
    {
      resourceId: newAssignment.id,
      contactId: validatedData.contactId,
      planId: validatedData.planId,
      description: `Assigned plan: ${plan[0].name} to contact: ${contact[0].firstName} ${contact[0].lastName}`
    }
  );
  
  return res.status(201).json(newAssignment);
}));

// PUT /api/crm/contact-plan-assignments/:id - Update a contact plan assignment
router.put('/contact-plan-assignments/:id', opalAuthMiddleware, requireRole(['admin', 'doctor']), asyncHandler(async (req: Request, res: Response) => {
  const user = (req.user as unknown) as User;
  const { id } = req.params;
  const validatedData = updateContactPlanAssignmentSchema.parse(req.body);
  
  // Verify assignment exists
  const existingAssignment = await db
    .select()
    .from(contactPlanAssignments)
    .where(eq(contactPlanAssignments.id, id))
    .limit(1);
  
  if (!existingAssignment || existingAssignment.length === 0) {
    throw new AppError('Contact plan assignment not found', 404);
  }
  
  // Apply role-based access control for doctors
  if (user.role === 'doctor') {
    // Doctors can only update assignments they created
    if (existingAssignment[0].assignedBy !== user.id) {
      throw new AppError('You do not have permission to update this assignment', 403);
    }
  }
  
  // Update the assignment
  const [updatedAssignment] = await db
    .update(contactPlanAssignments)
    .set({
      ...validatedData,
      updatedAt: new Date()
    })
    .where(eq(contactPlanAssignments.id, id))
    .returning();
  
  // Log the action
  logAuditEvent(
    user.id,
    'update',
    'contact_plan_assignments',
    {
      resourceId: id,
      contactId: updatedAssignment.contactId,
      planId: updatedAssignment.planId,
      description: `Updated plan assignment`
    }
  );
  
  return res.status(200).json(updatedAssignment);
}));

// DELETE /api/crm/contact-plan-assignments/:id - Delete a contact plan assignment
router.delete('/contact-plan-assignments/:id', opalAuthMiddleware, requireRole(['admin', 'doctor']), asyncHandler(async (req: Request, res: Response) => {
  const user = (req.user as unknown) as User;
  const { id } = req.params;
  
  // Verify assignment exists
  const existingAssignment = await db
    .select()
    .from(contactPlanAssignments)
    .leftJoin(crmContacts, eq(contactPlanAssignments.contactId, crmContacts.id))
    .leftJoin(communicationPlans, eq(contactPlanAssignments.planId, communicationPlans.id))
    .where(eq(contactPlanAssignments.id, id))
    .limit(1);
  
  if (!existingAssignment || existingAssignment.length === 0) {
    throw new AppError('Contact plan assignment not found', 404);
  }
  
  // Apply role-based access control for doctors
  if (user.role === 'doctor') {
    // Doctors can only delete assignments they created
    if (existingAssignment[0].crm_contact_plan_assignments.assignedBy !== user.id) {
      throw new AppError('You do not have permission to delete this assignment', 403);
    }
  }
  
  // Delete the assignment
  await db
    .delete(contactPlanAssignments)
    .where(eq(contactPlanAssignments.id, id));
  
  // Log the action
  const contactName = existingAssignment[0].crm_contacts ? 
    `${existingAssignment[0].crm_contacts.firstName} ${existingAssignment[0].crm_contacts.lastName}` : 
    'Unknown Contact';
  
  const planName = existingAssignment[0].crm_communication_plans ? 
    existingAssignment[0].crm_communication_plans.name : 
    'Unknown Plan';
  
  logAuditEvent(
    user.id,
    'delete',
    'contact_plan_assignments',
    {
      resourceId: id,
      contactId: existingAssignment[0].crm_contact_plan_assignments.contactId,
      planId: existingAssignment[0].crm_contact_plan_assignments.planId,
      description: `Removed plan: ${planName} from contact: ${contactName}`
    }
  );
  
  return res.status(200).json({
    message: 'Contact plan assignment deleted successfully',
    id
  });
}));

// ===== Touchpoint Endpoints =====

// Create touchpoint (admin and doctor roles only)
router.post('/touchpoints', opalAuthMiddleware, requireRole(['admin', 'doctor']), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  const validatedData = createTouchpointSchema.parse(req.body);
  
  // Verify contact exists and user has access
  const contact = await db
    .select()
    .from(crmContacts)
    .where(eq(crmContacts.id, validatedData.contactId))
    .execute();
  
  if (!contact || contact.length === 0) {
    throw new AppError('Contact not found', 404);
  }
  
  // Apply role-based access control
  if (userRole === 'doctor' && contact[0].createdBy !== userId) {
    throw new AppError('You do not have permission to add touchpoints for this contact', 403);
  }
  
  const [newTouchpoint] = await db
    .insert(crmTouchpoints)
    .values({
      ...validatedData,
      createdBy: userId,
    })
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_TOUCHPOINT_CREATED',
    'crm_touchpoints',
    {
      touchpointId: newTouchpoint.id,
      touchpointType: newTouchpoint.type,
      contactId: validatedData.contactId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.status(201).json({
    message: 'Touchpoint created successfully',
    touchpoint: newTouchpoint,
  });
}));

// Get all touchpoints with filtering and pagination
router.get('/touchpoints', opalAuthMiddleware, requireRole(['admin', 'doctor']), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Extract query parameters
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;
  const contactId = req.query.contactId as string;
  const type = req.query.type as typeof touchpointTypeEnum.enumValues[number];
  const startDate = req.query.startDate as string;
  const endDate = req.query.endDate as string;
  const followUpRequired = req.query.followUpRequired === 'true';
  const sortBy = req.query.sortBy as string || 'date';
  const sortOrder = req.query.sortOrder as string || 'desc';
  
  // Build query with contact info
  let query = db.select({
    id: crmTouchpoints.id,
    contactId: crmTouchpoints.contactId,
    organizationId: crmTouchpoints.organizationId,
    type: crmTouchpoints.type,
    title: crmTouchpoints.title,
    description: crmTouchpoints.description,
    date: crmTouchpoints.date,
    outcome: crmTouchpoints.outcome,
    followUpRequired: crmTouchpoints.followUpRequired,
    followUpDate: crmTouchpoints.followUpDate,
    createdBy: crmTouchpoints.createdBy,
    createdAt: crmTouchpoints.createdAt,
    updatedAt: crmTouchpoints.updatedAt,
    contactName: db.sql`concat(${crmContacts.firstName}, ' ', ${crmContacts.lastName})`,
    contactEmail: crmContacts.email,
  })
  .from(crmTouchpoints)
  .leftJoin(crmContacts, eq(crmTouchpoints.contactId, crmContacts.id));
  
  // Apply filters
  if (contactId) {
    query = (query as any).where(eq(crmTouchpoints.contactId, contactId));
  }
  
  if (type) {
    query = (query as any).where(eq(crmTouchpoints.type, type));
  }
  
  if (startDate) {
    query = (query as any).where(sql`${crmTouchpoints.date} >= ${startDate}`);
  }
  
  if (endDate) {
    query = (query as any).where(sql`${crmTouchpoints.date} <= ${endDate}`);
  }
  
  if (req.query.followUpRequired !== undefined) {
    query = (query as any).where(eq(crmTouchpoints.followUpRequired, followUpRequired));
  }
  
  // Apply role-based access control
  if (userRole === 'doctor') {
    // Doctors can only see touchpoints they created or for contacts they own
    query = (query as any).where(
      or(
        eq(crmTouchpoints.createdBy, userId),
        eq(crmContacts.createdBy, userId)
      )
    );
  }
  
  // Get total count for pagination
  const totalCountResult = await db
    .select({ count: sql`count(*)` })
    .from(crmTouchpoints)
    .execute();
  
  const total = Number(totalCountResult[0].count);
  
  // Apply sorting
  if (sortBy === 'date') {
    query = sortOrder === 'desc' 
      ? (query as any).orderBy(desc(crmTouchpoints.date))
      : (query as any).orderBy(asc(crmTouchpoints.date));
  } else if (sortBy === 'createdAt') {
    query = sortOrder === 'desc'
      ? (query as any).orderBy(desc(crmTouchpoints.createdAt))
      : (query as any).orderBy(asc(crmTouchpoints.createdAt));
  }
  
  // Apply pagination
  query = (query as any).limit(limit).offset(offset);
  
  // Execute query
  const touchpoints = await query.execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_TOUCHPOINTS_ACCESSED',
    'crm_touchpoints',
    {
      filters: { contactId, type, startDate, endDate, followUpRequired, sortBy, sortOrder },
      pagination: { page, limit },
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    touchpoints,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get touchpoint by ID
router.get('/touchpoints/:id', opalAuthMiddleware, requireRole(['admin', 'doctor']), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const touchpointId = req.params.id;
  
  // Build query with contact info
  let query = db.select({
    id: crmTouchpoints.id,
    contactId: crmTouchpoints.contactId,
    organizationId: crmTouchpoints.organizationId,
    type: crmTouchpoints.type,
    title: crmTouchpoints.title,
    description: crmTouchpoints.description,
    date: crmTouchpoints.date,
    outcome: crmTouchpoints.outcome,
    followUpRequired: crmTouchpoints.followUpRequired,
    followUpDate: crmTouchpoints.followUpDate,
    createdBy: crmTouchpoints.createdBy,
    createdAt: crmTouchpoints.createdAt,
    updatedAt: crmTouchpoints.updatedAt,
    contact: {
      id: crmContacts.id,
      firstName: crmContacts.firstName,
      lastName: crmContacts.lastName,
      email: crmContacts.email,
      phone: crmContacts.phone,
      organizationId: crmContacts.organizationId,
    },
  })
  .from(crmTouchpoints)
  .leftJoin(crmContacts, eq(crmTouchpoints.contactId, crmContacts.id))
  .where(eq(crmTouchpoints.id, touchpointId));
  
  // Apply role-based access control
  if (userRole === 'doctor') {
    // Doctors can only see touchpoints they created or for contacts they own
    query = (query as any).where(
      or(
        eq(crmTouchpoints.createdBy, userId),
        eq(crmContacts.createdBy, userId)
      )
    );
  }
  
  // Execute query
  const result = await query.execute();
  
  if (!result || result.length === 0) {
    throw new AppError('Touchpoint not found', 404);
  }
  
  // Format the response
  const touchpoint = result[0];
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_TOUCHPOINT_ACCESSED',
    'crm_touchpoints',
    {
      touchpointId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    touchpoint,
  });
}));

// Update touchpoint by ID
router.put('/touchpoints/:id', opalAuthMiddleware, requireRole(['admin', 'doctor']), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const touchpointId = req.params.id;
  
  const validatedData = updateTouchpointSchema.parse(req.body);
  
  // Check if touchpoint exists and user has access
  const existingTouchpoint = await db
    .select({
      id: crmTouchpoints.id,
      contactId: crmTouchpoints.contactId,
      organizationId: crmTouchpoints.organizationId,
      type: crmTouchpoints.type,
      title: crmTouchpoints.title,
      description: crmTouchpoints.description,
      date: crmTouchpoints.date,
      outcome: crmTouchpoints.outcome,
      followUpRequired: crmTouchpoints.followUpRequired,
      followUpDate: crmTouchpoints.followUpDate,
      createdBy: crmTouchpoints.createdBy,
      createdAt: crmTouchpoints.createdAt,
      updatedAt: crmTouchpoints.updatedAt,
      contactCreatedBy: crmContacts.createdBy,
    })
    .from(crmTouchpoints)
    .leftJoin(crmContacts, eq(crmTouchpoints.contactId, crmContacts.id))
    .where(eq(crmTouchpoints.id, touchpointId))
    .execute();
  
  if (!existingTouchpoint || existingTouchpoint.length === 0) {
    throw new AppError('Touchpoint not found', 404);
  }
  
  // Apply role-based access control
  if (userRole === 'doctor' && 
      existingTouchpoint[0].createdBy !== userId && 
      existingTouchpoint[0].contactCreatedBy !== userId) {
    throw new AppError('You do not have permission to update this touchpoint', 403);
  }
  
  // If contactId is provided, verify it exists and user has access
  if (validatedData.contactId) {
    const contact = await db
      .select()
      .from(crmContacts)
      .where(eq(crmContacts.id, validatedData.contactId))
      .execute();
    
    if (!contact || contact.length === 0) {
      throw new AppError('Contact not found', 404);
    }
    
    // If doctor, verify they have access to this contact
    if (userRole === 'doctor' && contact[0].createdBy !== userId) {
      throw new AppError('You do not have permission to associate with this contact', 403);
    }
  }
  
  // Update touchpoint
  const [updatedTouchpoint] = await db
    .update(crmTouchpoints)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(eq(crmTouchpoints.id, touchpointId))
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_TOUCHPOINT_UPDATED',
    'crm_touchpoints',
    {
      touchpointId,
      changes: validatedData,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Touchpoint updated successfully',
    touchpoint: updatedTouchpoint,
  });
}));

// Delete touchpoint by ID
router.delete('/touchpoints/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const touchpointId = req.params.id;
  
  // Check if touchpoint exists and user has access
  const existingTouchpoint = await db
    .select({
      id: crmTouchpoints.id,
      contactId: crmTouchpoints.contactId,
      organizationId: crmTouchpoints.organizationId,
      type: crmTouchpoints.type,
      title: crmTouchpoints.title,
      description: crmTouchpoints.description,
      date: crmTouchpoints.date,
      outcome: crmTouchpoints.outcome,
      followUpRequired: crmTouchpoints.followUpRequired,
      followUpDate: crmTouchpoints.followUpDate,
      createdBy: crmTouchpoints.createdBy,
      createdAt: crmTouchpoints.createdAt,
      updatedAt: crmTouchpoints.updatedAt,
      contactCreatedBy: crmContacts.createdBy,
    })
    .from(crmTouchpoints)
    .leftJoin(crmContacts, eq(crmTouchpoints.contactId, crmContacts.id))
    .where(eq(crmTouchpoints.id, touchpointId))
    .execute();
  
  if (!existingTouchpoint || existingTouchpoint.length === 0) {
    throw new AppError('Touchpoint not found', 404);
  }
  
  // Apply role-based access control
  if (userRole === 'doctor' && 
      existingTouchpoint[0].createdBy !== userId && 
      existingTouchpoint[0].contactCreatedBy !== userId) {
    throw new AppError('You do not have permission to delete this touchpoint', 403);
  }
  
  // Delete touchpoint
  await db
    .delete(crmTouchpoints)
    .where(eq(crmTouchpoints.id, touchpointId));
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_TOUCHPOINT_DELETED',
    'crm_touchpoints',
    {
      touchpointId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Touchpoint deleted successfully',
  });
}));

// ===== Email Campaign Management Routes =====

// Import campaign schema and types
import {
  campaigns,
  emailTemplates,
  campaignRules,
  campaignExecutions,
  campaignAnalytics,
  campaignTriggers,
  unsubscribeList,
  triggerTypeEnum,
  campaignStatusEnum,
  templateTypeEnum,
  campaignTypeEnum,
  audienceTypeEnum
} from '../db/schema/campaigns.js';

// Campaign validation schemas
const createCampaignSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  type: z.enum(campaignTypeEnum.enumValues).default('Email'),
  trigger_type: z.enum(triggerTypeEnum.enumValues),
  trigger_config: z.record(z.any()).optional(),
  audience_type: z.enum(audienceTypeEnum.enumValues),
  audience_rules: z.record(z.any()).optional(),
  template_id: z.string().uuid().optional(),
  rule_id: z.string().uuid().optional(),
  start_date: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  end_date: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  tags: z.array(z.string()).default([]),
});

const updateCampaignSchema = createCampaignSchema.partial();

const createTemplateSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  type: z.enum(templateTypeEnum.enumValues),
  subject: z.string().min(1).max(500),
  html_content: z.string().min(1),
  text_content: z.string().optional(),
  variables: z.array(z.string()).default([]),
  is_active: z.boolean().default(true),
});

const updateTemplateSchema = createTemplateSchema.partial();

// Template routes (must come before parameterized campaign routes)
// Create template
router.post('/campaigns/templates', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const validatedData = createTemplateSchema.parse(req.body);
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const newTemplate = await db
    .insert(emailTemplates)
    .values({
      ...validatedData,
      created_by: userId,
    })
    .returning();

  await logAuditEvent(userId, 'template_created', 'crm_email_templates', {
    resourceId: newTemplate[0].template_id,
    name: validatedData.name,
  });

  res.status(201).json({
    success: true,
    data: newTemplate[0],
  });
}));

// Get all templates
router.get('/campaigns/templates', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const result = await db
    .select()
    .from(emailTemplates)
    .orderBy(desc(emailTemplates.created_at));

  res.json({
    success: true,
    data: result,
  });
}));

// Get template by ID
router.get('/campaigns/templates/:templateId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { templateId } = req.params;
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const result = await db
    .select()
    .from(emailTemplates)
    .where(eq(emailTemplates.template_id, templateId))
    .limit(1);

  if (result.length === 0) {
    throw new AppError('Template not found', 404);
  }

  res.json({
    success: true,
    data: result[0],
  });
}));

// Update template
router.put('/campaigns/templates/:templateId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { templateId } = req.params;
  const validatedData = updateTemplateSchema.parse(req.body);
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const existingTemplate = await db
    .select()
    .from(emailTemplates)
    .where(eq(emailTemplates.template_id, templateId))
    .limit(1);

  if (existingTemplate.length === 0) {
    throw new AppError('Template not found', 404);
  }

  const updatedTemplate = await db
    .update(emailTemplates)
    .set({
      ...validatedData,
      updated_at: new Date(),
    })
    .where(eq(emailTemplates.template_id, templateId))
    .returning();

  await logAuditEvent(userId, 'template_updated', 'crm_email_templates', {
    resourceId: templateId,
  });

  res.json({
    success: true,
    data: updatedTemplate[0],
  });
}));

// Campaign routes (parameterized routes come after specific routes)
// Create campaign
router.post('/campaigns', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const validatedData = createCampaignSchema.parse(req.body);
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const newCampaign = await db
    .insert(campaigns)
    .values({
      ...validatedData,
      created_by: userId,
    })
    .returning();

  await logAuditEvent(userId, 'campaign_created', 'crm_campaigns', {
    resourceId: newCampaign[0].campaign_id,
    name: validatedData.name,
    type: validatedData.type,
  });

  res.status(201).json({
    success: true,
    data: newCampaign[0],
  });
}));

// Get all campaigns
router.get('/campaigns', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const result = await db
    .select()
    .from(campaigns)
    .orderBy(desc(campaigns.created_at));

  res.json({
    success: true,
    data: result,
  });
}));

// Get campaign analytics (specific route before parameterized route)
router.get('/campaigns/:campaignId/analytics', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { campaignId } = req.params;
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const analytics = await db
    .select()
    .from(campaignAnalytics)
    .where(eq(campaignAnalytics.campaign_id, campaignId))
    .limit(1);

  if (analytics.length === 0) {
    res.json({
      success: true,
      data: {
        campaign_id: campaignId,
        total_sent: 0,
        total_delivered: 0,
        total_bounced: 0,
        total_opened: 0,
        total_clicked: 0,
        total_unsubscribed: 0,
        delivery_rate: 0,
        open_rate: 0,
        click_rate: 0,
        bounce_rate: 0,
        unsubscribe_rate: 0,
      },
    });
    return;
  }

  res.json({
    success: true,
    data: analytics[0],
  });
}));

// Get campaign by ID (parameterized route comes last)
router.get('/campaigns/:campaignId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { campaignId } = req.params;
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const result = await db
    .select()
    .from(campaigns)
    .where(eq(campaigns.campaign_id, campaignId))
    .limit(1);

  if (result.length === 0) {
    throw new AppError('Campaign not found', 404);
  }

  res.json({
    success: true,
    data: result[0],
  });
}));

// Update campaign
router.put('/campaigns/:campaignId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { campaignId } = req.params;
  const validatedData = updateCampaignSchema.parse(req.body);
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const existingCampaign = await db
    .select()
    .from(campaigns)
    .where(eq(campaigns.campaign_id, campaignId))
    .limit(1);

  if (existingCampaign.length === 0) {
    throw new AppError('Campaign not found', 404);
  }

  const updatedCampaign = await db
    .update(campaigns)
    .set({
      ...validatedData,
      updated_at: new Date(),
    })
    .where(eq(campaigns.campaign_id, campaignId))
    .returning();

  await logAuditEvent(userId, 'campaign_updated', 'crm_campaigns', {
    resourceId: campaignId,
  });

  res.json({
    success: true,
    data: updatedCampaign[0],
  });
}));
export default router;
