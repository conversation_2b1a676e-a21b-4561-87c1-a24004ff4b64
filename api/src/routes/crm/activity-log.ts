import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../../db/index.js';
import { eq, desc, like, or, asc, sql, and } from 'drizzle-orm';
import { AppError, asyncHandler } from '../../middleware/errorHandler.js';
import { opalAuthMiddleware } from '../../middleware/opalauth.js';
import { logAuditEvent } from '../../utils/logger.js';
import { activity_log, activityTypeEnum, leads } from '../../db/schema/crm.js';
import { users } from '../../db/schema/users.js';

const router = Router();

// Validation schemas
const createActivityLogSchema = z.object({
  lead_id: z.string().uuid(),
  activity_type: z.enum(activityTypeEnum.enumValues),
  title: z.string().min(1, 'Activity title is required'),
  description: z.string().optional(),
  performed_at: z.string().transform(val => new Date(val)),
  duration_minutes: z.number().int().optional(),
  notes: z.string().optional(),
});

const updateActivityLogSchema = createActivityLogSchema.partial();

// Create activity log entry
router.post('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Check if user has permission to create activity logs
  if (!['Admin', 'Sales', 'Ops', 'Agent', 'Exec'].includes(userRole)) {
    throw new AppError('You do not have permission to create activity logs', 403);
  }
  
  const validatedData = createActivityLogSchema.parse(req.body);
  
  // Verify lead exists
  const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.id, validatedData.lead_id))
    .execute();
  
  if (!lead || lead.length === 0) {
    throw new AppError('Lead not found', 404);
  }
  
  // Apply role-based access control for the lead
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      lead[0].assigned_to_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to add activities to this lead', 403);
    }
  }
  
  const [newActivity] = await db
    .insert(activity_log)
    .values({
      ...validatedData,
      created_at: new Date(),
      updated_at: new Date(),
      created_by: userId,
    })
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ACTIVITY_CREATED',
    'activity_log',
    {
      activityId: newActivity.id,
      leadId: newActivity.lead_id,
      activityType: newActivity.activity_type,
      activityTitle: newActivity.title,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.status(201).json({
    message: 'Activity log entry created successfully',
    activity: newActivity,
  });
}));

// Get all activity logs with filtering and pagination
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Extract query parameters
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search as string;
  const leadId = req.query.lead_id as string;
  const activityType = req.query.activity_type as typeof activityTypeEnum.enumValues[number];
  const startDate = req.query.start_date ? new Date(req.query.start_date as string) : undefined;
  const endDate = req.query.end_date ? new Date(req.query.end_date as string) : undefined;
  const sortBy = req.query.sortBy as string || 'performed_at';
  const sortOrder = req.query.sortOrder as string || 'desc';
  
  // Build query
  let query = db.select({
    id: activity_log.id,
    lead_id: activity_log.lead_id,
    activity_type: activity_log.activity_type,
    title: activity_log.title,
    description: activity_log.description,
    performed_at: activity_log.performed_at,
    duration_minutes: activity_log.duration_minutes,
    notes: activity_log.notes,
    created_at: activity_log.created_at,
    updated_at: activity_log.updated_at,
    created_by: activity_log.created_by,
    // Join with users to get creator name
    creator_first_name: users.firstName,
    creator_last_name: users.lastName,
    // Join with leads to get lead title
    lead_title: leads.title,
    lead_created_by: leads.created_by,
    lead_assigned_user_id: leads.assigned_user_id,
  })
  .from(activity_log)
  .leftJoin(users, eq(activity_log.created_by, users.id))
  .leftJoin(leads, eq(activity_log.lead_id, leads.id));
  
  // Apply filters
  const conditions = [];
  
  if (search) {
    conditions.push(
      or(
        like(activity_log.title, `%${search}%`),
        like(activity_log.description || '', `%${search}%`),
        like(activity_log.notes || '', `%${search}%`)
      )
    );
  }
  
  if (leadId) {
    conditions.push(eq(activity_log.lead_id, leadId));
  }
  
  if (activityType) {
    conditions.push(eq(activity_log.activity_type, activityType));
  }
  
  if (startDate) {
    conditions.push(sql`${activity_log.performed_at} >= ${startDate}`);
  }
  
  if (endDate) {
    conditions.push(sql`${activity_log.performed_at} <= ${endDate}`);
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    // Non-admin users can only see activities they created or for leads they own/are assigned to
    conditions.push(
      or(
        eq(activity_log.created_by, userId),
        eq(leads.created_by, userId),
        eq(leads.assigned_user_id, userId)
        // TODO: Add team membership check when team-user relationship is implemented
      )
    );
  }
  
  if (conditions.length > 0) {
    query = query.where(and(...conditions));
  }
  
  // Get total count for pagination
  const countQuery = db
    .select({ count: sql`count(*)` })
    .from(activity_log)
    .leftJoin(leads, eq(activity_log.lead_id, leads.id));
  
  if (conditions.length > 0) {
    countQuery.where(and(...conditions));
  }
  
  const totalCountResult = await countQuery.execute();
  const total = Number(totalCountResult[0]?.count || 0);
  
  // Apply sorting
  if (sortBy === 'title') {
    query = sortOrder === 'desc' 
      ? query.orderBy(desc(activity_log.title))
      : query.orderBy(asc(activity_log.title));
  } else if (sortBy === 'activity_type') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(activity_log.activity_type))
      : query.orderBy(asc(activity_log.activity_type));
  } else if (sortBy === 'performed_at') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(activity_log.performed_at))
      : query.orderBy(asc(activity_log.performed_at));
  } else if (sortBy === 'created_at') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(activity_log.created_at))
      : query.orderBy(asc(activity_log.created_at));
  }
  
  // Apply pagination
  query = query.limit(limit).offset(offset);
  
  // Execute query
  const activitiesList = await query.execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ACTIVITIES_ACCESSED',
    'activity_log',
    {
      filters: { search, leadId, activityType, startDate, endDate },
      pagination: { page, limit },
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    activities: activitiesList,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get activity logs by lead ID
router.get('/lead/:leadId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const leadId = req.params.leadId;
  
  // Verify lead exists
  const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.id, leadId))
    .execute();
  
  if (!lead || lead.length === 0) {
    throw new AppError('Lead not found', 404);
  }
  
  // Apply role-based access control for the lead
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      lead[0].assigned_to_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to access activities for this lead', 403);
    }
  }
  
  // Extract query parameters
  const activityType = req.query.activity_type as typeof activityTypeEnum.enumValues[number];
  const startDate = req.query.start_date ? new Date(req.query.start_date as string) : undefined;
  const endDate = req.query.end_date ? new Date(req.query.end_date as string) : undefined;
  const sortBy = req.query.sortBy as string || 'performed_at';
  const sortOrder = req.query.sortOrder as string || 'desc';
  
  // Build query
  let query = db.select({
    id: activity_log.id,
    lead_id: activity_log.lead_id,
    activity_type: activity_log.activity_type,
    title: activity_log.title,
    description: activity_log.description,
    performed_at: activity_log.performed_at,
    duration_minutes: activity_log.duration_minutes,
    notes: activity_log.notes,
    created_at: activity_log.created_at,
    updated_at: activity_log.updated_at,
    created_by: activity_log.created_by,
    // Join with users to get creator name
    creator_first_name: users.firstName,
    creator_last_name: users.lastName,
  })
  .from(activity_log)
  .leftJoin(users, eq(activity_log.created_by, users.id))
  .where(eq(activity_log.lead_id, leadId));
  
  // Apply filters
  const conditions = [eq(activity_log.lead_id, leadId)];
  
  if (activityType) {
    conditions.push(eq(activity_log.activity_type, activityType));
  }
  
  if (startDate) {
    conditions.push(sql`${activity_log.performed_at} >= ${startDate}`);
  }
  
  if (endDate) {
    conditions.push(sql`${activity_log.performed_at} <= ${endDate}`);
  }
  
  query = query.where(and(...conditions));
  
  // Apply sorting
  if (sortBy === 'title') {
    query = sortOrder === 'desc' 
      ? query.orderBy(desc(activity_log.title))
      : query.orderBy(asc(activity_log.title));
  } else if (sortBy === 'activity_type') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(activity_log.activity_type))
      : query.orderBy(asc(activity_log.activity_type));
  } else if (sortBy === 'performed_at') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(activity_log.performed_at))
      : query.orderBy(asc(activity_log.performed_at));
  } else if (sortBy === 'created_at') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(activity_log.created_at))
      : query.orderBy(asc(activity_log.created_at));
  }
  
  // Execute query
  const activitiesList = await query.execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ACTIVITIES_BY_LEAD_ACCESSED',
    'activity_log',
    {
      leadId,
      filters: { activityType, startDate, endDate },
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    activities: activitiesList,
  });
}));

// Get activity log by ID
router.get('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const activityId = req.params.id;
  
  // Get activity with related data
  const activity = await db
    .select({
      id: activity_log.id,
      lead_id: activity_log.lead_id,
      activity_type: activity_log.activity_type,
      title: activity_log.title,
      description: activity_log.description,
      performed_at: activity_log.performed_at,
      duration_minutes: activity_log.duration_minutes,
      notes: activity_log.notes,
      created_at: activity_log.created_at,
      updated_at: activity_log.updated_at,
      created_by: activity_log.created_by,
      // Join with users to get creator name
      creator_first_name: users.firstName,
      creator_last_name: users.lastName,
      // Join with leads to get lead data
      lead_title: leads.title,
      lead_created_by: leads.created_by,
      lead_assigned_user_id: leads.assigned_user_id,
    })
    .from(activity_log)
    .leftJoin(users, eq(activity_log.created_by, users.id))
    .leftJoin(leads, eq(activity_log.lead_id, leads.id))
    .where(eq(activity_log.id, activityId))
    .execute();
  
  if (!activity || activity.length === 0) {
    throw new AppError('Activity log entry not found', 404);
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      activity[0].created_by === userId || 
      activity[0].lead_created_by === userId ||
      activity[0].lead_assigned_user_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to access this activity log entry', 403);
    }
  }
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ACTIVITY_ACCESSED',
    'activity_log',
    {
      activityId,
      leadId: activity[0].lead_id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    activity: activity[0],
  });
}));

// Update activity log by ID
router.put('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const activityId = req.params.id;
  
  // Check if activity exists
  const existingActivity = await db
    .select({
      id: activity_log.id,
      lead_id: activity_log.lead_id,
      created_by: activity_log.created_by,
      // Join with leads to get lead data
      lead_created_by: leads.created_by,
      lead_assigned_user_id: leads.assigned_user_id,
    })
    .from(activity_log)
    .leftJoin(leads, eq(activity_log.lead_id, leads.id))
    .where(eq(activity_log.id, activityId))
    .execute();
  
  if (!existingActivity || existingActivity.length === 0) {
    throw new AppError('Activity log entry not found', 404);
  }
  
  // Apply role-based access control - only creator, lead owner/assignee, or admin/exec can update
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      existingActivity[0].created_by === userId || 
      existingActivity[0].lead_created_by === userId ||
      existingActivity[0].lead_assigned_user_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to update this activity log entry', 403);
    }
  }
  
  const validatedData = updateActivityLogSchema.parse(req.body);
  
  // If lead_id is provided, verify it exists
  if (validatedData.lead_id) {
    const lead = await db
      .select()
      .from(leads)
      .where(eq(leads.id, validatedData.lead_id))
      .execute();
    
    if (!lead || lead.length === 0) {
      throw new AppError('Lead not found', 404);
    }
    
    // Apply role-based access control for the new lead
    if (!['Admin', 'Exec'].includes(userRole)) {
      const userHasAccess = 
        lead[0].assigned_to_id === userId;
        // TODO: Add team membership check when team-user relationship is implemented
      
      if (!userHasAccess) {
        throw new AppError('You do not have permission to move this activity to the specified lead', 403);
      }
    }
  }
  
  // Update activity log
  const [updatedActivity] = await db
    .update(activity_log)
    .set({
      ...validatedData,
      updated_at: new Date(),
    })
    .where(eq(activity_log.id, activityId))
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ACTIVITY_UPDATED',
    'activity_log',
    {
      activityId,
      leadId: updatedActivity.lead_id,
      changes: validatedData,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Activity log entry updated successfully',
    activity: updatedActivity,
  });
}));

// Delete activity log by ID
router.delete('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const activityId = req.params.id;
  
  // Check if activity exists
  const existingActivity = await db
    .select({
      id: activity_log.id,
      lead_id: activity_log.lead_id,
      title: activity_log.title,
      activity_type: activity_log.activity_type,
      created_by: activity_log.created_by,
      // Join with leads to get lead data
      lead_created_by: leads.created_by,
      lead_assigned_user_id: leads.assigned_user_id,
    })
    .from(activity_log)
    .leftJoin(leads, eq(activity_log.lead_id, leads.id))
    .where(eq(activity_log.id, activityId))
    .execute();
  
  if (!existingActivity || existingActivity.length === 0) {
    throw new AppError('Activity log entry not found', 404);
  }
  
  // Apply role-based access control - only creator, admin, or exec can delete
  if (!['Admin', 'Exec'].includes(userRole) && existingActivity[0].created_by !== userId) {
    throw new AppError('You do not have permission to delete this activity log entry', 403);
  }
  
  // Delete activity log
  await db
    .delete(activity_log)
    .where(eq(activity_log.id, activityId));
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_ACTIVITY_DELETED',
    'activity_log',
    {
      activityId,
      leadId: existingActivity[0].lead_id,
      activityTitle: existingActivity[0].title,
      activityType: existingActivity[0].activity_type,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Activity log entry deleted successfully',
  });
}));

export default router;
