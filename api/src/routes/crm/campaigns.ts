import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../../db/index.js';
import {
  campaigns,
  emailTemplates,
  campaignRules,
  campaignExecutions,
  campaignAnalytics,
  campaignTriggers,
  unsubscribeList,
  triggerTypeEnum,
  campaignStatusEnum,
  templateTypeEnum,
  campaignTypeEnum,
  audienceTypeEnum
} from '../../db/schema/campaigns.js';
import { AppError, asyncHandler } from '../../middleware/errorHandler.js';
import { opalAuthMiddleware } from '../../middleware/opalAuth.js';
import { logAuditEvent } from '../../utils/logger.js';
import { eq, and, desc, asc, like, or } from 'drizzle-orm';
import { campaignEngine } from '../../services/campaignEngine.js';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Campaign management routes - Email Campaign System

// Validation schemas
const createCampaignSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  type: z.enum(campaignTypeEnum.enumValues).default('Email'),
  trigger_type: z.enum(triggerTypeEnum.enumValues),
  trigger_config: z.record(z.any()).optional(),
  audience_type: z.enum(audienceTypeEnum.enumValues),
  audience_rules: z.record(z.any()).optional(),
  template_id: z.string().uuid().optional(),
  rule_id: z.string().uuid().optional(),
  start_date: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  end_date: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  tags: z.array(z.string()).default([]),
});

const updateCampaignSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  type: z.enum(campaignTypeEnum.enumValues).optional(),
  trigger_type: z.enum(triggerTypeEnum.enumValues).optional(),
  trigger_config: z.record(z.any()).optional(),
  audience_type: z.enum(audienceTypeEnum.enumValues).optional(),
  audience_rules: z.record(z.any()).optional(),
  template_id: z.string().uuid().optional(),
  rule_id: z.string().uuid().optional(),
  status: z.enum(campaignStatusEnum.enumValues).optional(),
  start_date: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  end_date: z.string().datetime().optional().transform(val => val ? new Date(val) : undefined),
  tags: z.array(z.string()).optional(),
});

const createTemplateSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  type: z.enum(templateTypeEnum.enumValues),
  subject: z.string().min(1).max(500),
  html_content: z.string().min(1),
  text_content: z.string().optional(),
  variables: z.array(z.string()).default([]),
  is_active: z.boolean().default(true),
});

const updateTemplateSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  type: z.enum(templateTypeEnum.enumValues).optional(),
  subject: z.string().min(1).max(500).optional(),
  html_content: z.string().min(1).optional(),
  text_content: z.string().optional(),
  variables: z.array(z.string()).optional(),
  is_active: z.boolean().optional(),
});

const createRuleSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  rule_definition: z.object({
    conditions: z.array(z.object({
      field: z.string(),
      operator: z.enum(['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'in', 'not_in', 'exists', 'not_exists']),
      value: z.any(),
      logical_operator: z.enum(['AND', 'OR']).optional(),
    })),
    actions: z.array(z.object({
      type: z.enum(['send_email', 'add_tag', 'update_field', 'create_task', 'wait', 'branch']),
      parameters: z.record(z.any()),
    })),
  }),
  is_active: z.boolean().default(true),
});

const querySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('10'),
  sort_by: z.enum(['name', 'created_at', 'status']).default('created_at'),
  sort_order: z.enum(['asc', 'desc']).default('desc'),
  status: z.enum(campaignStatusEnum.enumValues).optional(),
  type: z.enum(campaignTypeEnum.enumValues).optional(),
  search: z.string().optional(),
});

// Create campaign
router.post('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const validatedData = createCampaignSchema.parse(req.body);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const newCampaign = await db
    .insert(campaigns)
    .values({
      ...validatedData,
      created_by: userId,
    })
    .returning();

  await logAuditEvent(userId, 'campaign_created', 'crm_campaigns', {
    resourceId: newCampaign[0].campaign_id,
    name: validatedData.name,
    type: validatedData.type,
  });

  res.status(201).json({
    success: true,
    data: newCampaign[0],
  });
}));

// Get all campaigns
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const query = querySchema.parse(req.query);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const offset = (query.page - 1) * query.limit;
  const conditions = [];

  // Add filters
  if (query.status) {
    conditions.push(eq(campaigns.status, query.status));
  }

  if (query.type) {
    conditions.push(eq(campaigns.type, query.type));
  }

  if (query.search) {
    conditions.push(
      or(
        like(campaigns.name, `%${query.search}%`),
        like(campaigns.description, `%${query.search}%`)
      )
    );
  }

  // Build query
  const baseQuery = db.select().from(campaigns);
  const result = conditions.length > 0 
    ? await baseQuery
        .where(and(...conditions))
        .orderBy(query.sort_order === 'desc' ? desc(campaigns.created_at) : asc(campaigns.created_at))
        .limit(query.limit)
        .offset(offset)
    : await baseQuery
        .orderBy(query.sort_order === 'desc' ? desc(campaigns.created_at) : asc(campaigns.created_at))
        .limit(query.limit)
        .offset(offset);

  // Get total count
  const totalResult = conditions.length > 0 
    ? await baseQuery.where(and(...conditions))
    : await baseQuery;

  res.json({
    success: true,
    data: result,
    pagination: {
      page: query.page,
      limit: query.limit,
      total: totalResult.length,
      pages: Math.ceil(totalResult.length / query.limit),
    },
  });
}));

// Get campaign by ID
router.get('/:campaignId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { campaignId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const result = await db
    .select()
    .from(campaigns)
    .where(eq(campaigns.campaign_id, campaignId))
    .limit(1);

  if (result.length === 0) {
    throw new AppError('Campaign not found', 404);
  }

  res.json({
    success: true,
    data: result[0],
  });
}));

// Update campaign
router.put('/:campaignId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { campaignId } = req.params;
  const validatedData = updateCampaignSchema.parse(req.body);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  // Check if campaign exists
  const existingCampaign = await db
    .select()
    .from(campaigns)
    .where(eq(campaigns.campaign_id, campaignId))
    .limit(1);

  if (existingCampaign.length === 0) {
    throw new AppError('Campaign not found', 404);
  }

  const updatedCampaign = await db
    .update(campaigns)
    .set({
      ...validatedData,
      updated_at: new Date(),
    })
    .where(eq(campaigns.campaign_id, campaignId))
    .returning();

  await logAuditEvent(userId, 'campaign_updated', 'crm_campaigns', {
    resourceId: campaignId,
  });

  res.json({
    success: true,
    data: updatedCampaign[0],
  });
}));

// Delete campaign
router.delete('/:campaignId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { campaignId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  // Check if campaign exists
  const existingCampaign = await db
    .select()
    .from(campaigns)
    .where(eq(campaigns.campaign_id, campaignId))
    .limit(1);

  if (existingCampaign.length === 0) {
    throw new AppError('Campaign not found', 404);
  }

  await db
    .delete(campaigns)
    .where(eq(campaigns.campaign_id, campaignId));

  await logAuditEvent(userId, 'campaign_deleted', 'crm_campaigns', {
    resourceId: campaignId,
    name: existingCampaign[0].name,
  });

  res.json({
    success: true,
    message: 'Campaign deleted successfully',
  });
}));

// Execute campaign manually
router.post('/:campaignId/execute', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { campaignId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  // Check if campaign exists
  const existingCampaign = await db
    .select()
    .from(campaigns)
    .where(eq(campaigns.campaign_id, campaignId))
    .limit(1);

  if (existingCampaign.length === 0) {
    throw new AppError('Campaign not found', 404);
  }

  if (existingCampaign[0].status !== 'Active') {
    throw new AppError('Campaign is not active', 400);
  }

  // Execute campaign manually - simplified approach
  // In a real implementation, this would trigger the campaign execution
  logger.info('Manual execution triggered for campaign ${campaignId} by user ${userId}', { requestId: 'context-needed' }, { data: undefined });

  await logAuditEvent(userId, 'campaign_executed', 'crm_campaigns', {
    resourceId: campaignId,
    name: existingCampaign[0].name,
  });

  res.json({
    success: true,
    message: 'Campaign execution started',
  });
}));

// Get campaign analytics
router.get('/:campaignId/analytics', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { campaignId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const analytics = await db
    .select()
    .from(campaignAnalytics)
    .where(eq(campaignAnalytics.campaign_id, campaignId))
    .limit(1);

  if (analytics.length === 0) {
    // Return empty analytics if none exist yet
    res.json({
      success: true,
      data: {
        campaign_id: campaignId,
        total_sent: 0,
        total_delivered: 0,
        total_bounced: 0,
        total_opened: 0,
        total_clicked: 0,
        total_unsubscribed: 0,
        delivery_rate: 0,
        open_rate: 0,
        click_rate: 0,
        bounce_rate: 0,
        unsubscribe_rate: 0,
      },
    });
    return;
  }

  res.json({
    success: true,
    data: analytics[0],
  });
}));

// Template management routes

// Create template
router.post('/templates', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const validatedData = createTemplateSchema.parse(req.body);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const newTemplate = await db
    .insert(emailTemplates)
    .values({
      ...validatedData,
      created_by: userId,
    })
    .returning();

  await logAuditEvent(userId, 'template_created', 'crm_email_templates', {
    resourceId: newTemplate[0].template_id,
    name: validatedData.name,
  });

  res.status(201).json({
    success: true,
    data: newTemplate[0],
  });
}));

// Get all templates
router.get('/templates', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const query = querySchema.parse(req.query);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const offset = (query.page - 1) * query.limit;
  const conditions = [];

  if (query.search) {
    conditions.push(
      or(
        like(emailTemplates.name, `%${query.search}%`),
        like(emailTemplates.description, `%${query.search}%`)
      )
    );
  }

  const baseQuery = db.select().from(emailTemplates);
  const result = conditions.length > 0
    ? await baseQuery
        .where(and(...conditions))
        .orderBy(desc(emailTemplates.created_at))
        .limit(query.limit)
        .offset(offset)
    : await baseQuery
        .orderBy(desc(emailTemplates.created_at))
        .limit(query.limit)
        .offset(offset);

  const totalResult = conditions.length > 0
    ? await baseQuery.where(and(...conditions))
    : await baseQuery;

  res.json({
    success: true,
    data: result,
    pagination: {
      page: query.page,
      limit: query.limit,
      total: totalResult.length,
      pages: Math.ceil(totalResult.length / query.limit),
    },
  });
}));

// Get template by ID
router.get('/templates/:templateId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { templateId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const result = await db
    .select()
    .from(emailTemplates)
    .where(eq(emailTemplates.template_id, templateId))
    .limit(1);

  if (result.length === 0) {
    throw new AppError('Template not found', 404);
  }

  res.json({
    success: true,
    data: result[0],
  });
}));

// Update template
router.put('/templates/:templateId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { templateId } = req.params;
  const validatedData = updateTemplateSchema.parse(req.body);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const existingTemplate = await db
    .select()
    .from(emailTemplates)
    .where(eq(emailTemplates.template_id, templateId))
    .limit(1);

  if (existingTemplate.length === 0) {
    throw new AppError('Template not found', 404);
  }

  const updatedTemplate = await db
    .update(emailTemplates)
    .set({
      ...validatedData,
      updated_at: new Date(),
    })
    .where(eq(emailTemplates.template_id, templateId))
    .returning();

  await logAuditEvent(userId, 'template_updated', 'crm_email_templates', {
    resourceId: templateId,
  });

  res.json({
    success: true,
    data: updatedTemplate[0],
  });
}));

// Create campaign rule
router.post('/rules', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const validatedData = createRuleSchema.parse(req.body);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const newRule = await db
    .insert(campaignRules)
    .values({
      name: validatedData.name,
      description: validatedData.description,
      rule_definition: validatedData.rule_definition,
      is_active: validatedData.is_active ?? true,
      created_by: userId,
    })
    .returning();

  await logAuditEvent(userId, 'campaign_rule_created', 'crm_campaign_rules', {
    resourceId: newRule[0].rule_id,
    name: validatedData.name,
  });

  res.status(201).json({
    success: true,
    data: newRule[0],
  });
}));

export default router;