import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../../db/index.js';
import { eq, desc, like, or, asc, sql, and } from 'drizzle-orm';
import { AppError, asyncHandler } from '../../middleware/errorHandler.js';
import { opalAuthMiddleware } from '../../middleware/opalauth.js';
import { logAuditEvent } from '../../utils/logger.js';
import { contacts, leads, ownerTypeEnum } from '../../db/schema/crm.js';
import { users } from '../../db/schema/users.js';

const router = Router();

// Validation schemas
const createContactSchema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email format').optional().nullable(),
  phone: z.string().optional().nullable(),
  job_title: z.string().optional().nullable(),
  organization_name: z.string().optional().nullable(),
  owner_type: z.enum(ownerTypeEnum.enumValues).default('lead'),
  owner_id: z.string().uuid(),
  address_line1: z.string().optional().nullable(),
  address_line2: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  postal_code: z.string().optional().nullable(),
  country: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
});

const updateContactSchema = createContactSchema.partial();

// Create contact
router.post('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Check if user has permission to create contacts
  if (!['Admin', 'Sales', 'Ops', 'Agent', 'Exec'].includes(userRole)) {
    throw new AppError('You do not have permission to create contacts', 403);
  }
  
  const validatedData = createContactSchema.parse(req.body);
  
  // Verify owner exists based on owner_type
  if (validatedData.owner_type === 'lead') {
    const lead = await db
      .select()
      .from(leads)
      .where(eq(leads.id, validatedData.owner_id))
      .execute();
    
    if (!lead || lead.length === 0) {
      throw new AppError('Lead not found', 404);
    }
    
    // Apply role-based access control for the lead
    if (!['Admin', 'Exec'].includes(userRole)) {
      const userHasAccess = 
        lead[0].created_by === userId || 
        lead[0].assigned_user_id === userId;
        // TODO: Add team membership check when team-user relationship is implemented
      
      if (!userHasAccess) {
        throw new AppError('You do not have permission to add contacts to this lead', 403);
      }
    }
  } else {
    // For future owner types like 'organization' or 'opportunity'
    throw new AppError(`Owner type '${validatedData.owner_type}' is not supported yet`, 400);
  }
  
  const [newContact] = await db
    .insert(contacts)
    .values({
      ...validatedData,
      created_at: new Date(),
      updated_at: new Date(),
      created_by: userId,
    })
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACT_CREATED',
    'contacts',
    {
      contactId: newContact.id,
      contactName: `${newContact.first_name} ${newContact.last_name}`,
      ownerType: newContact.owner_type,
      ownerId: newContact.owner_id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.status(201).json({
    message: 'Contact created successfully',
    contact: newContact,
  });
}));

// Get all contacts with filtering and pagination
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Extract query parameters
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search as string;
  const ownerType = req.query.owner_type as typeof ownerTypeEnum.enumValues[number];
  const ownerId = req.query.owner_id as string;
  const sortBy = req.query.sortBy as string || 'last_name';
  const sortOrder = req.query.sortOrder as string || 'asc';
  
  // Build query
  let query = db.select({
    id: contacts.id,
    first_name: contacts.first_name,
    last_name: contacts.last_name,
    email: contacts.email,
    phone: contacts.phone,
    job_title: contacts.job_title,
    organization_name: contacts.organization_name,
    owner_type: contacts.owner_type,
    owner_id: contacts.owner_id,
    address_line1: contacts.address_line1,
    address_line2: contacts.address_line2,
    city: contacts.city,
    state: contacts.state,
    postal_code: contacts.postal_code,
    country: contacts.country,
    notes: contacts.notes,
    created_at: contacts.created_at,
    updated_at: contacts.updated_at,
    created_by: contacts.created_by,
    // Join with leads to get lead title when owner_type is 'lead'
    lead_title: leads.title,
    lead_created_by: leads.created_by,
    lead_assigned_user_id: leads.assigned_user_id,
  })
  .from(contacts)
  .leftJoin(leads, and(
    eq(contacts.owner_type, 'lead'),
    eq(contacts.owner_id, leads.id)
  ));
  
  // Apply filters
  const conditions = [];
  
  if (search) {
    conditions.push(
      or(
        like(contacts.first_name, `%${search}%`),
        like(contacts.last_name, `%${search}%`),
        like(contacts.email || '', `%${search}%`),
        like(contacts.phone || '', `%${search}%`),
        like(contacts.organization_name || '', `%${search}%`)
      )
    );
  }
  
  if (ownerType) {
    conditions.push(eq(contacts.owner_type, ownerType));
  }
  
  if (ownerId) {
    conditions.push(eq(contacts.owner_id, ownerId));
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    // Non-admin users can only see contacts they created or for leads they own/are assigned to
    conditions.push(
      or(
        eq(contacts.created_by, userId),
        eq(leads.created_by, userId),
        eq(leads.assigned_user_id, userId)
        // TODO: Add team membership check when team-user relationship is implemented
      )
    );
  }
  
  if (conditions.length > 0) {
    query = query.where(and(...conditions));
  }
  
  // Get total count for pagination
  const countQuery = db
    .select({ count: sql`count(*)` })
    .from(contacts)
    .leftJoin(leads, and(
      eq(contacts.owner_type, 'lead'),
      eq(contacts.owner_id, leads.id)
    ));
  
  if (conditions.length > 0) {
    countQuery.where(and(...conditions));
  }
  
  const totalCountResult = await countQuery.execute();
  const total = Number(totalCountResult[0]?.count || 0);
  
  // Apply sorting
  if (sortBy === 'first_name') {
    query = sortOrder === 'desc' 
      ? query.orderBy(desc(contacts.first_name))
      : query.orderBy(asc(contacts.first_name));
  } else if (sortBy === 'last_name') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(contacts.last_name))
      : query.orderBy(asc(contacts.last_name));
  } else if (sortBy === 'email') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(contacts.email))
      : query.orderBy(asc(contacts.email));
  } else if (sortBy === 'created_at') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(contacts.created_at))
      : query.orderBy(asc(contacts.created_at));
  }
  
  // Apply pagination
  query = query.limit(limit).offset(offset);
  
  // Execute query
  const contactsList = await query.execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACTS_ACCESSED',
    'contacts',
    {
      filters: { search, ownerType, ownerId },
      pagination: { page, limit },
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    contacts: contactsList,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get contacts by owner (lead, organization, etc.)
router.get('/owner/:type/:id', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const ownerType = req.params.type as typeof ownerTypeEnum.enumValues[number];
  const ownerId = req.params.id;
  
  // Verify owner exists based on owner_type
  if (ownerType === 'lead') {
    const lead = await db
      .select()
      .from(leads)
      .where(eq(leads.id, ownerId))
      .execute();
    
    if (!lead || lead.length === 0) {
      throw new AppError('Lead not found', 404);
    }
    
    // Apply role-based access control for the lead
    if (!['Admin', 'Exec'].includes(userRole)) {
      const userHasAccess = 
        lead[0].created_by === userId || 
        lead[0].assigned_user_id === userId;
        // TODO: Add team membership check when team-user relationship is implemented
      
      if (!userHasAccess) {
        throw new AppError('You do not have permission to access contacts for this lead', 403);
      }
    }
  } else {
    // For future owner types like 'organization' or 'opportunity'
    throw new AppError(`Owner type '${ownerType}' is not supported yet`, 400);
  }
  
  // Extract query parameters
  const sortBy = req.query.sortBy as string || 'last_name';
  const sortOrder = req.query.sortOrder as string || 'asc';
  
  // Build query
  let query = db.select()
    .from(contacts)
    .where(
      and(
        eq(contacts.owner_type, ownerType),
        eq(contacts.owner_id, ownerId)
      )
    );
  
  // Apply sorting
  if (sortBy === 'first_name') {
    query = sortOrder === 'desc' 
      ? query.orderBy(desc(contacts.first_name))
      : query.orderBy(asc(contacts.first_name));
  } else if (sortBy === 'last_name') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(contacts.last_name))
      : query.orderBy(asc(contacts.last_name));
  } else if (sortBy === 'email') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(contacts.email))
      : query.orderBy(asc(contacts.email));
  } else if (sortBy === 'created_at') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(contacts.created_at))
      : query.orderBy(asc(contacts.created_at));
  }
  
  // Execute query
  const contactsList = await query.execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACTS_BY_OWNER_ACCESSED',
    'contacts',
    {
      ownerType,
      ownerId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    contacts: contactsList,
  });
}));

// Get contact by ID
router.get('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const contactId = req.params.id;
  
  // Get contact with related data
  const contact = await db
    .select({
      id: contacts.id,
      first_name: contacts.first_name,
      last_name: contacts.last_name,
      email: contacts.email,
      phone: contacts.phone,
      job_title: contacts.job_title,
      organization_name: contacts.organization_name,
      owner_type: contacts.owner_type,
      owner_id: contacts.owner_id,
      address_line1: contacts.address_line1,
      address_line2: contacts.address_line2,
      city: contacts.city,
      state: contacts.state,
      postal_code: contacts.postal_code,
      country: contacts.country,
      notes: contacts.notes,
      created_at: contacts.created_at,
      updated_at: contacts.updated_at,
      created_by: contacts.created_by,
      // Join with leads to get lead data when owner_type is 'lead'
      lead_title: leads.title,
      lead_created_by: leads.created_by,
      lead_assigned_user_id: leads.assigned_user_id,
    })
    .from(contacts)
    .leftJoin(leads, and(
      eq(contacts.owner_type, 'lead'),
      eq(contacts.owner_id, leads.id)
    ))
    .where(eq(contacts.id, contactId))
    .execute();
  
  if (!contact || contact.length === 0) {
    throw new AppError('Contact not found', 404);
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      contact[0].created_by === userId || 
      contact[0].lead_created_by === userId ||
      contact[0].lead_assigned_user_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to access this contact', 403);
    }
  }
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACT_ACCESSED',
    'contacts',
    {
      contactId,
      contactName: `${contact[0].first_name} ${contact[0].last_name}`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    contact: contact[0],
  });
}));

// Update contact by ID
router.put('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const contactId = req.params.id;
  
  // Check if contact exists
  const existingContact = await db
    .select({
      id: contacts.id,
      first_name: contacts.first_name,
      last_name: contacts.last_name,
      owner_type: contacts.owner_type,
      owner_id: contacts.owner_id,
      created_by: contacts.created_by,
      // Join with leads to get lead data when owner_type is 'lead'
      lead_created_by: leads.created_by,
      lead_assigned_user_id: leads.assigned_user_id,
    })
    .from(contacts)
    .leftJoin(leads, and(
      eq(contacts.owner_type, 'lead'),
      eq(contacts.owner_id, leads.id)
    ))
    .where(eq(contacts.id, contactId))
    .execute();
  
  if (!existingContact || existingContact.length === 0) {
    throw new AppError('Contact not found', 404);
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      existingContact[0].created_by === userId || 
      existingContact[0].lead_created_by === userId ||
      existingContact[0].lead_assigned_user_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to update this contact', 403);
    }
  }
  
  const validatedData = updateContactSchema.parse(req.body);
  
  // If owner_type and owner_id are provided, verify owner exists
  if (validatedData.owner_type && validatedData.owner_id) {
    if (validatedData.owner_type === 'lead') {
      const lead = await db
        .select()
        .from(leads)
        .where(eq(leads.id, validatedData.owner_id))
        .execute();
      
      if (!lead || lead.length === 0) {
        throw new AppError('Lead not found', 404);
      }
      
      // Apply role-based access control for the new lead
      if (!['Admin', 'Exec'].includes(userRole)) {
        const userHasAccess = 
          lead[0].created_by === userId || 
          lead[0].assigned_user_id === userId;
          // TODO: Add team membership check when team-user relationship is implemented
        
        if (!userHasAccess) {
          throw new AppError('You do not have permission to move this contact to the specified lead', 403);
        }
      }
    } else {
      // For future owner types like 'organization' or 'opportunity'
      throw new AppError(`Owner type '${validatedData.owner_type}' is not supported yet`, 400);
    }
  } else if (validatedData.owner_id && !validatedData.owner_type) {
    // If only owner_id is provided without owner_type
    throw new AppError('Owner type must be provided when updating owner ID', 400);
  } else if (validatedData.owner_type && !validatedData.owner_id) {
    // If only owner_type is provided without owner_id
    throw new AppError('Owner ID must be provided when updating owner type', 400);
  }
  
  // Update contact
  const [updatedContact] = await db
    .update(contacts)
    .set({
      ...validatedData,
      updated_at: new Date(),
    })
    .where(eq(contacts.id, contactId))
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACT_UPDATED',
    'contacts',
    {
      contactId,
      contactName: `${updatedContact.first_name} ${updatedContact.last_name}`,
      changes: validatedData,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Contact updated successfully',
    contact: updatedContact,
  });
}));

// Delete contact by ID
router.delete('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const contactId = req.params.id;
  
  // Check if contact exists
  const existingContact = await db
    .select({
      id: contacts.id,
      first_name: contacts.first_name,
      last_name: contacts.last_name,
      owner_type: contacts.owner_type,
      owner_id: contacts.owner_id,
      created_by: contacts.created_by,
      // Join with leads to get lead data when owner_type is 'lead'
      lead_created_by: leads.created_by,
      lead_assigned_user_id: leads.assigned_user_id,
    })
    .from(contacts)
    .leftJoin(leads, and(
      eq(contacts.owner_type, 'lead'),
      eq(contacts.owner_id, leads.id)
    ))
    .where(eq(contacts.id, contactId))
    .execute();
  
  if (!existingContact || existingContact.length === 0) {
    throw new AppError('Contact not found', 404);
  }
  
  // Apply role-based access control - only Admin, Exec, or the creator can delete contacts
  if (!['Admin', 'Exec'].includes(userRole) && existingContact[0].created_by !== userId) {
    throw new AppError('You do not have permission to delete this contact', 403);
  }
  
  // Delete contact
  await db
    .delete(contacts)
    .where(eq(contacts.id, contactId));
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_CONTACT_DELETED',
    'contacts',
    {
      contactId,
      contactName: `${existingContact[0].first_name} ${existingContact[0].last_name}`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Contact deleted successfully',
  });
}));

export default router;
