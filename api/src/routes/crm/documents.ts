import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../../db/index.js';
import { documents, leads, documentTypeEnum, documentStatusEnum } from '../../db/schema/crm.js';
import { users } from '../../db/schema/users.js';
import { AppError, asyncHandler } from '../../middleware/errorHandler.js';
import { opalAuthMiddleware } from '../../middleware/opalAuth.js';
import { logAuditEvent } from '../../utils/logger.js';
import { eq, and, desc, asc } from 'drizzle-orm';
import { storageService } from '../../services/storageService.js';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Validation schemas
const createDocumentSchema = z.object({
  lead_id: z.string().uuid(),
  type: z.enum(documentTypeEnum.enumValues),
  status: z.enum(documentStatusEnum.enumValues).default('Draft'),
  url: z.string().url(),
});

const updateDocumentSchema = z.object({
  type: z.enum(documentTypeEnum.enumValues).optional(),
  status: z.enum(documentStatusEnum.enumValues).optional(),
  url: z.string().url().optional(),
});

const querySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('10'),
  sort_by: z.enum(['type', 'status', 'uploaded_at']).default('uploaded_at'),
  sort_order: z.enum(['asc', 'desc']).default('desc'),
  type: z.enum(documentTypeEnum.enumValues).optional(),
  status: z.enum(documentStatusEnum.enumValues).optional(),
});

// Create document
router.post('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const validatedData = createDocumentSchema.parse(req.body);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  // Check if lead exists and user has access
  const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.lead_id, validatedData.lead_id))
    .limit(1);

  if (lead.length === 0) {
    throw new AppError('Lead not found', 404);
  }

  // Check access permissions
  const hasAccess = lead[0].assigned_to_id === userId;

  if (!hasAccess) {
    throw new AppError('Access denied', 403);
  }

  const newDocument = await db
    .insert(documents)
    .values({
      lead_id: validatedData.lead_id,
      type: validatedData.type,
      status: validatedData.status,
      url: validatedData.url,
      uploaded_by: userId,
    })
    .returning();

  await logAuditEvent(userId, 'document_created', 'crm_documents', {
    resourceId: newDocument[0].document_id,
    lead_id: validatedData.lead_id,
    type: validatedData.type,
  });

  res.status(201).json({
    success: true,
    data: newDocument[0],
  });
}));

// Get documents for a lead
router.get('/lead/:leadId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { leadId } = req.params;
  const query = querySchema.parse(req.query);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  // Check if lead exists and user has access
  const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.lead_id, leadId))
    .limit(1);

  if (lead.length === 0) {
    throw new AppError('Lead not found', 404);
  }

  // Check access permissions
  const hasAccess = lead[0].assigned_to_id === userId;

  if (!hasAccess) {
    throw new AppError('Access denied', 403);
  }

  const offset = (query.page - 1) * query.limit;
  const conditions = [eq(documents.lead_id, leadId)];

  // Add filters
  if (query.type) {
    conditions.push(eq(documents.type, query.type));
  }

  if (query.status) {
    conditions.push(eq(documents.status, query.status));
  }

  // Simple query without complex joins for now
  const result = await db
    .select()
    .from(documents)
    .where(and(...conditions))
    .orderBy(query.sort_order === 'desc' ? desc(documents.uploaded_at) : asc(documents.uploaded_at))
    .limit(query.limit)
    .offset(offset);

  // Get total count
  const totalCount = await db
    .select()
    .from(documents)
    .where(and(...conditions));

  res.json({
    success: true,
    data: result,
    pagination: {
      page: query.page,
      limit: query.limit,
      total: totalCount.length,
      pages: Math.ceil(totalCount.length / query.limit),
    },
  });
}));

// Get all documents with filtering
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const query = querySchema.parse(req.query);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const offset = (query.page - 1) * query.limit;
  const conditions = [];

  // Add filters
  if (query.type) {
    conditions.push(eq(documents.type, query.type));
  }

  if (query.status) {
    conditions.push(eq(documents.status, query.status));
  }

  // Simple query
  const baseQuery = db.select().from(documents);
  const result = conditions.length > 0 
    ? await baseQuery
        .where(and(...conditions))
        .orderBy(query.sort_order === 'desc' ? desc(documents.uploaded_at) : asc(documents.uploaded_at))
        .limit(query.limit)
        .offset(offset)
    : await baseQuery
        .orderBy(query.sort_order === 'desc' ? desc(documents.uploaded_at) : asc(documents.uploaded_at))
        .limit(query.limit)
        .offset(offset);

  // Get total count
  const totalResult = conditions.length > 0 
    ? await baseQuery.where(and(...conditions))
    : await baseQuery;

  res.json({
    success: true,
    data: result,
    pagination: {
      page: query.page,
      limit: query.limit,
      total: totalResult.length,
      pages: Math.ceil(totalResult.length / query.limit),
    },
  });
}));

// Get document by ID
router.get('/:documentId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { documentId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const result = await db
    .select()
    .from(documents)
    .where(eq(documents.document_id, documentId))
    .limit(1);

  if (result.length === 0) {
    throw new AppError('Document not found', 404);
  }

  // Check if user has access to the lead
  const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.lead_id, result[0].lead_id))
    .limit(1);

  if (lead.length === 0) {
    throw new AppError('Associated lead not found', 404);
  }

  const hasAccess = lead[0].assigned_to_id === userId;

  if (!hasAccess) {
    throw new AppError('Access denied', 403);
  }

  res.json({
    success: true,
    data: result[0],
  });
}));

// Update document
router.put('/:documentId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { documentId } = req.params;
  const validatedData = updateDocumentSchema.parse(req.body);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  // Check if document exists
  const existingDocument = await db
    .select()
    .from(documents)
    .where(eq(documents.document_id, documentId))
    .limit(1);

  if (existingDocument.length === 0) {
    throw new AppError('Document not found', 404);
  }

  // Check if user has access to the lead
  const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.lead_id, existingDocument[0].lead_id))
    .limit(1);

  if (lead.length === 0) {
    throw new AppError('Associated lead not found', 404);
  }

  const hasAccess = lead[0].assigned_to_id === userId;

  if (!hasAccess) {
    throw new AppError('Access denied', 403);
  }

  const updatedDocument = await db
    .update(documents)
    .set({
      ...validatedData,
      updated_at: new Date(),
    })
    .where(eq(documents.document_id, documentId))
    .returning();

  await logAuditEvent(userId, 'document_updated', 'crm_documents', {
    resourceId: documentId,
  });

  res.json({
    success: true,
    data: updatedDocument[0],
  });
}));

// Delete document
router.delete('/:documentId', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { documentId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  // Check if document exists
  const existingDocument = await db
    .select()
    .from(documents)
    .where(eq(documents.document_id, documentId))
    .limit(1);

  if (existingDocument.length === 0) {
    throw new AppError('Document not found', 404);
  }

  // Check if user has access to the lead
  const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.lead_id, existingDocument[0].lead_id))
    .limit(1);

  if (lead.length === 0) {
    throw new AppError('Associated lead not found', 404);
  }

  const hasAccess = lead[0].assigned_to_id === userId;

  if (!hasAccess) {
    throw new AppError('Access denied', 403);
  }

  // Delete from storage if needed
  try {
    await storageService.deleteFile(existingDocument[0].url);
  } catch (error) {
    // Log but don't fail the deletion
    logger.warn('Failed to delete file from storage:', undefined, { data: error });
  }

  await db
    .delete(documents)
    .where(eq(documents.document_id, documentId));

  await logAuditEvent(userId, 'document_deleted', 'crm_documents', {
    resourceId: documentId,
    lead_id: existingDocument[0].lead_id,
    type: existingDocument[0].type,
  });

  res.json({
    success: true,
    message: 'Document deleted successfully',
  });
}));

export default router;
