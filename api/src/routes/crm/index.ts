import { Router } from 'express';
import teamsRouter from './teams.js';
import leadsRouter from './leads.js';
import leadActionsRouter from './lead-actions.js';
import contactsRouter from './contacts.js';
import activityLogRouter from './activity-log.js';
import documentsRouter from './documents.js';
import campaignsRouter from './campaigns.js';

const router = Router();

// Mount all CRM routes
router.use('/teams', teamsRouter);
router.use('/leads', leadsRouter);
router.use('/lead-actions', leadActionsRouter);
router.use('/contacts', contactsRouter);
router.use('/activity-log', activityLogRouter);
router.use('/documents', documentsRouter);
router.use('/campaigns', campaignsRouter);

export default router;
