import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../../db/index.js';
import { eq, desc, like, or, asc, sql, and, isNull } from 'drizzle-orm';
import { AppError, asyncHandler } from '../../middleware/errorHandler.js';
import { opalAuthMiddleware } from '../../middleware/opalauth.js';
import { logAuditEvent } from '../../utils/logger.js';
import { lead_actions, actionStatusEnum, leads } from '../../db/schema/crm.js';
import { users } from '../../db/schema/users.js';

const router = Router();

// Validation schemas
const createLeadActionSchema = z.object({
  lead_id: z.string().uuid(),
  title: z.string().min(1, 'Action title is required'),
  description: z.string().optional(),
  status: z.enum(actionStatusEnum.enumValues).default('pending'),
  due_date: z.string().optional().transform(val => val ? new Date(val) : undefined),
  assigned_user_id: z.string().uuid().optional(),
  notes: z.string().optional(),
});

const updateLeadActionSchema = createLeadActionSchema.partial();

// Create lead action
router.post('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Check if user has permission to create lead actions
  if (!['Admin', 'Sales', 'Ops', 'Agent', 'Exec'].includes(userRole)) {
    throw new AppError('You do not have permission to create lead actions', 403);
  }
  
  const validatedData = createLeadActionSchema.parse(req.body);
  
  // Verify lead exists
  const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.id, validatedData.lead_id))
    .execute();
  
  if (!lead || lead.length === 0) {
    throw new AppError('Lead not found', 404);
  }
  
  // Apply role-based access control for the lead
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      lead[0].created_by === userId || 
      lead[0].assigned_user_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to add actions to this lead', 403);
    }
  }
  
  // If assigned_user_id is provided, verify it exists
  if (validatedData.assigned_user_id) {
    const assignedUser = await db
      .select()
      .from(users)
      .where(eq(users.id, validatedData.assigned_user_id))
      .execute();
    
    if (!assignedUser || assignedUser.length === 0) {
      throw new AppError('Assigned user not found', 404);
    }
  }
  
  const [newLeadAction] = await db
    .insert(lead_actions)
    .values({
      ...validatedData,
      created_at: new Date(),
      updated_at: new Date(),
      created_by: userId,
    })
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEAD_ACTION_CREATED',
    'lead_actions',
    {
      actionId: newLeadAction.id,
      leadId: newLeadAction.lead_id,
      actionTitle: newLeadAction.title,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.status(201).json({
    message: 'Lead action created successfully',
    action: newLeadAction,
  });
}));

// Get all lead actions with filtering and pagination
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Extract query parameters
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search as string;
  const leadId = req.query.lead_id as string;
  const status = req.query.status as typeof actionStatusEnum.enumValues[number];
  const assignedUserId = req.query.assigned_user_id as string;
  const sortBy = req.query.sortBy as string || 'due_date';
  const sortOrder = req.query.sortOrder as string || 'asc';
  
  // Build query
  let query = db.select({
    id: lead_actions.id,
    lead_id: lead_actions.lead_id,
    title: lead_actions.title,
    description: lead_actions.description,
    status: lead_actions.status,
    due_date: lead_actions.due_date,
    assigned_user_id: lead_actions.assigned_user_id,
    notes: lead_actions.notes,
    created_at: lead_actions.created_at,
    updated_at: lead_actions.updated_at,
    created_by: lead_actions.created_by,
    // Join with users to get assigned user name
    assigned_user_first_name: users.firstName,
    assigned_user_last_name: users.lastName,
    // Join with leads to get lead title
    lead_title: leads.title,
  })
  .from(lead_actions)
  .leftJoin(users, eq(lead_actions.assigned_user_id, users.id))
  .leftJoin(leads, eq(lead_actions.lead_id, leads.id));
  
  // Apply filters
  const conditions = [];
  
  if (search) {
    conditions.push(
      or(
        like(lead_actions.title, `%${search}%`),
        like(lead_actions.description || '', `%${search}%`),
        like(lead_actions.notes || '', `%${search}%`)
      )
    );
  }
  
  if (leadId) {
    conditions.push(eq(lead_actions.lead_id, leadId));
  }
  
  if (status) {
    conditions.push(eq(lead_actions.status, status));
  }
  
  if (assignedUserId) {
    conditions.push(eq(lead_actions.assigned_user_id, assignedUserId));
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    // Non-admin users can only see actions they created, are assigned to them, or for leads they own/are assigned to
    conditions.push(
      or(
        eq(lead_actions.created_by, userId),
        eq(lead_actions.assigned_user_id, userId),
        eq(leads.created_by, userId),
        eq(leads.assigned_user_id, userId)
        // TODO: Add team membership check when team-user relationship is implemented
      )
    );
  }
  
  if (conditions.length > 0) {
    query = query.where(and(...conditions));
  }
  
  // Get total count for pagination
  const countQuery = db
    .select({ count: sql`count(*)` })
    .from(lead_actions)
    .leftJoin(leads, eq(lead_actions.lead_id, leads.id));
  
  if (conditions.length > 0) {
    countQuery.where(and(...conditions));
  }
  
  const totalCountResult = await countQuery.execute();
  const total = Number(totalCountResult[0]?.count || 0);
  
  // Apply sorting
  if (sortBy === 'title') {
    query = sortOrder === 'desc' 
      ? query.orderBy(desc(lead_actions.title))
      : query.orderBy(asc(lead_actions.title));
  } else if (sortBy === 'status') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(lead_actions.status))
      : query.orderBy(asc(lead_actions.status));
  } else if (sortBy === 'due_date') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(lead_actions.due_date))
      : query.orderBy(asc(lead_actions.due_date));
  } else if (sortBy === 'created_at') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(lead_actions.created_at))
      : query.orderBy(asc(lead_actions.created_at));
  }
  
  // Apply pagination
  query = query.limit(limit).offset(offset);
  
  // Execute query
  const actionsList = await query.execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEAD_ACTIONS_ACCESSED',
    'lead_actions',
    {
      filters: { search, leadId, status, assignedUserId },
      pagination: { page, limit },
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    actions: actionsList,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get lead actions by lead ID
router.get('/lead/:leadId', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const leadId = req.params.leadId;
  
  // Verify lead exists
  const lead = await db
    .select()
    .from(leads)
    .where(eq(leads.id, leadId))
    .execute();
  
  if (!lead || lead.length === 0) {
    throw new AppError('Lead not found', 404);
  }
  
  // Apply role-based access control for the lead
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      lead[0].created_by === userId || 
      lead[0].assigned_user_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to access actions for this lead', 403);
    }
  }
  
  // Extract query parameters
  const status = req.query.status as typeof actionStatusEnum.enumValues[number];
  const sortBy = req.query.sortBy as string || 'due_date';
  const sortOrder = req.query.sortOrder as string || 'asc';
  
  // Build query
  let query = db.select({
    id: lead_actions.id,
    lead_id: lead_actions.lead_id,
    title: lead_actions.title,
    description: lead_actions.description,
    status: lead_actions.status,
    due_date: lead_actions.due_date,
    assigned_user_id: lead_actions.assigned_user_id,
    notes: lead_actions.notes,
    created_at: lead_actions.created_at,
    updated_at: lead_actions.updated_at,
    created_by: lead_actions.created_by,
    // Join with users to get assigned user name
    assigned_user_first_name: users.firstName,
    assigned_user_last_name: users.lastName,
  })
  .from(lead_actions)
  .leftJoin(users, eq(lead_actions.assigned_user_id, users.id))
  .where(eq(lead_actions.lead_id, leadId));
  
  // Apply status filter if provided
  if (status) {
    query = query.where(eq(lead_actions.status, status));
  }
  
  // Apply sorting
  if (sortBy === 'title') {
    query = sortOrder === 'desc' 
      ? query.orderBy(desc(lead_actions.title))
      : query.orderBy(asc(lead_actions.title));
  } else if (sortBy === 'status') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(lead_actions.status))
      : query.orderBy(asc(lead_actions.status));
  } else if (sortBy === 'due_date') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(lead_actions.due_date))
      : query.orderBy(asc(lead_actions.due_date));
  } else if (sortBy === 'created_at') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(lead_actions.created_at))
      : query.orderBy(asc(lead_actions.created_at));
  }
  
  // Execute query
  const actionsList = await query.execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEAD_ACTIONS_BY_LEAD_ACCESSED',
    'lead_actions',
    {
      leadId,
      filters: { status },
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    actions: actionsList,
  });
}));

// Get lead action by ID
router.get('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const actionId = req.params.id;
  
  // Get lead action with related data
  const action = await db
    .select({
      id: lead_actions.id,
      lead_id: lead_actions.lead_id,
      title: lead_actions.title,
      description: lead_actions.description,
      status: lead_actions.status,
      due_date: lead_actions.due_date,
      assigned_user_id: lead_actions.assigned_user_id,
      notes: lead_actions.notes,
      created_at: lead_actions.created_at,
      updated_at: lead_actions.updated_at,
      created_by: lead_actions.created_by,
      // Join with users to get assigned user name
      assigned_user_first_name: users.firstName,
      assigned_user_last_name: users.lastName,
      // Join with leads to get lead data
      lead_title: leads.title,
      lead_created_by: leads.created_by,
      lead_assigned_user_id: leads.assigned_user_id,
    })
    .from(lead_actions)
    .leftJoin(users, eq(lead_actions.assigned_user_id, users.id))
    .leftJoin(leads, eq(lead_actions.lead_id, leads.id))
    .where(eq(lead_actions.id, actionId))
    .execute();
  
  if (!action || action.length === 0) {
    throw new AppError('Lead action not found', 404);
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      action[0].created_by === userId || 
      action[0].assigned_user_id === userId ||
      action[0].lead_created_by === userId ||
      action[0].lead_assigned_user_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to access this lead action', 403);
    }
  }
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEAD_ACTION_ACCESSED',
    'lead_actions',
    {
      actionId,
      leadId: action[0].lead_id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    action: action[0],
  });
}));

// Update lead action by ID
router.put('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const actionId = req.params.id;
  
  // Check if action exists
  const existingAction = await db
    .select({
      id: lead_actions.id,
      lead_id: lead_actions.lead_id,
      created_by: lead_actions.created_by,
      assigned_user_id: lead_actions.assigned_user_id,
      // Join with leads to get lead data
      lead_created_by: leads.created_by,
      lead_assigned_user_id: leads.assigned_user_id,
    })
    .from(lead_actions)
    .leftJoin(leads, eq(lead_actions.lead_id, leads.id))
    .where(eq(lead_actions.id, actionId))
    .execute();
  
  if (!existingAction || existingAction.length === 0) {
    throw new AppError('Lead action not found', 404);
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      existingAction[0].created_by === userId || 
      existingAction[0].assigned_user_id === userId ||
      existingAction[0].lead_created_by === userId ||
      existingAction[0].lead_assigned_user_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to update this lead action', 403);
    }
  }
  
  const validatedData = updateLeadActionSchema.parse(req.body);
  
  // If lead_id is provided, verify it exists
  if (validatedData.lead_id) {
    const lead = await db
      .select()
      .from(leads)
      .where(eq(leads.id, validatedData.lead_id))
      .execute();
    
    if (!lead || lead.length === 0) {
      throw new AppError('Lead not found', 404);
    }
    
    // Apply role-based access control for the new lead
    if (!['Admin', 'Exec'].includes(userRole)) {
      const userHasAccess = 
        lead[0].created_by === userId || 
        lead[0].assigned_user_id === userId;
        // TODO: Add team membership check when team-user relationship is implemented
      
      if (!userHasAccess) {
        throw new AppError('You do not have permission to move this action to the specified lead', 403);
      }
    }
  }
  
  // If assigned_user_id is provided, verify it exists
  if (validatedData.assigned_user_id) {
    const assignedUser = await db
      .select()
      .from(users)
      .where(eq(users.id, validatedData.assigned_user_id))
      .execute();
    
    if (!assignedUser || assignedUser.length === 0) {
      throw new AppError('Assigned user not found', 404);
    }
  }
  
  // Update lead action
  const [updatedAction] = await db
    .update(lead_actions)
    .set({
      ...validatedData,
      updated_at: new Date(),
    })
    .where(eq(lead_actions.id, actionId))
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEAD_ACTION_UPDATED',
    'lead_actions',
    {
      actionId,
      leadId: updatedAction.lead_id,
      changes: validatedData,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Lead action updated successfully',
    action: updatedAction,
  });
}));

// Delete lead action by ID
router.delete('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const actionId = req.params.id;
  
  // Check if action exists
  const existingAction = await db
    .select({
      id: lead_actions.id,
      lead_id: lead_actions.lead_id,
      title: lead_actions.title,
      created_by: lead_actions.created_by,
      assigned_user_id: lead_actions.assigned_user_id,
      // Join with leads to get lead data
      lead_created_by: leads.created_by,
      lead_assigned_user_id: leads.assigned_user_id,
    })
    .from(lead_actions)
    .leftJoin(leads, eq(lead_actions.lead_id, leads.id))
    .where(eq(lead_actions.id, actionId))
    .execute();
  
  if (!existingAction || existingAction.length === 0) {
    throw new AppError('Lead action not found', 404);
  }
  
  // Apply role-based access control - only Admin, Exec, or the creator can delete actions
  if (!['Admin', 'Exec'].includes(userRole) && existingAction[0].created_by !== userId) {
    throw new AppError('You do not have permission to delete this lead action', 403);
  }
  
  // Delete lead action
  await db
    .delete(lead_actions)
    .where(eq(lead_actions.id, actionId));
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEAD_ACTION_DELETED',
    'lead_actions',
    {
      actionId,
      leadId: existingAction[0].lead_id,
      actionTitle: existingAction[0].title,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Lead action deleted successfully',
  });
}));

export default router;
