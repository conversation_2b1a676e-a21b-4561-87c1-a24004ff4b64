import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../../db/index.js';
import { eq, desc, like, or, asc, sql, and, isNull, ne } from 'drizzle-orm';
import { AppError, asyncHandler } from '../../middleware/errorHandler.js';
import { opalAuthMiddleware } from '../../middleware/opalAuth.js';
import { logAuditEvent } from '../../utils/logger.js';
import { leads, leadTypeEnum, leadSourceEnum, leadStatusEnum, leadStageEnum } from '../../db/schema/crm.js';
import { users } from '../../db/schema/users.js';
import { teams } from '../../db/schema/crm.js';

const router = Router();

// Validation schemas
const createLeadSchema = z.object({
  type: z.enum(leadTypeEnum.enumValues),
  source: z.enum(leadSourceEnum.enumValues),
  status: z.enum(leadStatusEnum.enumValues).default('new'),
  stage: z.enum(leadStageEnum.enumValues).default('awareness'),
  title: z.string().min(1, 'Lead title is required'),
  description: z.string().optional(),
  assigned_user_id: z.string().uuid().optional(),
  assigned_team_id: z.string().uuid().optional(),
  organization_name: z.string().optional(),
  organization_size: z.string().optional(),
  industry: z.string().optional(),
  website: z.string().optional(),
  annual_revenue: z.string().optional(),
  territory: z.string().optional(),
  notes: z.string().optional(),
});

const updateLeadSchema = createLeadSchema.partial();

// Create lead
router.post('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Check if user has permission to create leads
  if (!['Admin', 'Sales', 'Ops', 'Agent', 'Exec'].includes(userRole)) {
    throw new AppError('You do not have permission to create leads', 403);
  }
  
  const validatedData = createLeadSchema.parse(req.body);
  
  // If assigned_user_id is provided, verify it exists
  if (validatedData.assigned_user_id) {
    const assignedUser = await db
      .select()
      .from(users)
      .where(eq(users.id, validatedData.assigned_user_id))
      .execute();
    
    if (!assignedUser || assignedUser.length === 0) {
      throw new AppError('Assigned user not found', 404);
    }
  }
  
  // If assigned_team_id is provided, verify it exists
  if (validatedData.assigned_team_id) {
    const assignedTeam = await db
      .select()
      .from(teams)
      .where(eq(teams.id, validatedData.assigned_team_id))
      .execute();
    
    if (!assignedTeam || assignedTeam.length === 0) {
      throw new AppError('Assigned team not found', 404);
    }
  }
  
  const [newLead] = await db
    .insert(leads)
    .values({
      ...validatedData,
      created_at: new Date(),
      updated_at: new Date(),
      created_by: userId,
    })
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEAD_CREATED',
    'leads',
    {
      leadId: newLead.id,
      leadTitle: newLead.title,
      leadType: newLead.type,
      leadSource: newLead.source,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.status(201).json({
    message: 'Lead created successfully',
    lead: newLead,
  });
}));

// Get all leads with filtering and pagination
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Extract query parameters
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search as string;
  const type = req.query.type as typeof leadTypeEnum.enumValues[number];
  const source = req.query.source as typeof leadSourceEnum.enumValues[number];
  const status = req.query.status as typeof leadStatusEnum.enumValues[number];
  const stage = req.query.stage as typeof leadStageEnum.enumValues[number];
  const assignedUserId = req.query.assigned_user_id as string;
  const assignedTeamId = req.query.assigned_team_id as string;
  const unassigned = req.query.unassigned === 'true';
  const sortBy = req.query.sortBy as string || 'created_at';
  const sortOrder = req.query.sortOrder as string || 'desc';
  
  // Build query
  let query = db.select({
    id: leads.id,
    type: leads.type,
    source: leads.source,
    status: leads.status,
    stage: leads.stage,
    title: leads.title,
    description: leads.description,
    assigned_user_id: leads.assigned_user_id,
    assigned_team_id: leads.assigned_team_id,
    organization_name: leads.organization_name,
    organization_size: leads.organization_size,
    industry: leads.industry,
    website: leads.website,
    annual_revenue: leads.annual_revenue,
    territory: leads.territory,
    notes: leads.notes,
    created_at: leads.created_at,
    updated_at: leads.updated_at,
    created_by: leads.created_by,
    // Join with users to get assigned user name
    assigned_user_first_name: users.firstName,
    assigned_user_last_name: users.lastName,
  })
  .from(leads)
  .leftJoin(users, eq(leads.assigned_user_id, users.id));
  
  // Apply filters
  const conditions = [];
  
  if (search) {
    conditions.push(
      or(
        like(leads.title, `%${search}%`),
        like(leads.description || '', `%${search}%`),
        like(leads.organization_name || '', `%${search}%`)
      )
    );
  }
  
  if (type) {
    conditions.push(eq(leads.type, type));
  }
  
  if (source) {
    conditions.push(eq(leads.source, source));
  }
  
  if (status) {
    conditions.push(eq(leads.status, status));
  }
  
  if (stage) {
    conditions.push(eq(leads.stage, stage));
  }
  
  if (assignedUserId) {
    conditions.push(eq(leads.assigned_user_id, assignedUserId));
  }
  
  if (assignedTeamId) {
    conditions.push(eq(leads.assigned_team_id, assignedTeamId));
  }
  
  if (unassigned) {
    conditions.push(and(isNull(leads.assigned_user_id), isNull(leads.assigned_team_id)));
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    // Non-admin users can only see leads they created or are assigned to them or their team
    conditions.push(
      or(
        eq(leads.created_by, userId),
        eq(leads.assigned_user_id, userId),
        // TODO: Add team membership check when team-user relationship is implemented
      )
    );
  }
  
  if (conditions.length > 0) {
    query = query.where(and(...conditions));
  }
  
  // Get total count for pagination
  const countQuery = db
    .select({ count: sql`count(*)` })
    .from(leads);
  
  if (conditions.length > 0) {
    countQuery.where(and(...conditions));
  }
  
  const totalCountResult = await countQuery.execute();
  const total = Number(totalCountResult[0]?.count || 0);
  
  // Apply sorting
  if (sortBy === 'title') {
    query = sortOrder === 'desc' 
      ? query.orderBy(desc(leads.title))
      : query.orderBy(asc(leads.title));
  } else if (sortBy === 'status') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(leads.status))
      : query.orderBy(asc(leads.status));
  } else if (sortBy === 'stage') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(leads.stage))
      : query.orderBy(asc(leads.stage));
  } else if (sortBy === 'created_at') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(leads.created_at))
      : query.orderBy(asc(leads.created_at));
  } else if (sortBy === 'updated_at') {
    query = sortOrder === 'desc'
      ? query.orderBy(desc(leads.updated_at))
      : query.orderBy(asc(leads.updated_at));
  }
  
  // Apply pagination
  query = query.limit(limit).offset(offset);
  
  // Execute query
  const leadsList = await query.execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEADS_ACCESSED',
    'leads',
    {
      filters: { search, type, source, status, stage, assignedUserId, assignedTeamId, unassigned },
      pagination: { page, limit },
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    leads: leadsList,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get lead by ID
router.get('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const leadId = req.params.id;
  
  const lead = await db
    .select({
      id: leads.id,
      type: leads.type,
      source: leads.source,
      status: leads.status,
      stage: leads.stage,
      title: leads.title,
      description: leads.description,
      assigned_user_id: leads.assigned_user_id,
      assigned_team_id: leads.assigned_team_id,
      organization_name: leads.organization_name,
      organization_size: leads.organization_size,
      industry: leads.industry,
      website: leads.website,
      annual_revenue: leads.annual_revenue,
      territory: leads.territory,
      notes: leads.notes,
      created_at: leads.created_at,
      updated_at: leads.updated_at,
      created_by: leads.created_by,
      // Join with users to get assigned user name
      assigned_user_first_name: users.firstName,
      assigned_user_last_name: users.lastName,
    })
    .from(leads)
    .leftJoin(users, eq(leads.assigned_user_id, users.id))
    .where(eq(leads.id, leadId))
    .execute();
  
  if (!lead || lead.length === 0) {
    throw new AppError('Lead not found', 404);
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      lead[0].created_by === userId || 
      lead[0].assigned_user_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to access this lead', 403);
    }
  }
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEAD_ACCESSED',
    'leads',
    {
      leadId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    lead: lead[0],
  });
}));

// Update lead by ID
router.put('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const leadId = req.params.id;
  
  // Check if lead exists
  const existingLead = await db
    .select()
    .from(leads)
    .where(eq(leads.id, leadId))
    .execute();
  
  if (!existingLead || existingLead.length === 0) {
    throw new AppError('Lead not found', 404);
  }
  
  // Apply role-based access control
  if (!['Admin', 'Exec'].includes(userRole)) {
    const userHasAccess = 
      existingLead[0].created_by === userId || 
      existingLead[0].assigned_user_id === userId;
      // TODO: Add team membership check when team-user relationship is implemented
    
    if (!userHasAccess) {
      throw new AppError('You do not have permission to update this lead', 403);
    }
  }
  
  const validatedData = updateLeadSchema.parse(req.body);
  
  // If assigned_user_id is provided, verify it exists
  if (validatedData.assigned_user_id) {
    const assignedUser = await db
      .select()
      .from(users)
      .where(eq(users.id, validatedData.assigned_user_id))
      .execute();
    
    if (!assignedUser || assignedUser.length === 0) {
      throw new AppError('Assigned user not found', 404);
    }
  }
  
  // If assigned_team_id is provided, verify it exists
  if (validatedData.assigned_team_id) {
    const assignedTeam = await db
      .select()
      .from(teams)
      .where(eq(teams.id, validatedData.assigned_team_id))
      .execute();
    
    if (!assignedTeam || assignedTeam.length === 0) {
      throw new AppError('Assigned team not found', 404);
    }
  }
  
  // Update lead
  const [updatedLead] = await db
    .update(leads)
    .set({
      ...validatedData,
      updated_at: new Date(),
    })
    .where(eq(leads.id, leadId))
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEAD_UPDATED',
    'leads',
    {
      leadId,
      changes: validatedData,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Lead updated successfully',
    lead: updatedLead,
  });
}));

// Delete lead by ID
router.delete('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const leadId = req.params.id;
  
  // Check if lead exists
  const existingLead = await db
    .select()
    .from(leads)
    .where(eq(leads.id, leadId))
    .execute();
  
  if (!existingLead || existingLead.length === 0) {
    throw new AppError('Lead not found', 404);
  }
  
  // Apply role-based access control - only Admin, Exec, or the creator can delete leads
  if (!['Admin', 'Exec'].includes(userRole) && existingLead[0].created_by !== userId) {
    throw new AppError('You do not have permission to delete this lead', 403);
  }
  
  // Delete lead - cascade will handle related records
  await db
    .delete(leads)
    .where(eq(leads.id, leadId));
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_LEAD_DELETED',
    'leads',
    {
      leadId,
      leadTitle: existingLead[0].title,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Lead deleted successfully',
  });
}));

export default router;
