import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../../db/index.js';
import { eq, desc, like, or, asc, sql } from 'drizzle-orm';
import { AppError, asyncHandler } from '../../middleware/errorHandler.js';
import { opalAuthMiddleware, requireRole } from '../../middleware/opalAuth.js';
import { logAuditEvent } from '../../utils/logger.js';
import { teams } from '../../db/schema/crm.js';

const router = Router();

// Validation schemas
const createTeamSchema = z.object({
  name: z.string().min(1, 'Team name is required'),
  description: z.string().optional(),
});

const updateTeamSchema = createTeamSchema.partial();

// Create team (Admin, Sales, Ops, Exec roles only)
router.post('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Check if user has permission to create teams
  if (!['Admin', 'Sales', 'Ops', 'Exec'].includes(userRole)) {
    throw new AppError('You do not have permission to create teams', 403);
  }
  
  const validatedData = createTeamSchema.parse(req.body);
  
  const [newTeam] = await db
    .insert(teams)
    .values({
      ...validatedData,
      created_at: new Date(),
      updated_at: new Date(),
    })
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_TEAM_CREATED',
    'teams',
    {
      teamId: newTeam.team_id,
      teamName: newTeam.name,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.status(201).json({
    message: 'Team created successfully',
    team: newTeam,
  });
}));

// Get all teams with filtering and pagination
router.get('/', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Extract query parameters
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const offset = (page - 1) * limit;
  const search = req.query.search as string;
  const sortBy = req.query.sortBy as string || 'name';
  const sortOrder = req.query.sortOrder as string || 'asc';
  
  // Build where condition for filtering
  let whereCondition = undefined;
  
  if (search) {
    whereCondition = or(
      like(teams.name, `%${search}%`),
      like(teams.description || '', `%${search}%`)
    );
  }
  
  // Get total count for pagination
  const totalCountQuery = db
    .select({ count: sql`count(*)` })
    .from(teams);
    
  if (whereCondition) {
    totalCountQuery.where(whereCondition);
  }
  
  const totalCountResult = await totalCountQuery.execute();
  const total = Number(totalCountResult[0]?.count || 0);
  
  // Determine sort order
  const orderBy = [];
  if (sortBy === 'name') {
    orderBy.push(sortOrder === 'desc' ? desc(teams.name) : asc(teams.name));
  } else if (sortBy === 'created_at') {
    orderBy.push(sortOrder === 'desc' ? desc(teams.created_at) : asc(teams.created_at));
  }
  
  // Execute main query with filtering, sorting and pagination
  const teamsList = await db
    .select()
    .from(teams)
    .where(whereCondition ? whereCondition : undefined)
    .orderBy(...orderBy)
    .limit(limit)
    .offset(offset)
    .execute();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_TEAMS_ACCESSED',
    'teams',
    {
      filters: { search, sortBy, sortOrder },
      pagination: { page, limit },
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    teams: teamsList,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get team by ID
router.get('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const teamId = req.params.id;
  
  const team = await db
    .select()
    .from(teams)
    .where(eq(teams.team_id, teamId))
    .execute();
  
  if (!team || team.length === 0) {
    throw new AppError('Team not found', 404);
  }
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_TEAM_ACCESSED',
    'teams',
    {
      teamId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    team: team[0],
  });
}));

// Update team by ID
router.put('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const teamId = req.params.id;
  
  // Check if user has permission to update teams
  if (!['Admin', 'Sales', 'Ops', 'Exec'].includes(userRole)) {
    throw new AppError('You do not have permission to update teams', 403);
  }
  
  // Check if team exists
  const existingTeam = await db
    .select()
    .from(teams)
    .where(eq(teams.team_id, teamId))
    .execute();
  
  if (!existingTeam || existingTeam.length === 0) {
    throw new AppError('Team not found', 404);
  }
  
  const validatedData = updateTeamSchema.parse(req.body);
  
  // Update team
  const [updatedTeam] = await db
    .update(teams)
    .set({
      ...validatedData,
      updated_at: new Date(),
    })
    .where(eq(teams.team_id, teamId))
    .returning();
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_TEAM_UPDATED',
    'teams',
    {
      teamId,
      changes: validatedData,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Team updated successfully',
    team: updatedTeam,
  });
}));

// Delete team by ID
router.delete('/:id', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  const teamId = req.params.id;
  
  // Check if user has permission to delete teams
  if (!['Admin', 'Exec'].includes(userRole)) {
    throw new AppError('You do not have permission to delete teams', 403);
  }
  
  // Check if team exists
  const existingTeam = await db
    .select()
    .from(teams)
    .where(eq(teams.team_id, teamId))
    .execute();
  
  if (!existingTeam || existingTeam.length === 0) {
    throw new AppError('Team not found', 404);
  }
  
  // Delete team
  await db
    .delete(teams)
    .where(eq(teams.team_id, teamId));
  
  // Log audit event
  logAuditEvent(
    userId,
    'CRM_TEAM_DELETED',
    'teams',
    {
      teamId,
      teamName: existingTeam[0].name,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  
  res.json({
    message: 'Team deleted successfully',
  });
}));

export default router;
