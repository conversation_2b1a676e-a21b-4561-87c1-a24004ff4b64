import { Router, Request, Response } from 'express';
import { db } from '../db/index.js';
import { users } from '../db/schema/users.js';
import { cases, medicalDocuments } from '../db/schema/cases.js';
import { contacts, touchpoints } from '../db/schema/crm.js';
import { appointments } from '../db/schema/appointments.js';
import { sql, count, eq, and, or } from 'drizzle-orm';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { opalAuthMiddleware } from '../middleware/opalAuth.js';
import { logAuditEvent } from '../utils/logger.js';
import { canUserAccess } from '../utils/policyUtils.js';

import { logger } from '../utils/structuredLogger';
const router = Router();

// Get dashboard statistics based on user role
router.get('/stats', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user.id;
  const userRole = (req as any).user.role;
  
  // Common statistics for all roles
  const stats: Record<string, any> = {};
  
  try {
    // Role-specific statistics
    if (userRole === 'admin' || userRole === 'agent') {
      // Active users count
      const [activeUsersResult] = await db
        .select({ count: count() })
        .from(users)
        .where(eq(users.isActive, true));
      
      stats.activeUsers = Number(activeUsersResult?.count || 0);
      
      // Active cases count (in_review + assigned)
      const [activeCasesResult] = await db
        .select({ count: count() })
        .from(cases)
        .where(or(
          eq(cases.status, 'in_review'),
          eq(cases.status, 'assigned')
        ));
      
      stats.activeCases = Number(activeCasesResult?.count || 0);
      
      // Active chats (touchpoints of type 'call' or 'meeting')
      const [activeChatsResult] = await db
        .select({ count: count() })
        .from(touchpoints)
        .where(or(
          eq(touchpoints.type, 'call'),
          eq(touchpoints.type, 'meeting')
        ));
      
      stats.activeChats = Number(activeChatsResult?.count || 0);
      
      // Cases coordinated (total cases)
      const [casesCoordinatedResult] = await db
        .select({ count: count() })
        .from(cases);
      
      stats.casesCoordinated = Number(casesCoordinatedResult?.count || 0);
      
      // Pending tasks (appointments with status 'scheduled')
      const [pendingTasksResult] = await db
        .select({ count: count() })
        .from(appointments)
        .where(eq(appointments.status, 'scheduled'));
      
      stats.pendingTasks = Number(pendingTasksResult?.count || 0);
      
      // Total contacts
      const [totalContactsResult] = await db
        .select({ count: count() })
        .from(contacts);
      
      stats.totalContacts = Number(totalContactsResult?.count || 0);
      
      // Total documents
      const [totalDocumentsResult] = await db
        .select({ count: count() })
        .from(medicalDocuments);
      
      stats.totalDocuments = Number(totalDocumentsResult?.count || 0);
      
      // Satisfaction rating (hardcoded for now, would come from a ratings table)
      stats.satisfactionRating = 4.9;
    }
    
    // Doctor-specific statistics
    if (userRole === 'doctor') {
      // Doctor's active cases (in_review + assigned) using case_doctors table
      const activeCasesResult = await db
        .select({ count: count() })
        .from(cases)
        .innerJoin(caseDoctors, eq(cases.id, caseDoctors.caseId))
        .where(and(
          eq(caseDoctors.doctorId, userId),
          eq(caseDoctors.isActive, true),
          or(
            eq(cases.status, 'in_review'),
            eq(cases.status, 'assigned')
          )
        ));
      
      stats.activeCases = activeCasesResult.length > 0 ? Number(activeCasesResult[0].count) : 0;
      
      // Doctor's appointments
      const [appointmentsResult] = await db
        .select({ count: count() })
        .from(appointments)
        .where(eq(appointments.doctorId, userId));
      
      stats.appointments = Number(appointmentsResult?.count || 0);
      
      // Doctor's pending appointments
      const [pendingAppointmentsResult] = await db
        .select({ count: count() })
        .from(appointments)
        .where(and(
          eq(appointments.doctorId, userId),
          eq(appointments.status, 'scheduled')
        ));
      
      stats.pendingAppointments = Number(pendingAppointmentsResult?.count || 0);
      
      // Doctor's patients (distinct patients from assigned cases)
      const patientsResult = await db
        .select({ count: countDistinct(cases.patientId) })
        .from(cases)
        .innerJoin(caseDoctors, eq(cases.id, caseDoctors.caseId))
        .where(and(
          eq(caseDoctors.doctorId, userId),
          eq(caseDoctors.isActive, true)
        ));
      
      stats.patients = patientsResult.length > 0 ? Number(patientsResult[0].count) : 0;
    }
    
    // Patient-specific statistics
    if (userRole === 'patient') {
      // Patient's appointments
      const [appointmentsResult] = await db
        .select({ count: count() })
        .from(appointments)
        .where(eq(appointments.patientId, userId));
      
      stats.appointments = Number(appointmentsResult?.count || 0);
      
      // Patient's upcoming appointments
      const [upcomingAppointmentsResult] = await db
        .select({ count: count() })
        .from(appointments)
        .where(and(
          eq(appointments.patientId, userId),
          eq(appointments.status, 'scheduled')
        ));
      
      stats.upcomingAppointments = Number(upcomingAppointmentsResult?.count || 0);
      
      // Patient's documents
      const [documentsResult] = await db
        .select({ count: count() })
        .from(medicalDocuments)
        .where(eq(medicalDocuments.uploadedBy, userId));
      
      stats.documents = Number(documentsResult?.count || 0);
    }
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Error fetching dashboard statistics:', error);
    throw new AppError('Failed to fetch dashboard statistics', 500);
  }
}));

export { router as dashboardRoutes };
