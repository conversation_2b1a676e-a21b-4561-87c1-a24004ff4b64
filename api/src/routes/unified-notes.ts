import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { db } from '../db/index.js';
import { caseNotes } from '../db/schema/case-notes.js';
import { cases } from '../db/schema/cases.js';
import { users } from '../db/schema/users.js';
import { eq, and, desc } from 'drizzle-orm';
import { opalAuthMiddleware } from '../middleware/opalAuth.js';
import { AppError, asyncHandler } from '../middleware/errorHandler.js';
import { logAuditEvent } from '../utils/logger.js';
import { CaseDoctorService } from '../services/caseDoctorService.js';

const router = Router();

// Validation schemas
const createNoteSchema = z.object({
  noteType: z.string().min(1, 'Note type is required'), // symptoms, medical_history, consultation, progress_note, etc.
  content: z.string().default(''), // Plain text content
});

const updateNoteSchema = z.object({
  content: z.string(),
});

// Note type mapping - using the single clinical_notes type for all note types
// This matches the existing note type in the database
const CLINICAL_NOTES_TYPE_ID = 'c6867d50-6dc7-443d-aa14-061ab153d685';

const NOTE_TYPE_IDS = {
  'symptoms': CLINICAL_NOTES_TYPE_ID,
  'medical_history': CLINICAL_NOTES_TYPE_ID,
  'current_medications': CLINICAL_NOTES_TYPE_ID,
  'case_description': CLINICAL_NOTES_TYPE_ID,
  'consultation': CLINICAL_NOTES_TYPE_ID,
  'progress_note': CLINICAL_NOTES_TYPE_ID,
  'second_opinion': CLINICAL_NOTES_TYPE_ID,
  'medical_opinion': CLINICAL_NOTES_TYPE_ID,
  'discharge_summary': CLINICAL_NOTES_TYPE_ID,
};

// Define which note types are accessible to which roles
const PATIENT_ACCESSIBLE_NOTES = [
  'symptoms',
  'medical_history',
  'current_medications',
  'case_description'
];

const DOCTOR_ONLY_NOTES = [
  'consultation',
  'progress_note',
  'second_opinion',
  'medical_opinion',
  'discharge_summary'
];

// Helper function to filter note types based on user role
const filterNoteTypesByRole = (noteTypes: string[], userRoles: string[]): string[] => {
  if (userRoles.includes('admin') || userRoles.includes('agent')) {
    return noteTypes; // Admins and agents can see all note types
  }
  
  if (userRoles.includes('doctor')) {
    return noteTypes; // Doctors can see all note types
  }
  
  if (userRoles.includes('patient')) {
    return noteTypes.filter(noteType => PATIENT_ACCESSIBLE_NOTES.includes(noteType));
  }
  
  return []; // Default: no access
};

// Helper function to check if user can access a specific note type
const canAccessNoteType = (noteType: string, userRoles: string[]): boolean => {
  if (userRoles.includes('admin') || userRoles.includes('agent')) {
    return true; // Admins and agents can access all note types
  }
  
  if (userRoles.includes('doctor')) {
    return true; // Doctors can access all note types
  }
  
  if (userRoles.includes('patient')) {
    return PATIENT_ACCESSIBLE_NOTES.includes(noteType);
  }
  
  return false; // Default: no access
};

// Get all notes for a case
router.get('/:caseId/notes', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.caseId;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;
  const noteType = req.query.noteType as string; // Optional filter

  // Check case access permissions
  const [caseData] = await db
    .select({
      id: cases.id,
      patientId: cases.patientId,
    })
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check access permissions
  let canAccess = 
    userRoles.includes('admin') ||
    userRoles.includes('agent') ||
    (userRoles.includes('patient') && caseData.patientId === userId);
    
  // For doctors, check using multi-doctor assignment system
  if (userRoles.includes('doctor') && !canAccess) {
    canAccess = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
  }

  if (!canAccess) {
    throw new AppError('Unauthorized to access case notes', 403);
  }

  // Build query conditions
  const whereConditions = [
    eq(caseNotes.caseId, caseId),
    eq(caseNotes.isActive, true)
  ];

  // Execute query with joins to get author info
  const notes = await db
    .select({
      id: caseNotes.id,
      caseId: caseNotes.caseId,
      noteType: caseNotes.noteTypeId, // We'll map this to readable names
      content: caseNotes.structuredContent,
      authorId: caseNotes.doctorId,
      authorName: users.firstName,
      authorLastName: users.lastName,
      authorRole: users.role,
      createdAt: caseNotes.createdAt,
      updatedAt: caseNotes.updatedAt,
      version: caseNotes.version,
    })
    .from(caseNotes)
    .leftJoin(users, eq(caseNotes.doctorId, users.id))
    .where(and(...whereConditions))
    .orderBy(desc(caseNotes.updatedAt));

  // Transform notes to extract individual note types from structured content
  const transformedNotes: any[] = [];
  
  for (const note of notes) {
    if (note.content && typeof note.content === 'object') {
      const structuredContent = note.content as any;
      
      // Extract each note type from the structured content
      for (const [noteTypeKey, content] of Object.entries(structuredContent)) {
        // Skip metadata fields (those ending with _lastUpdated or _updatedBy)
        if (noteTypeKey.endsWith('_lastUpdated') || noteTypeKey.endsWith('_updatedBy')) {
          continue;
        }
        
        // Only include note types that are in our supported list
        if (Object.keys(NOTE_TYPE_IDS).includes(noteTypeKey)) {
          transformedNotes.push({
            id: `${note.id}_${noteTypeKey}`, // Create unique ID for each note type
            caseId: note.caseId,
            noteType: noteTypeKey,
            content: content || '',
            author: {
              id: note.authorId,
              name: `${note.authorName || ''} ${note.authorLastName || ''}`.trim() || 'Unknown User',
              role: note.authorRole || 'patient',
            },
            createdAt: note.createdAt,
            updatedAt: structuredContent[`${noteTypeKey}_lastUpdated`] ? new Date(structuredContent[`${noteTypeKey}_lastUpdated`]) : note.updatedAt,
            version: note.version,
          });
        }
      }
    }
  }

  // Filter by note type if specified
  let filteredNotes = noteType
    ? transformedNotes.filter(note => note.noteType === noteType)
    : transformedNotes;

  // Apply role-based filtering - patients can't see doctor-only notes
  const accessibleNoteTypes = filterNoteTypesByRole(
    filteredNotes.map(note => note.noteType),
    userRoles
  );
  
  filteredNotes = filteredNotes.filter(note => accessibleNoteTypes.includes(note.noteType));

  // Log audit event
  logAuditEvent(
    userId,
    'UNIFIED_NOTES_ACCESSED',
    'case_notes',
    {
      caseId,
      noteType: noteType || 'all',
      notesCount: filteredNotes.length,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    caseId,
    notes: filteredNotes,
    total: filteredNotes.length,
  });
}));

// Get all notes for a case organized by note type (optimized for bulk loading)
router.get('/:caseId/notes-bulk', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.caseId;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;

  // Check if case exists and user has access
  const [caseData] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check access permissions
  const canAccess =
    (userRoles.includes('admin')) ||
    (userRoles.includes('agent')) ||
    (userRoles.includes('patient') && caseData.patientId === userId) ||
    (userRoles.includes('doctor') && await CaseDoctorService.isDoctorAssignedToCase(userId, caseId));

  if (!canAccess) {
    throw new AppError('Access denied', 403);
  }

  // Get all notes for this case
  const notes = await db
    .select({
      id: caseNotes.id,
      caseId: caseNotes.caseId,
      noteTypeId: caseNotes.noteTypeId,
      structuredContent: caseNotes.structuredContent,
      doctorId: caseNotes.doctorId,
      createdAt: caseNotes.createdAt,
      updatedAt: caseNotes.updatedAt,
      version: caseNotes.version,
      authorName: users.firstName,
      authorLastName: users.lastName,
      authorRole: users.role,
    })
    .from(caseNotes)
    .leftJoin(users, eq(caseNotes.doctorId, users.id))
    .where(eq(caseNotes.caseId, caseId))
    .orderBy(caseNotes.updatedAt);

  // Transform notes into organized structure by note type
  const notesByType: {[noteType: string]: any} = {};

  // Initialize all possible note types with empty content
  const allNoteTypes = Object.keys(NOTE_TYPE_IDS);
  const accessibleNoteTypes = filterNoteTypesByRole(allNoteTypes, userRoles);

  accessibleNoteTypes.forEach(noteType => {
    notesByType[noteType] = {
      caseId,
      noteType,
      content: '',
      author: null,
      createdAt: null,
      updatedAt: null,
      version: 0,
    };
  });

  // Fill in actual note data
  notes.forEach(note => {
    if (note.structuredContent && typeof note.structuredContent === 'object') {
      const structuredContent = note.structuredContent as any;

      // Extract content for each note type from the structured content
      Object.keys(structuredContent).forEach(noteType => {
        if (accessibleNoteTypes.includes(noteType) && structuredContent[noteType]) {
          notesByType[noteType] = {
            id: note.id,
            caseId,
            noteType,
            content: structuredContent[noteType],
            author: {
              id: note.doctorId,
              name: `${note.authorName || ''} ${note.authorLastName || ''}`.trim() || 'Unknown User',
              role: note.authorRole || 'patient',
            },
            createdAt: note.createdAt,
            updatedAt: note.updatedAt,
            version: note.version,
          };
        }
      });
    }
  });

  // Log audit event
  logAuditEvent(
    userId,
    'UNIFIED_NOTES_BULK_ACCESSED',
    'case_notes',
    {
      caseId,
      noteTypesCount: Object.keys(notesByType).length,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    caseId,
    notesByType,
    availableNoteTypes: accessibleNoteTypes,
    total: Object.keys(notesByType).length,
  });
}));

// Create or update a note for a specific type
router.post('/:caseId/notes/:noteType', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.caseId;
  const noteType = req.params.noteType;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;
  
  // Check if user can access this note type
  if (!canAccessNoteType(noteType, userRoles)) {
    throw new AppError(`Access denied: You cannot access ${noteType} notes`, 403);
  }

  const validatedData = createNoteSchema.parse({
    noteType,
    content: req.body.content || '',
  });

  // Check if case exists and user has access
  const [caseData] = await db
    .select()
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check permissions (patients can edit their own draft cases, doctors can edit assigned cases, admins can edit all)
  let canEdit = false;
  if (userRoles.includes('admin')) {
    canEdit = true;
  } else if (userRoles.includes('doctor')) {
    canEdit = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
  } else if (userRoles.includes('patient') && caseData.patientId === userId && caseData.status === 'draft') {
    canEdit = true;
  }

  if (!canEdit) {
    throw new AppError('Unauthorized to edit this case', 403);
  }

  // Use the clinical notes type ID for all notes
  const noteTypeId = CLINICAL_NOTES_TYPE_ID;

  // Check if a clinical note already exists for this case
  const [existingNote] = await db
    .select()
    .from(caseNotes)
    .where(
      and(
        eq(caseNotes.caseId, caseId),
        eq(caseNotes.noteTypeId, noteTypeId),
        eq(caseNotes.isActive, true)
      )
    )
    .orderBy(desc(caseNotes.updatedAt))
    .limit(1);

  let savedNote;

  if (existingNote) {
    // Update existing note - merge the new note type content with existing content
    const currentContent = (existingNote.structuredContent as any) || {};
    const updatedContent = {
      ...currentContent,
      [noteType]: validatedData.content, // Store each note type as a separate field
      [`${noteType}_lastUpdated`]: new Date().toISOString(),
      [`${noteType}_updatedBy`]: userId
    };

    const [updatedNote] = await db
      .update(caseNotes)
      .set({
        structuredContent: updatedContent,
        version: existingNote.version + 1,
        updatedAt: new Date(),
      })
      .where(eq(caseNotes.id, existingNote.id))
      .returning();
    
    savedNote = updatedNote;
  } else {
    // Create new note with the specific note type content
    const initialContent = {
      [noteType]: validatedData.content,
      [`${noteType}_lastUpdated`]: new Date().toISOString(),
      [`${noteType}_updatedBy`]: userId
    };

    const [newNote] = await db
      .insert(caseNotes)
      .values({
        caseId,
        doctorId: userId,
        noteTypeId,
        structuredContent: initialContent,
        version: 1,
        isActive: true,
      })
      .returning();
    
    savedNote = newNote;
  }

  // Log audit event
  logAuditEvent(
    userId,
    existingNote ? 'UNIFIED_NOTE_UPDATED' : 'UNIFIED_NOTE_CREATED',
    'case_notes',
    {
      caseId,
      noteId: savedNote.id,
      noteType,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );

  res.json({
    message: `${noteType} note ${existingNote ? 'updated' : 'created'} successfully`,
    note: {
      id: savedNote.id,
      caseId: savedNote.caseId,
      noteType,
      content: validatedData.content,
      version: savedNote.version,
      createdAt: savedNote.createdAt,
      updatedAt: savedNote.updatedAt,
    },
  });
}));

// Get a specific note by type
router.get('/:caseId/notes/:noteType', opalAuthMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const caseId = req.params.caseId;
  const noteType = req.params.noteType;
  const userId = (req as any).user.id;
  const userRoles = (req as any).user.roles;

  // Check case access permissions
  const [caseData] = await db
    .select({
      id: cases.id,
      patientId: cases.patientId,
    })
    .from(cases)
    .where(eq(cases.id, caseId))
    .limit(1);

  if (!caseData) {
    throw new AppError('Case not found', 404);
  }

  // Check access permissions
  let canAccess = 
    userRoles.includes('admin') ||
    userRoles.includes('agent') ||
    (userRoles.includes('patient') && caseData.patientId === userId);
    
  if (userRoles.includes('doctor') && !canAccess) {
    canAccess = await CaseDoctorService.isDoctorAssignedToCase(userId, caseId);
  }

  if (!canAccess) {
    throw new AppError('Unauthorized to access this case', 403);
  }

  // Use the clinical notes type ID
  const noteTypeId = CLINICAL_NOTES_TYPE_ID;

  // Get the clinical note for this case
  const [note] = await db
    .select({
      id: caseNotes.id,
      content: caseNotes.structuredContent,
      authorId: caseNotes.doctorId,
      authorName: users.firstName,
      authorLastName: users.lastName,
      authorRole: users.role,
      createdAt: caseNotes.createdAt,
      updatedAt: caseNotes.updatedAt,
      version: caseNotes.version,
    })
    .from(caseNotes)
    .leftJoin(users, eq(caseNotes.doctorId, users.id))
    .where(
      and(
        eq(caseNotes.caseId, caseId),
        eq(caseNotes.noteTypeId, noteTypeId),
        eq(caseNotes.isActive, true)
      )
    )
    .orderBy(desc(caseNotes.updatedAt))
    .limit(1);

  if (!note) {
    // Return empty note structure
    return res.json({
      caseId,
      noteType,
      content: '',
      author: null,
      createdAt: null,
      updatedAt: null,
      version: 0,
    });
  }

  // Extract content for the specific note type from structured content
  let content = '';
  if (note.content && typeof note.content === 'object') {
    const structuredContent = note.content as any;
    content = structuredContent[noteType] || '';
  }

  res.json({
    id: note.id,
    caseId,
    noteType,
    content,
    author: {
      id: note.authorId,
      name: `${note.authorName || ''} ${note.authorLastName || ''}`.trim() || 'Unknown User',
      role: note.authorRole || 'patient',
    },
    createdAt: note.createdAt,
    updatedAt: note.updatedAt,
    version: note.version,
  });
}));

export { router as unifiedNotesRoutes };