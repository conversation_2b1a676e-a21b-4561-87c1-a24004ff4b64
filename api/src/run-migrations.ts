import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import * as dotenv from 'dotenv';

import { logger } from '../utils/structuredLogger';
// Load environment variables
dotenv.config();

// Database connection string
const connectionString = process.env.DATABASE_URL || '********************************************/continuia';

// Create a Postgres client
const client = postgres(connectionString);

// Create a Drizzle instance
const db = drizzle(client);

// Run migrations
async function runMigrations() {
  logger.debug('Starting database migrations...');
  logger.debug(`Using connection string: ${connectionString}`);
  
  try {
    await migrate(db, { migrationsFolder: './src/db/migrations' });
    logger.debug('Migrations completed successfully!');
  } catch (error) {
    logger.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

runMigrations();
