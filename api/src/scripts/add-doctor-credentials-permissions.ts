import { db } from '../db/index.js';
import { permissions, roles, rolePermissions } from '../db/schema/permissions.js';
import { eq, and } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
async function addDoctorCredentialsPermissions() {
  try {
    logger.debug('Adding doctor credentials permissions...');

    // Define the permissions for doctor credentials
    const doctorCredentialsPermissions = [
      {
        name: 'doctor_credentials:read',
        description: 'View doctor credentials and profile information',
        resource: 'doctor_credentials',
        action: 'read',
        scope_filter: { scope: 'own' },
      },
      {
        name: 'doctor_credentials:create',
        description: 'Create new doctor credentials and profile information',
        resource: 'doctor_credentials',
        action: 'create',
        scope_filter: { scope: 'own' },
      },
      {
        name: 'doctor_credentials:update',
        description: 'Update doctor credentials and profile information',
        resource: 'doctor_credentials',
        action: 'update',
        scope_filter: { scope: 'own' },
      },
      {
        name: 'doctor_credentials:delete',
        description: 'Delete doctor credentials and profile information',
        resource: 'doctor_credentials',
        action: 'delete',
        scope_filter: { scope: 'own' },
      },
      {
        name: 'doctor_credentials:manage',
        description: 'Full management access to all doctor credentials (admin only)',
        resource: 'doctor_credentials',
        action: 'manage',
        scope_filter: { scope: 'global' },
      },
    ];

    // Insert permissions
    const insertedPermissions = [];
    for (const permission of doctorCredentialsPermissions) {
      try {
        const [existingPermission] = await db
          .select()
          .from(permissions)
          .where(eq(permissions.name, permission.name))
          .limit(1);

        if (!existingPermission) {
          const [newPermission] = await db
            .insert(permissions)
            .values(permission)
            .returning();
          insertedPermissions.push(newPermission);
          logger.debug(`✅ Created permission: ${permission.name}`);
        } else {
          logger.debug(`⚠️  Permission already exists: ${permission.name}`);
          insertedPermissions.push(existingPermission);
        }
      } catch (error) {
        logger.error('❌ Error creating permission ${permission.name}:', error);
      }
    }

    // Get roles
    const doctorRole = await db
      .select()
      .from(roles)
      .where(eq(roles.name, 'doctor'))
      .limit(1);

    const adminRole = await db
      .select()
      .from(roles)
      .where(eq(roles.name, 'admin'))
      .limit(1);

    // Assign permissions to doctor role (own scope permissions)
    if (doctorRole.length > 0) {
      const doctorPermissions = insertedPermissions.filter(p =>
        p.scope_filter && typeof p.scope_filter === 'object' &&
        (p.scope_filter as any).scope === 'own'
      );
      
      for (const permission of doctorPermissions) {
        try {
          const [existing] = await db
            .select()
            .from(rolePermissions)
            .where(
              and(
                eq(rolePermissions.roleId, doctorRole[0].id),
                eq(rolePermissions.permissionId, permission.id)
              )
            )
            .limit(1);

          if (!existing) {
            await db
              .insert(rolePermissions)
              .values({
                roleId: doctorRole[0].id,
                permissionId: permission.id,
              });
            logger.debug(`✅ Assigned permission ${permission.name} to doctor role`);
          } else {
            logger.debug(`⚠️  Permission ${permission.name} already assigned to doctor role`);
          }
        } catch (error) {
          logger.error('❌ Error assigning permission ${permission.name} to doctor role:', error);
        }
      }
    } else {
      logger.debug('⚠️  Doctor role not found');
    }

    // Assign all permissions to admin role
    if (adminRole.length > 0) {
      for (const permission of insertedPermissions) {
        try {
          const [existing] = await db
            .select()
            .from(rolePermissions)
            .where(
              and(
                eq(rolePermissions.roleId, adminRole[0].id),
                eq(rolePermissions.permissionId, permission.id)
              )
            )
            .limit(1);

          if (!existing) {
            await db
              .insert(rolePermissions)
              .values({
                roleId: adminRole[0].id,
                permissionId: permission.id,
              });
            logger.debug(`✅ Assigned permission ${permission.name} to admin role`);
          } else {
            logger.debug(`⚠️  Permission ${permission.name} already assigned to admin role`);
          }
        } catch (error) {
          logger.error('❌ Error assigning permission ${permission.name} to admin role:', error);
        }
      }
    } else {
      logger.debug('⚠️  Admin role not found');
    }

    logger.debug('✅ Doctor credentials permissions setup completed!');
  } catch (error) {
    logger.error('❌ Error setting up doctor credentials permissions:', error);
    throw error;
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  addDoctorCredentialsPermissions()
    .then(() => {
      logger.debug('Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Script failed:', error);
      process.exit(1);
    });
}

export { addDoctorCredentialsPermissions };