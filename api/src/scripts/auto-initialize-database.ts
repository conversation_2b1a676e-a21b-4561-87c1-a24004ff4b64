#!/usr/bin/env tsx

/**
 * Auto Database Initialization Script
 * This script automatically initializes the database on startup:
 * 1. Generates Drizzle migrations from schema
 * 2. Applies migrations to database
 * 3. Seeds initial data
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { logger } from '../utils/structuredLogger.js';
// import { seedCompleteDatabase } from './seed-complete-database.js';
import { db, sql } from '../db/index.js';

const execAsync = promisify(exec);

async function checkDatabaseExists(): Promise<boolean> {
  try {
    // Try to connect and run a simple query
    await sql`SELECT 1`;
    return true;
  } catch (error) {
    logger.warn('Database connection failed, will attempt to initialize');
    return false;
  }
}

async function checkTablesExist(): Promise<boolean> {
  try {
    const result = await sql`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'users'
    `;
    return result[0].count > 0;
  } catch (error) {
    return false;
  }
}

async function checkDataExists(): Promise<boolean> {
  try {
    const result = await sql`SELECT COUNT(*) as count FROM users`;
    return result[0].count > 0;
  } catch (error) {
    return false;
  }
}

async function generateMigrations(): Promise<void> {
  logger.info('🔧 Generating Drizzle migrations from schema...');
  try {
    const { stdout, stderr } = await execAsync('npm run db:generate', { 
      cwd: process.cwd(),
      timeout: 60000 
    });
    
    if (stderr && !stderr.includes('Warning')) {
      logger.warn('Migration generation warnings:', stderr);
    }
    
    logger.info('✅ Migrations generated successfully');
  } catch (error) {
    logger.error('❌ Failed to generate migrations:', error);
    throw error;
  }
}

async function applyMigrations(): Promise<void> {
  logger.info('📋 Applying migrations to database...');
  try {
    const { stdout, stderr } = await execAsync('npm run db:migrate', { 
      cwd: process.cwd(),
      timeout: 120000 
    });
    
    if (stderr && !stderr.includes('Warning')) {
      logger.warn('Migration application warnings:', stderr);
    }
    
    logger.info('✅ Migrations applied successfully');
  } catch (error) {
    logger.error('❌ Failed to apply migrations:', error);
    throw error;
  }
}

export async function autoInitializeDatabase(): Promise<void> {
  logger.info('🚀 Starting auto database initialization...');
  
  try {
    // Step 1: Check if database is accessible
    const dbExists = await checkDatabaseExists();
    if (!dbExists) {
      throw new Error('Database is not accessible. Please ensure PostgreSQL is running and DATABASE_URL is correct.');
    }
    
    // Step 2: Check if tables exist
    const tablesExist = await checkTablesExist();
    if (!tablesExist) {
      logger.info('📊 No tables found, initializing database schema...');
      
      // Generate and apply migrations
      await generateMigrations();
      await applyMigrations();
      
      logger.info('✅ Database schema initialized');
    } else {
      logger.info('📊 Database tables already exist, skipping schema initialization');
    }
    
    // Step 3: Check if initial data exists
    const dataExists = await checkDataExists();
    if (!dataExists) {
      logger.info('🌱 No initial data found, seeding database...');
      // await seedCompleteDatabase();
      logger.info('✅ Database seeded with initial data');
    } else {
      logger.info('🌱 Initial data already exists, skipping seeding');
    }
    
    logger.info('🎉 Database initialization complete!');
    
  } catch (error) {
    logger.error('❌ Database initialization failed:', error);
    throw error;
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  autoInitializeDatabase()
    .then(() => {
      logger.info('✅ Auto-initialization completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('❌ Auto-initialization failed:', error);
      process.exit(1);
    });
}
