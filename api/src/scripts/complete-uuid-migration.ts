import { sql } from 'drizzle-orm';
import { db } from '../db/index';
import { config } from 'dotenv';

import { logger } from '../utils/structuredLogger';
// Load environment variables
config();

async function completeUUIDMigration() {
  try {
    logger.debug('Completing UUID migration for Legal & Compliance tables...');
    
    // Step 1: Check current state and add missing UUID columns
    logger.debug('1. Adding missing UUID columns...');
    
    // Add UUID columns where they don't exist
    try {
      await db.execute(sql`
        ALTER TABLE legal_compliance_templates 
        ADD COLUMN IF NOT EXISTS id_uuid UUID DEFAULT uuid_generate_v4()
      `);
    } catch (e) {
      logger.debug('Templates id_uuid column already exists');
    }
    
    try {
      await db.execute(sql`
        ALTER TABLE legal_compliance_versions 
        ADD COLUMN IF NOT EXISTS id_uuid UUID DEFAULT uuid_generate_v4(),
        ADD COLUMN IF NOT EXISTS template_id_uuid UUID
      `);
    } catch (e) {
      logger.debug('Versions UUID columns already exist or partially exist');
    }
    
    try {
      await db.execute(sql`
        ALTER TABLE user_legal_agreements 
        ADD COLUMN IF NOT EXISTS id_uuid UUID DEFAULT uuid_generate_v4(),
        ADD COLUMN IF NOT EXISTS form_version_id_uuid UUID
      `);
    } catch (e) {
      logger.info('User agreements UUID columns already exist or partially exist', { requestId: 'context-needed' }, { data: undefined });
    }
    
    // Step 2: Populate UUID foreign key relationships
    logger.debug('2. Populating UUID foreign key relationships...');
    
    // Update template_id_uuid in versions table
    await db.execute(sql`
      UPDATE legal_compliance_versions 
      SET template_id_uuid = legal_compliance_templates.id_uuid
      FROM legal_compliance_templates
      WHERE legal_compliance_versions.template_id = legal_compliance_templates.id
      AND legal_compliance_versions.template_id_uuid IS NULL
    `);
    
    // Update form_version_id_uuid in user agreements table
    await db.execute(sql`
      UPDATE user_legal_agreements 
      SET form_version_id_uuid = legal_compliance_versions.id_uuid
      FROM legal_compliance_versions
      WHERE user_legal_agreements.form_version_id = legal_compliance_versions.id
      AND user_legal_agreements.form_version_id_uuid IS NULL
    `);
    
    // Step 3: Drop all existing foreign key constraints
    logger.debug('3. Dropping existing foreign key constraints...');
    
    const dropConstraints = [
      'legal_compliance_templates_created_by_fkey',
      'legal_compliance_versions_template_id_fkey', 
      'legal_compliance_versions_created_by_fkey',
      'user_consents_user_id_fkey',
      'user_consents_form_version_id_fkey'
    ];
    
    for (const constraintName of dropConstraints) {
      try {
        // Try to find which table has this constraint
        const constraintInfo = await db.execute(sql`
          SELECT table_name 
          FROM information_schema.table_constraints 
          WHERE constraint_name = ${constraintName}
          AND table_schema = 'public'
        `);
        
        if (constraintInfo.length > 0) {
          const tableName = (constraintInfo[0] as any).table_name;
          logger.debug(`Dropping constraint ${constraintName} from ${tableName}`);
          await db.execute(sql`
            ALTER TABLE ${sql.identifier(tableName)} 
            DROP CONSTRAINT ${sql.identifier(constraintName)}
          `);
        }
      } catch (e) {
        logger.debug('Debug output', undefined, { data: `Constraint ${constraintName} doesn't exist or already dropped` });
      }
    }
    
    // Step 4: Drop primary key constraints
    logger.debug('4. Dropping primary key constraints...');
    
    const tables = ['legal_compliance_templates', 'legal_compliance_versions', 'user_legal_agreements'];
    for (const table of tables) {
      try {
        await db.execute(sql`
          ALTER TABLE ${sql.identifier(table)} 
          DROP CONSTRAINT ${sql.identifier(table + '_pkey')}
        `);
        logger.debug(`Dropped primary key for ${table}`);
      } catch (e) {
        logger.debug('Debug output', undefined, { data: `Primary key for ${table} doesn't exist or already dropped` });
      }
    }
    
    // Step 5: Drop old integer ID columns and rename UUID columns
    logger.debug('5. Replacing integer IDs with UUIDs...');
    
    // Templates table
    await db.execute(sql`
      ALTER TABLE legal_compliance_templates 
      DROP COLUMN IF EXISTS id CASCADE
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_templates 
      ALTER COLUMN id_uuid SET NOT NULL,
      ALTER COLUMN id_uuid DROP DEFAULT
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_templates 
      RENAME COLUMN id_uuid TO id
    `);
    
    // Versions table
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      DROP COLUMN IF EXISTS id CASCADE
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      DROP COLUMN IF EXISTS template_id CASCADE
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      ALTER COLUMN id_uuid SET NOT NULL
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      ALTER COLUMN id_uuid DROP DEFAULT
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      ALTER COLUMN template_id_uuid SET NOT NULL
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      RENAME COLUMN id_uuid TO id
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      RENAME COLUMN template_id_uuid TO template_id
    `);
    
    // User agreements table
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      DROP COLUMN IF EXISTS id CASCADE
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      DROP COLUMN IF EXISTS form_version_id CASCADE
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      ALTER COLUMN id_uuid SET NOT NULL
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      ALTER COLUMN id_uuid DROP DEFAULT
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      ALTER COLUMN form_version_id_uuid SET NOT NULL
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      RENAME COLUMN id_uuid TO id
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      RENAME COLUMN form_version_id_uuid TO document_version_id
    `);
    
    // Step 6: Add primary key constraints
    logger.debug('6. Adding primary key constraints...');
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_templates 
      ADD CONSTRAINT legal_compliance_templates_pkey PRIMARY KEY (id)
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      ADD CONSTRAINT legal_compliance_versions_pkey PRIMARY KEY (id)
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      ADD CONSTRAINT user_legal_agreements_pkey PRIMARY KEY (id)
    `);
    
    // Step 7: Add foreign key constraints
    logger.debug('7. Adding new foreign key constraints...');
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      ADD CONSTRAINT legal_compliance_versions_template_id_fkey 
      FOREIGN KEY (template_id) REFERENCES legal_compliance_templates(id)
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      ADD CONSTRAINT user_legal_agreements_document_version_id_fkey 
      FOREIGN KEY (document_version_id) REFERENCES legal_compliance_versions(id)
    `);
    
    // Step 8: Add indexes for performance
    logger.debug('8. Adding indexes...');
    
    try {
      await db.execute(sql`
        CREATE INDEX idx_legal_compliance_versions_template_id 
        ON legal_compliance_versions(template_id)
      `);
    } catch (e) {
      logger.debug('Index already exists');
    }
    
    try {
      await db.execute(sql`
        CREATE INDEX idx_legal_compliance_versions_is_active 
        ON legal_compliance_versions(is_active)
      `);
    } catch (e) {
      logger.debug('Index already exists');
    }
    
    try {
      await db.execute(sql`
        CREATE INDEX idx_user_legal_agreements_user_id 
        ON user_legal_agreements(user_id)
      `);
    } catch (e) {
      logger.debug('Index already exists');
    }
    
    try {
      await db.execute(sql`
        CREATE INDEX idx_user_legal_agreements_document_version_id 
        ON user_legal_agreements(document_version_id)
      `);
    } catch (e) {
      logger.debug('Index already exists');
    }
    
    logger.debug('✅ UUID migration completed successfully!');
    logger.debug('Security improvements:');
    logger.debug('Debug output', undefined, { data: '  - Template IDs are now UUIDs (prevents enumeration attacks });');
    logger.debug('Debug output', undefined, { data: '  - Version IDs are now UUIDs (prevents enumeration attacks });');
    logger.info('  - User agreement IDs are now UUIDs (prevents enumeration attacks)', { requestId: 'context-needed' }, { data: undefined });
    logger.debug('  - All foreign key relationships preserved');
    
    // Verify the migration
    logger.debug('\n🔍 Verifying migration...');
    const verification = await db.execute(sql`
      SELECT 
        t.table_name,
        c.column_name,
        c.data_type
      FROM information_schema.tables t
      JOIN information_schema.columns c ON t.table_name = c.table_name
      WHERE t.table_schema = 'public' 
      AND t.table_name IN ('legal_compliance_templates', 'legal_compliance_versions', 'user_legal_agreements')
      AND c.column_name IN ('id', 'template_id', 'document_version_id')
      ORDER BY t.table_name, c.column_name
    `);
    
    logger.debug('Column types after migration:');
    for (const row of verification) {
      const tableName = (row as any).table_name;
      const columnName = (row as any).column_name;
      const dataType = (row as any).data_type;
      logger.debug(`  ${tableName}.${columnName}: ${dataType}`);
    }
    
    // Check data count
    const counts = await db.execute(sql`
      SELECT 
        (SELECT COUNT(*) FROM legal_compliance_templates) as templates,
        (SELECT COUNT(*) FROM legal_compliance_versions) as versions,
        (SELECT COUNT(*) FROM user_legal_agreements) as agreements
    `);
    
    const countsRow = counts[0] as any;
    logger.debug('\nData preserved:');
    logger.debug(`  Templates: ${countsRow.templates}`);
    logger.debug(`  Versions: ${countsRow.versions}`);
    logger.info('  User Agreements: ${countsRow.agreements}', { requestId: 'context-needed' }, { data: undefined });
    
  } catch (error) {
    logger.error('❌ UUID migration failed:', error);
    throw error;
  }
}

async function main() {
  try {
    await completeUUIDMigration();
    logger.debug('\n🎉 UUID migration completed successfully!');
  } catch (error) {
    logger.error('\n💥 UUID migration failed:', error);
    process.exit(1);
  } finally {
    logger.debug('Migration process completed.');
  }
}

// Run the migration
main();

export { completeUUIDMigration };
