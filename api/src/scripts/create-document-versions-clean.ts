import { db } from '../db/index.js';
import { legalComplianceTemplates, legalComplianceVersions } from '../db/schema/legal-compliance.js';
import { users } from '../db/schema/users.js';
import { eq, desc } from 'drizzle-orm';
import { randomUUID } from 'crypto';

import { logger } from '../utils/structuredLogger';
// Generate version content based on document title and version number
function generateVersionContent(title: string, versionNumber: number) {
  const baseContent = {
    2: {
      content: `# ${title} (Version 2)

## Updated Content

This is an updated version of the ${title} document.

### Key Changes in Version 2:
- Enhanced clarity in language
- Updated regulatory compliance requirements
- Improved user experience considerations
- Additional safety and privacy provisions

### Effective Date
This version is effective immediately upon approval.

---

**Document Version:** 2.0
**Last Updated:** ${new Date().toISOString().split('T')[0]}
**Status:** Active`,
      changesSummary: 'Version 2: Enhanced clarity, updated compliance requirements, improved UX, additional safety provisions'
    },
    3: {
      content: `# ${title} (Version 3)

## Latest Updates

This is the most recent version of the ${title} document with comprehensive updates.

### Major Changes in Version 3:
- Comprehensive legal review and updates
- Enhanced data protection measures
- Improved accessibility compliance
- Updated contact information and procedures
- Additional user rights and protections

### Regulatory Compliance
- Updated to meet latest healthcare regulations
- Enhanced HIPAA compliance measures
- Improved data breach notification procedures
- Updated consent mechanisms

### User Experience Improvements
- Simplified language for better understanding
- Clear action items and requirements
- Enhanced visual formatting
- Mobile-friendly presentation

### Support and Contact
For questions about this document, please contact our legal compliance team.

---

**Document Version:** 3.0
**Last Updated:** ${new Date().toISOString().split('T')[0]}
**Status:** Active
**Review Date:** ${new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}`,
      changesSummary: 'Version 3: Comprehensive legal review, enhanced data protection, accessibility compliance, improved UX, additional user rights'
    }
  };

  return baseContent[versionNumber as keyof typeof baseContent] || baseContent[2];
}

async function createDocumentVersions() {
  logger.info('🚀 Starting document version creation...', { requestId: 'context-needed' }, { data: undefined });

  try {
    // Get admin user ID for audit trail
    const adminUser = await db
      .select({ id: users.id })
      .from(users)
      .where(eq(users.role, 'admin'))
      .limit(1);
    
    if (adminUser.length === 0) {
      throw new Error('No admin user found in database');
    }
    
    const adminUserId = adminUser[0].id;
    logger.info('Using admin user ID: ${adminUserId}', { requestId: 'context-needed' }, { data: undefined });

    // Get all templates
    const templates = await db
      .select()
      .from(legalComplianceTemplates)
      .orderBy(desc(legalComplianceTemplates.updatedAt));

    logger.debug(`📄 Found ${templates.length} templates to process`);

    let createdVersions = 0;

    // Create multiple versions for each template
    for (const template of templates) {
      logger.debug(`\n📝 Processing: ${template.title}`);
      
      // Get current versions for this template
      const existingVersions = await db
        .select()
        .from(legalComplianceVersions)
        .where(eq(legalComplianceVersions.templateId, template.id))
        .orderBy(desc(legalComplianceVersions.version));
      
      const maxVersion = existingVersions.length > 0 ? Math.max(...existingVersions.map(v => v.version)) : 0;
      logger.debug('Debug output', undefined, { data: `   Current versions: ${existingVersions.length} (max version: ${maxVersion} });`);
      
      // Create 2 additional versions (for a total of 3 versions per template)
      const versionsToCreate = 3 - existingVersions.length;
      
      if (versionsToCreate <= 0) {
        logger.debug(`   ✅ Already has ${existingVersions.length} versions, skipping`);
        continue;
      }
      
      for (let i = 1; i <= versionsToCreate; i++) {
        const newVersionNumber = maxVersion + i;
        
        try {
          const versionData = generateVersionContent(template.title, newVersionNumber);
          
          await db.insert(legalComplianceVersions).values({
            id: randomUUID(),
            templateId: template.id,
            version: newVersionNumber,
            content: versionData.content,
            notes: versionData.changesSummary,
            createdBy: adminUserId,
            createdAt: new Date()
          });
          
          logger.debug(`   ✅ Created version ${newVersionNumber}: ${versionData.changesSummary}`);
          createdVersions++;
        } catch (error) {
          logger.error('   ❌ Failed to create version ${newVersionNumber}:', error);
        }
      }
    }

    logger.info('\n🎉 Document version creation completed! Created ${createdVersions} new versions.', { requestId: 'context-needed' }, { data: undefined });
    
  } catch (error) {
    logger.error('❌ Error creating document versions:', error);
    process.exit(1);
  }
}

// Run the script
createDocumentVersions()
  .then(() => {
    logger.debug('\n✨ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('💥 Script failed:', error);
    process.exit(1);
  });
