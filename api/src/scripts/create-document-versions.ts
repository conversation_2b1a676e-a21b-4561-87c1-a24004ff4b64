import { db } from '../db/index.js';
import { legalComplianceTemplates, legalComplianceVersions } from '../db/schema/legal-compliance.js';
import { users } from '../db/schema/users.js';
import { eq, desc } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
// Sample version content variations for different document types
const versionVariations = {
  'Patient Consent Form': [
    {
      content: `# 📋 Continuia Patient Consent Form (Version 2)

## Patient Information

| Field | Information |
|-------|-------------|
| **Patient Name** | _________________________ |
| **Date of Birth** | _________________________ |
| **Medical Record Number** | _________________________ |
| **Date** | _________________________ |

---

## 🏥 Consent for Treatment

I, the undersigned patient, acknowledge that I have been informed of the nature of my condition and the proposed treatment plan. I understand that:

### Treatment Authorization
- I authorize Continuia and its affiliated healthcare providers to provide medical care
- I understand the risks, benefits, and alternatives to the proposed treatment
- I have had the opportunity to ask questions, and all my questions have been answered

### AI-Assisted Care (Updated)
- I understand that Continuia utilizes advanced AI technology to assist in care coordination
- AI recommendations are reviewed by licensed healthcare professionals
- Final medical decisions are made by qualified physicians
- **NEW:** I consent to AI analysis of my medical records for treatment optimization

### Information Sharing (Enhanced)
- I consent to the sharing of my medical information among my care team
- Information will be shared in accordance with HIPAA regulations
- I understand my rights regarding my medical information
- **NEW:** I authorize secure data sharing with partner healthcare institutions

---

## ✅ Patient Acknowledgment

**I acknowledge that:**
- [ ] I have read and understand this consent form
- [ ] All my questions have been answered to my satisfaction
- [ ] I voluntarily consent to the proposed treatment
- [ ] I understand I may withdraw this consent at any time
- [ ] **NEW:** I understand the enhanced AI capabilities and data sharing provisions

**Patient Signature:** ___________________________ **Date:** ___________

**Witness Signature:** ___________________________ **Date:** ___________`,
      notes: 'Added enhanced AI capabilities section and expanded data sharing provisions'
    },
    {
      content: `# 📋 Continuia Patient Consent Form (Version 3)

## Patient Information

| Field | Information |
|-------|-------------|
| **Patient Name** | _________________________ |
| **Date of Birth** | _________________________ |
| **Medical Record Number** | _________________________ |
| **Date** | _________________________ |
| **Emergency Contact** | _________________________ |

---

## 🏥 Consent for Treatment

I, the undersigned patient, acknowledge that I have been informed of the nature of my condition and the proposed treatment plan. I understand that:

### Treatment Authorization
- I authorize Continuia and its affiliated healthcare providers to provide medical care
- I understand the risks, benefits, and alternatives to the proposed treatment
- I have had the opportunity to ask questions, and all my questions have been answered
- **NEW:** I consent to telemedicine consultations when appropriate

### AI-Assisted Care (Latest)
- I understand that Continuia utilizes state-of-the-art AI technology to assist in care coordination
- AI recommendations are reviewed by licensed healthcare professionals
- Final medical decisions are made by qualified physicians
- I consent to AI analysis of my medical records for treatment optimization
- **NEW:** I understand AI may be used for predictive health analytics

### Information Sharing (Comprehensive)
- I consent to the sharing of my medical information among my care team
- Information will be shared in accordance with HIPAA regulations
- I understand my rights regarding my medical information
- I authorize secure data sharing with partner healthcare institutions
- **NEW:** I consent to anonymized data use for medical research and quality improvement

### Privacy and Security
- **NEW:** I understand that all data is encrypted and stored securely
- **NEW:** I have the right to request data deletion subject to legal requirements
- **NEW:** I will be notified of any data breaches affecting my information

---

## ✅ Patient Acknowledgment

**I acknowledge that:**
- [ ] I have read and understand this consent form
- [ ] All my questions have been answered to my satisfaction
- [ ] I voluntarily consent to the proposed treatment
- [ ] I understand I may withdraw this consent at any time
- [ ] **NEW:** I consent to telemedicine and research data usage

**Patient Signature:** ___________________________ **Date:** ___________

**Witness Signature:** ___________________________ **Date:** ___________

**Healthcare Provider:** ___________________________ **Date:** ___________`,
      notes: 'Major update: Added telemedicine consent, predictive analytics, research data usage, privacy section, and healthcare provider signature'
    }
  ],
  'AI Output Disclaimer': [
    {
      content: `# ⚠️ AI Output Disclaimer (Version 2)

## Important Notice

### Regulatory Compliance
- Updated to meet latest healthcare regulations
- Enhanced HIPAA compliance measures
- Improved data breach notification procedures
- Updated consent mechanisms
`,
      changesSummary: 'Version 2: Enhanced regulatory compliance and updated procedures'
    }
  ]
};

function generateVersionContent(templateTitle: string, versionNumber: number) {
  const baseContent = versionVariations[templateTitle as keyof typeof versionVariations];

  if (!baseContent) {
    throw new Error(`No version content found for template: ${templateTitle}`);
  }

  return baseContent[versionNumber - 1] || baseContent[0];
}

async function createDocumentVersions() {
  logger.info('🚀 Starting document version creation...', { requestId: 'context-needed' }, { data: undefined });

  try {
    // Get admin user ID for audit trail
    const adminUser = await db
      .select({ id: users.id })
      .from(users)
      .where(eq(users.role, 'admin'))
      .limit(1);
    
    if (adminUser.length === 0) {
      throw new Error('No admin user found in database');
    }
    
    const adminUserId = adminUser[0].id;
    logger.info('Using admin user ID: ${adminUserId}', { requestId: 'context-needed' }, { data: undefined });

    // Get all templates
    const templates = await db
      .select()
      .from(legalComplianceTemplates)
      .orderBy(desc(legalComplianceTemplates.updatedAt));

    logger.debug(`📄 Found ${templates.length} templates to process`);

    let createdVersions = 0;

    // Create multiple versions for each template
    for (const template of templates) {
      logger.debug(`\n📝 Processing: ${template.title}`);
      
      // Get current versions for this template
      const existingVersions = await db
        .select()
        .from(legalComplianceVersions)
        .where(eq(legalComplianceVersions.templateId, template.id))
        .orderBy(desc(legalComplianceVersions.version));
      
      const maxVersion = existingVersions.length > 0 ? Math.max(...existingVersions.map(v => v.version)) : 0;
      logger.debug('Debug output', undefined, { data: `   Current versions: ${existingVersions.length} (max version: ${maxVersion} });`);
      
      // Create 2 additional versions (for a total of 3 versions per template)
      const versionsToCreate = 3 - existingVersions.length;
      
      if (versionsToCreate <= 0) {
        logger.debug(`   ✅ Already has ${existingVersions.length} versions, skipping`);
        continue;
      }
      
      for (let i = 1; i <= versionsToCreate; i++) {
        const newVersionNumber = maxVersion + i;
        
        try {
          const versionData = generateVersionContent(template.title, newVersionNumber);
          const changesSummary = ('changesSummary' in versionData ? versionData.changesSummary : versionData.notes) || `Version ${newVersionNumber}: Enhanced content and improved user experience`;
          const notes = `Generated version ${newVersionNumber} based on: ${('changesSummary' in versionData ? versionData.changesSummary : versionData.notes)}`;
          
          await db.insert(legalComplianceVersions).values({
            templateId: template.id,
            version: newVersionNumber,
            content: versionData.content,
            notes: notes,
            createdBy: adminUserId,
            createdAt: new Date()
          });
          
          logger.debug(`   ✅ Created version ${newVersionNumber}: ${changesSummary}`);
          createdVersions++;
        } catch (error) {
          logger.error('   ❌ Failed to create version ${newVersionNumber}:', error);
        }
      }
    }

    logger.info('\n🎉 Document version creation completed! Created ${createdVersions} new versions.', { requestId: 'context-needed' }, { data: undefined });
    
  } catch (error) {
    logger.error('❌ Error creating document versions:', error);
    process.exit(1);
  }
}

// Run the script
createDocumentVersions()
  .then(() => {
    logger.debug('\n✨ Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('💥 Script failed:', error);
    process.exit(1);
  });
