import { logger } from '../utils/structuredLogger';

const { drizzle } = require('drizzle-orm/postgres-js')
const postgres = require('postgres')
const { cases } = require('../db/schema')
const { eq } = require('drizzle-orm')

const connectionString = process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/continuia'
const client = postgres(connectionString)
const db = drizzle(client)

async function enhanceCaseDetails() {
  const caseId = '2bfb4ffe-8ed4-4ae7-b920-751fbf091471'
  
  logger.debug('Enhancing case with comprehensive medical details...');
  
  const enhancedCaseData = {
    title: 'Chronic Chest Pain and Shortness of Breath - Seeking Cardiac Second Opinion',
    description: `I am a 45-year-old software engineer experiencing persistent chest pain and shortness of breath for the past 6 months. My primary care physician initially thought it was stress-related, but symptoms have worsened despite lifestyle changes. I'm seeking a second opinion from a cardiologist as my family has a history of heart disease.

**Chief Complaint:**
Intermittent chest pain (7/10 severity) with shortness of breath, especially during physical activity or stress. Episodes last 15-30 minutes and occur 3-4 times per week.

**Current Symptoms:**
- Sharp, stabbing chest pain radiating to left arm
- Shortness of breath during mild exertion (climbing stairs)
- Occasional heart palpitations
- Fatigue and decreased exercise tolerance
- Night sweats (2-3 times per week)
- Mild dizziness when standing quickly

**Timeline:**
- 6 months ago: First episode of chest pain during work presentation
- 4 months ago: Symptoms became more frequent
- 2 months ago: Added shortness of breath and fatigue
- 1 month ago: Night sweats and palpitations began`,

    symptoms: `**Primary Symptoms:**
• Chest pain (7/10 severity) - sharp, stabbing, radiates to left arm
• Shortness of breath on exertion (climbing 1 flight of stairs)
• Heart palpitations - irregular, racing sensation
• Fatigue - significant decrease in energy levels
• Exercise intolerance - can't maintain previous activity level

**Secondary Symptoms:**
• Night sweats - 2-3 times per week, soaking through shirt
• Dizziness when standing quickly (orthostatic)
• Occasional nausea during chest pain episodes
• Sleep disturbances due to discomfort
• Anxiety about symptoms (understandable given family history)

**Symptom Triggers:**
• Physical exertion (stairs, walking uphill)
• Emotional stress (work deadlines, presentations)
• Cold weather exposure
• Large meals
• Caffeine intake (>2 cups coffee)

**Pain Characteristics:**
• Location: Central chest, radiating to left arm and jaw
• Quality: Sharp, stabbing, sometimes crushing
• Duration: 15-30 minutes per episode
• Frequency: 3-4 episodes per week
• Relief: Rest, sitting down, deep breathing exercises`,

    medical_history: `**Personal Medical History:**
• Hypertension (diagnosed 2019) - well controlled on medication
• Type 2 Diabetes (diagnosed 2020) - HbA1c 6.8% (last check 3 months ago)
• Hyperlipidemia (diagnosed 2021) - total cholesterol 240 mg/dL
• Gastroesophageal reflux disease (GERD) - managed with PPI
• Seasonal allergies - managed with antihistamines
• No previous cardiac procedures or hospitalizations

**Family Medical History:**
• Father: Myocardial infarction at age 52, bypass surgery at 55, deceased at 68 (heart failure)
• Mother: Hypertension, stroke at age 71 (recovered well)
• Paternal grandfather: Died of heart attack at age 49
• Maternal grandmother: Diabetes, hypertension
• Brother (age 42): Recently diagnosed with hypertension
• Sister (age 38): Healthy, no known conditions

**Social History:**
• Former smoker: 1 pack/day for 15 years, quit 5 years ago
• Alcohol: 2-3 drinks per week (wine with dinner)
• Exercise: Previously ran 3 miles, 3x/week - now limited due to symptoms
• Diet: Attempting heart-healthy diet since symptoms began
• Stress level: High (demanding job in tech industry)
• Sleep: 6-7 hours/night, often interrupted by symptoms

**Surgical History:**
• Appendectomy (age 16)
• Wisdom teeth extraction (age 22)
• No cardiac or major surgeries

**Allergies:**
• Penicillin - rash and hives
• Shellfish - mild GI upset
• No known drug allergies to current medications`,

    current_medications: `**Current Medications:**
1. **Lisinopril 10mg** - Once daily (morning) for hypertension
   - Started: 2019, well tolerated
   - Last BP reading: 138/82 mmHg (goal <130/80)

2. **Metformin 1000mg** - Twice daily with meals for diabetes
   - Started: 2020, no GI side effects
   - Last HbA1c: 6.8% (3 months ago)

3. **Atorvastatin 40mg** - Once daily (evening) for hyperlipidemia
   - Started: 2021, no muscle pain or weakness
   - Last lipid panel: Total cholesterol 240, LDL 155, HDL 38, TG 235

4. **Omeprazole 20mg** - Once daily (morning) for GERD
   - Started: 2022, effective for acid reflux symptoms
   - Taking 30 minutes before breakfast

5. **Aspirin 81mg** - Once daily (evening) for cardiovascular protection
   - Started: 6 months ago when symptoms began
   - No GI bleeding or upset

6. **Loratadine 10mg** - As needed for seasonal allergies
   - Used spring/fall seasons, effective

**Recent Medication Changes:**
• Added low-dose aspirin 6 months ago
• Increased Lisinopril from 5mg to 10mg (4 months ago)
• Temporarily tried sublingual nitroglycerin (prescribed by PCP) - provided some relief

**Supplements:**
• Multivitamin - daily
• Vitamin D3 2000 IU - daily (deficiency noted last year)
• Fish oil 1000mg - twice daily (started 3 months ago)
• Magnesium 400mg - daily (for muscle cramps)

**Medication Adherence:**
• Excellent compliance with all prescribed medications
• Uses pill organizer and smartphone reminders
• Regular pharmacy refills, no missed doses`,

    urgency_level: 'high',
    specialty_required: 'Cardiology',
    status: 'assigned'
  }

  try {
    const result = await db
      .update(cases)
      .set({
        ...enhancedCaseData,
        updated_at: new Date()
      })
      .where(eq(cases.id, caseId))
      .returning()

    logger.debug('✅ Case enhanced successfully!');
    logger.debug('Debug output', undefined, { data: 'Updated case:', result[0] });
    
    // Also add some additional context that would be helpful
    logger.debug('\n📋 Additional Information Added:');
    logger.debug('- Comprehensive symptom timeline and triggers');
    logger.debug('Debug output', undefined, { data: '- Detailed family cardiac history (father had MI at 52 });')
    logger.debug('- Complete medication list with dosages and adherence');
    logger.debug('- Social history including smoking cessation');
    logger.debug('- Specific pain characteristics and radiation patterns');
    logger.info('- Exercise tolerance decline documentation', { requestId: 'context-needed' }, { data: undefined });
    
  } catch (error) {
    logger.error('❌ Error enhancing case:', error);
  } finally {
    await client.end()
  }
}

enhanceCaseDetails()
