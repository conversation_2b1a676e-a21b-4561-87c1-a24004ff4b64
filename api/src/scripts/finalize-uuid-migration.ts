import { sql } from 'drizzle-orm';
import { db } from '../db/index';
import { config } from 'dotenv';

import { logger } from '../utils/structuredLogger';
// Load environment variables
config();

async function finalizeUUIDMigration() {
  try {
    logger.debug('Finalizing UUID migration for Legal & Compliance tables...');
    
    // Step 1: Check current state and complete the migration
    logger.debug('1. Completing UUID column setup...');
    
    // Templates table - rename id_uuid to id
    try {
      await db.execute(sql`
        ALTER TABLE legal_compliance_templates 
        RENAME COLUMN id_uuid TO id
      `);
      logger.debug('Renamed templates id_uuid to id');
    } catch (e) {
      logger.debug('Templates id column already correct');
    }
    
    // Versions table - rename UUID columns to final names
    try {
      await db.execute(sql`
        ALTER TABLE legal_compliance_versions 
        RENAME COLUMN id_uuid TO id
      `);
      logger.debug('Renamed versions id_uuid to id');
    } catch (e) {
      logger.debug('Versions id column already correct');
    }
    
    try {
      await db.execute(sql`
        ALTER TABLE legal_compliance_versions 
        RENAME COLUMN template_id_uuid TO template_id
      `);
      logger.debug('Renamed versions template_id_uuid to template_id');
    } catch (e) {
      logger.debug('Versions template_id column already correct');
    }
    
    // User agreements table - rename UUID columns to final names
    try {
      await db.execute(sql`
        ALTER TABLE user_legal_agreements 
        RENAME COLUMN id_uuid TO id
      `);
      logger.info('Renamed user_agreements id_uuid to id', { requestId: 'context-needed' }, { data: undefined });
    } catch (e) {
      logger.info('User agreements id column already correct', { requestId: 'context-needed' }, { data: undefined });
    }
    
    try {
      await db.execute(sql`
        ALTER TABLE user_legal_agreements 
        RENAME COLUMN form_version_id_uuid TO document_version_id
      `);
      logger.info('Renamed user_agreements form_version_id_uuid to document_version_id', { requestId: 'context-needed' }, { data: undefined });
    } catch (e) {
      logger.info('User agreements document_version_id column already correct', { requestId: 'context-needed' }, { data: undefined });
    }
    
    // Step 2: Add primary key constraints
    logger.debug('2. Adding primary key constraints...');
    
    try {
      await db.execute(sql`
        ALTER TABLE legal_compliance_templates 
        ADD CONSTRAINT legal_compliance_templates_pkey PRIMARY KEY (id)
      `);
      logger.debug('Added primary key to templates');
    } catch (e) {
      logger.debug('Templates primary key already exists');
    }
    
    try {
      await db.execute(sql`
        ALTER TABLE legal_compliance_versions 
        ADD CONSTRAINT legal_compliance_versions_pkey PRIMARY KEY (id)
      `);
      logger.debug('Added primary key to versions');
    } catch (e) {
      logger.debug('Versions primary key already exists');
    }
    
    try {
      await db.execute(sql`
        ALTER TABLE user_legal_agreements 
        ADD CONSTRAINT user_legal_agreements_pkey PRIMARY KEY (id)
      `);
      logger.info('Added primary key to user agreements', { requestId: 'context-needed' }, { data: undefined });
    } catch (e) {
      logger.info('User agreements primary key already exists', { requestId: 'context-needed' }, { data: undefined });
    }
    
    // Step 3: Add foreign key constraints
    logger.debug('3. Adding foreign key constraints...');
    
    try {
      await db.execute(sql`
        ALTER TABLE legal_compliance_versions 
        ADD CONSTRAINT legal_compliance_versions_template_id_fkey 
        FOREIGN KEY (template_id) REFERENCES legal_compliance_templates(id)
      `);
      logger.debug('Added foreign key from versions to templates');
    } catch (e) {
      logger.debug('Versions to templates foreign key already exists');
    }
    
    try {
      await db.execute(sql`
        ALTER TABLE user_legal_agreements 
        ADD CONSTRAINT user_legal_agreements_document_version_id_fkey 
        FOREIGN KEY (document_version_id) REFERENCES legal_compliance_versions(id)
      `);
      logger.info('Added foreign key from user agreements to versions', { requestId: 'context-needed' }, { data: undefined });
    } catch (e) {
      logger.info('User agreements to versions foreign key already exists', { requestId: 'context-needed' }, { data: undefined });
    }
    
    // Step 4: Add indexes for performance
    logger.debug('4. Adding performance indexes...');
    
    try {
      await db.execute(sql`
        CREATE INDEX idx_legal_compliance_versions_template_id 
        ON legal_compliance_versions(template_id)
      `);
      logger.debug('Added template_id index');
    } catch (e) {
      logger.debug('Template_id index already exists');
    }
    
    try {
      await db.execute(sql`
        CREATE INDEX idx_user_legal_agreements_document_version_id 
        ON user_legal_agreements(document_version_id)
      `);
      logger.info('Added document_version_id index', { requestId: 'context-needed' }, { data: undefined });
    } catch (e) {
      logger.info('Document_version_id index already exists', { requestId: 'context-needed' }, { data: undefined });
    }
    
    logger.debug('✅ UUID migration finalized successfully!');
    logger.debug('Security improvements completed:');
    logger.debug('Debug output', undefined, { data: '  - Template IDs are now UUIDs (prevents enumeration attacks });');
    logger.debug('Debug output', undefined, { data: '  - Version IDs are now UUIDs (prevents enumeration attacks });');
    logger.info('  - User agreement IDs are now UUIDs (prevents enumeration attacks)', { requestId: 'context-needed' }, { data: undefined });
    logger.debug('  - All foreign key relationships preserved');
    
    // Verify the migration
    logger.debug('\n🔍 Verifying final migration state...');
    const verification = await db.execute(sql`
      SELECT 
        t.table_name,
        c.column_name,
        c.data_type,
        c.is_nullable
      FROM information_schema.tables t
      JOIN information_schema.columns c ON t.table_name = c.table_name
      WHERE t.table_schema = 'public' 
      AND t.table_name IN ('legal_compliance_templates', 'legal_compliance_versions', 'user_legal_agreements')
      AND c.column_name IN ('id', 'template_id', 'document_version_id')
      ORDER BY t.table_name, c.column_name
    `);
    
    logger.debug('Final column types:');
    for (const row of verification) {
      const tableName = (row as any).table_name;
      const columnName = (row as any).column_name;
      const dataType = (row as any).data_type;
      const nullable = (row as any).is_nullable;
      logger.debug('Debug output', undefined, { data: `  ${tableName}.${columnName}: ${dataType} (nullable: ${nullable} });`);
    }
    
    // Check data count
    const counts = await db.execute(sql`
      SELECT 
        (SELECT COUNT(*) FROM legal_compliance_templates) as templates,
        (SELECT COUNT(*) FROM legal_compliance_versions) as versions,
        (SELECT COUNT(*) FROM user_legal_agreements) as agreements
    `);
    
    const countsRow = counts[0] as any;
    logger.debug('\nData preserved:');
    logger.debug(`  Templates: ${countsRow.templates}`);
    logger.debug(`  Versions: ${countsRow.versions}`);
    logger.info('  User Agreements: ${countsRow.agreements}', { requestId: 'context-needed' }, { data: undefined });
    
    // Check constraints
    const constraints = await db.execute(sql`
      SELECT constraint_name, table_name, constraint_type
      FROM information_schema.table_constraints 
      WHERE table_name IN ('legal_compliance_templates', 'legal_compliance_versions', 'user_legal_agreements')
      AND constraint_type IN ('PRIMARY KEY', 'FOREIGN KEY')
      ORDER BY table_name, constraint_type
    `);
    
    logger.debug('\nConstraints:');
    for (const constraint of constraints) {
      const name = (constraint as any).constraint_name;
      const table = (constraint as any).table_name;
      const type = (constraint as any).constraint_type;
      logger.debug('Debug output', undefined, { data: `  ${table}: ${type} (${name} });`);
    }
    
  } catch (error) {
    logger.error('❌ UUID migration finalization failed:', error);
    throw error;
  }
}

async function main() {
  try {
    await finalizeUUIDMigration();
    logger.debug('\n🎉 UUID migration finalization completed successfully!');
    logger.debug('🔒 Security enhancement complete: All IDs are now UUIDs to prevent enumeration attacks');
  } catch (error) {
    logger.error('\n💥 UUID migration finalization failed:', error);
    process.exit(1);
  } finally {
    logger.debug('Migration process completed.');
  }
}

// Run the migration
main();

export { finalizeUUIDMigration };
