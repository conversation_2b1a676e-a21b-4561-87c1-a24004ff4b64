import { db } from '../db/index.js';
import { caseNotes } from '../db/schema/case-notes.js';
import { eq } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
// Sample plain text content for testing
const SAMPLE_CONTENT = {
  symptoms: "Patient reports severe headaches lasting 3-4 hours, occurring 2-3 times per week. Associated with nausea and sensitivity to light. Pain is described as throbbing and located primarily on the right side of the head.",
  medical_history: "Previous history of migraines since age 25. No significant medical conditions. Family history of migraines (mother). No known allergies to medications.",
  current_medications: "Ibuprofen 400mg as needed for headaches (2-3 times per week). Daily multivitamin. No prescription medications currently.",
  case_description: "35-year-old patient presenting with worsening migraine headaches over the past 6 months. Seeking evaluation for preventive treatment options and lifestyle modifications to reduce frequency and severity of episodes.",
  consultation: "Initial consultation completed. Patient appears well but reports significant impact on daily activities. Neurological examination normal. Recommend MRI to rule out secondary causes and discuss preventive medication options.",
  progress_note: "Patient reports 50% reduction in headache frequency after starting preventive medication 4 weeks ago. Tolerating medication well with no side effects. Continue current regimen and follow up in 8 weeks.",
  second_opinion: "Concur with migraine diagnosis. Current treatment plan is appropriate. Consider adding magnesium supplementation and stress management techniques. Monitor response to current preventive therapy.",
  discharge_summary: "Patient successfully treated for migraine headaches. Preventive medication initiated with good response. Patient educated on trigger avoidance and lifestyle modifications. Follow-up scheduled with primary care physician."
};

async function fixCorruptedNotes() {
  logger.debug('🔧 Starting to fix corrupted notes with sample content...');
  
  try {
    // Get all case notes
    const allNotes = await db
      .select()
      .from(caseNotes)
      .where(eq(caseNotes.isActive, true));

    logger.debug(`📊 Found ${allNotes.length} active notes to process`);

    let updatedCount = 0;

    for (const note of allNotes) {
      let needsUpdate = false;
      let newContent = '';

      // Check if the content is corrupted HTML or needs updating
      if (note.structuredContent && typeof note.structuredContent === 'object') {
        const currentContent = (note.structuredContent as any).content || '';
        
        // Check if content contains HTML tags or is corrupted
        if (currentContent.includes('<') && currentContent.includes('>')) {
          needsUpdate = true;
          
          // Determine note type based on noteTypeId and assign appropriate sample content
          // Since all note types use the same ID in the current system, we'll cycle through different content
          const contentKeys = Object.keys(SAMPLE_CONTENT);
          const contentIndex = updatedCount % contentKeys.length;
          const contentKey = contentKeys[contentIndex] as keyof typeof SAMPLE_CONTENT;
          newContent = SAMPLE_CONTENT[contentKey];
          
          logger.debug(`🔄 Updating note ${note.id} with ${contentKey} content`);
        }
      } else if (typeof note.structuredContent === 'string' && note.structuredContent.includes('<')) {
        // Handle case where structuredContent is a string with HTML
        needsUpdate = true;
        const contentKeys = Object.keys(SAMPLE_CONTENT);
        const contentIndex = updatedCount % contentKeys.length;
        const contentKey = contentKeys[contentIndex] as keyof typeof SAMPLE_CONTENT;
        newContent = SAMPLE_CONTENT[contentKey];
        
        logger.debug('Debug output', undefined, { data: `🔄 Updating note ${note.id} with ${contentKey} content (string format });`);
      }

      if (needsUpdate) {
        // Update the note with clean plain text content
        await db
          .update(caseNotes)
          .set({
            structuredContent: { content: newContent },
            version: note.version + 1,
            updatedAt: new Date(),
          })
          .where(eq(caseNotes.id, note.id));

        updatedCount++;
      }
    }

    logger.debug(`✅ Successfully updated ${updatedCount} corrupted notes with clean sample content`);
    logger.debug('🎉 All notes now contain readable plain text for testing');

  } catch (error) {
    logger.error('❌ Error fixing corrupted notes:', error);
    throw error;
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  fixCorruptedNotes()
    .then(() => {
      logger.debug('✨ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Script failed:', error);
      process.exit(1);
    });
}

export { fixCorruptedNotes };