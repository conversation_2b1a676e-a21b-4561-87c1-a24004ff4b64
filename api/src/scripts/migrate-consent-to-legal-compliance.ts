import { sql } from 'drizzle-orm';
import { db } from '../db/index';
import { config } from 'dotenv';

import { logger } from '../utils/structuredLogger';
// Load environment variables
config();

async function migrateTables() {
  try {
    logger.debug('Starting database migration: Consent Forms → Legal & Compliance');
    
    // Use Drizzle transaction
    
    // 1. Rename consent_form_templates to legal_compliance_templates
    logger.debug('1. Renaming consent_form_templates to legal_compliance_templates...');
    await db.execute(sql`
      ALTER TABLE consent_form_templates 
      RENAME TO legal_compliance_templates
    `);
    
    // 2. Rename consent_form_versions to legal_compliance_versions
    logger.debug('2. Renaming consent_form_versions to legal_compliance_versions...');
    await db.execute(sql`
      ALTER TABLE consent_form_versions 
      RENAME TO legal_compliance_versions
    `);
    
    // 3. Rename user_consents to user_legal_agreements
    logger.info('3. Renaming user_consents to user_legal_agreements...', { requestId: 'context-needed' }, { data: undefined });
    await db.execute(sql`
      ALTER TABLE user_consents 
      RENAME TO user_legal_agreements
    `);
    
    // 4. Skip column renaming - they're already correct
    logger.debug('4. Column names are already correct...');
    
    // 5. Skip column renaming - they're already correct
    logger.debug('5. Column names are already correct...');
    
    // 6. Update constraint names (if they exist)
    logger.debug('6. Updating constraint names...');
    
    // Get existing constraint names first
    const constraints = await db.execute(sql`
      SELECT constraint_name, table_name 
      FROM information_schema.table_constraints 
      WHERE table_name IN ('legal_compliance_templates', 'legal_compliance_versions', 'user_legal_agreements')
      AND constraint_type = 'FOREIGN KEY'
    `);
    logger.debug('Debug output', undefined, { data: 'Found constraints:', constraints });
    
    // Update any consent_form related constraint names if they exist
    for (const constraint of constraints) {
      const constraintName = (constraint as any).constraint_name;
      const tableName = (constraint as any).table_name;
      if (constraintName && constraintName.includes('consent_form')) {
        const newName = constraintName.replace('consent_form', 'legal_compliance');
        logger.debug(`Renaming constraint ${constraintName} to ${newName}`);
        await db.execute(sql`
          ALTER TABLE ${sql.identifier(tableName)} 
          RENAME CONSTRAINT ${sql.identifier(constraintName)} TO ${sql.identifier(newName)}
        `);
      }
    }
    
    // 7. Update any indexes that might reference the old table names
    logger.debug('7. Updating index names...');
    
    const indexes = await db.execute(sql`
      SELECT indexname, tablename 
      FROM pg_indexes 
      WHERE tablename IN ('legal_compliance_templates', 'legal_compliance_versions', 'user_legal_agreements')
      AND indexname LIKE '%consent_form%'
    `);
    logger.debug('Debug output', undefined, { data: 'Found indexes to rename:', indexes });
    
    for (const index of indexes) {
      const indexName = (index as any).indexname;
      if (indexName && indexName.includes('consent_form')) {
        const newName = indexName.replace('consent_form', 'legal_compliance');
        logger.debug(`Renaming index ${indexName} to ${newName}`);
        await db.execute(sql`ALTER INDEX ${sql.identifier(indexName)} RENAME TO ${sql.identifier(newName)}`);
      }
    }
    
    // Transaction will be committed automatically
    
    logger.debug('✅ Database migration completed successfully!');
    logger.debug('Tables renamed:');
    logger.debug('  - consent_form_templates → legal_compliance_templates');
    logger.debug('  - consent_form_versions → legal_compliance_versions');
    logger.info('  - user_consents → user_legal_agreements', { requestId: 'context-needed' }, { data: undefined });
    
    // Verify the migration
    logger.debug('\n🔍 Verifying migration...');
    const verification = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('legal_compliance_templates', 'legal_compliance_versions', 'user_legal_agreements')
    `);
    logger.debug('Debug output', undefined, { data: 'New tables found:', verification.map(r => (r as any });.table_name));
    
    if (verification.length === 3) {
      logger.debug('✅ All tables successfully migrated!');
    } else {
      logger.debug('⚠️  Some tables may not have been migrated properly');
    }
    
  } catch (error) {
    // Transaction will be rolled back automatically on error
    logger.error('❌ Migration failed:', error);
    throw error;
  }
}

async function main() {
  try {
    await migrateTables();
    logger.debug('\n🎉 Migration completed successfully!');
  } catch (error) {
    logger.error('\n💥 Migration failed:', error);
    process.exit(1);
  } finally {
    // Database connection will be closed automatically
    logger.debug('Migration process completed.');
  }
}

// Run the migration
main();

export { migrateTables };
