import { sql } from 'drizzle-orm';
import { db } from '../db/index';
import { config } from 'dotenv';

import { logger } from '../utils/structuredLogger';
// Load environment variables
config();

async function migrateToUUIDs() {
  try {
    logger.debug('Starting UUID migration for Legal & Compliance tables...');
    
    // Step 1: Add UUID extension if not exists
    logger.debug('1. Enabling UUID extension...');
    await db.execute(sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);
    
    // Step 2: Add new UUID columns to existing tables
    logger.debug('2. Adding new UUID columns...');
    
    // Add UUID columns to templates table
    await db.execute(sql`
      ALTER TABLE legal_compliance_templates 
      ADD COLUMN id_uuid UUID DEFAULT uuid_generate_v4()
    `);
    
    // Add UUID columns to versions table
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      ADD COLUMN id_uuid UUID DEFAULT uuid_generate_v4(),
      ADD COLUMN template_id_uuid UUID
    `);
    
    // Add UUID columns to user agreements table (check if columns already exist)
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      ADD COLUMN IF NOT EXISTS id_uuid UUID DEFAULT uuid_generate_v4(),
      ADD COLUMN IF NOT EXISTS form_version_id_uuid UUID
    `);
    
    // Step 3: Populate UUID foreign key relationships
    logger.debug('3. Populating UUID foreign key relationships...');
    
    // Update template_id_uuid in versions table
    await db.execute(sql`
      UPDATE legal_compliance_versions 
      SET template_id_uuid = legal_compliance_templates.id_uuid
      FROM legal_compliance_templates
      WHERE legal_compliance_versions.template_id = legal_compliance_templates.id
    `);
    
    // Update form_version_id_uuid in user agreements table
    await db.execute(sql`
      UPDATE user_legal_agreements 
      SET form_version_id_uuid = legal_compliance_versions.id_uuid
      FROM legal_compliance_versions
      WHERE user_legal_agreements.form_version_id = legal_compliance_versions.id
    `);
    
    // Step 4: Drop old foreign key constraints
    logger.debug('4. Dropping old foreign key constraints...');
    
    // Get and drop existing foreign key constraints
    const constraints = await db.execute(sql`
      SELECT constraint_name, table_name 
      FROM information_schema.table_constraints 
      WHERE table_name IN ('legal_compliance_templates', 'legal_compliance_versions', 'user_legal_agreements')
      AND constraint_type = 'FOREIGN KEY'
    `);
    
    for (const constraint of constraints) {
      const constraintName = (constraint as any).constraint_name;
      const tableName = (constraint as any).table_name;
      if (constraintName && tableName) {
        logger.debug(`Dropping constraint ${constraintName} from ${tableName}`);
        await db.execute(sql`
          ALTER TABLE ${sql.identifier(tableName)} 
          DROP CONSTRAINT ${sql.identifier(constraintName)}
        `);
      }
    }
    
    // Step 5: Drop old integer ID columns and rename UUID columns
    logger.debug('5. Replacing integer IDs with UUIDs...');
    
    // Templates table
    await db.execute(sql`
      ALTER TABLE legal_compliance_templates 
      DROP COLUMN id CASCADE,
      ALTER COLUMN id_uuid SET NOT NULL,
      ALTER COLUMN id_uuid DROP DEFAULT
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_templates 
      RENAME COLUMN id_uuid TO id
    `);
    
    // Versions table
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      DROP COLUMN id CASCADE,
      DROP COLUMN template_id CASCADE,
      ALTER COLUMN id_uuid SET NOT NULL,
      ALTER COLUMN id_uuid DROP DEFAULT,
      ALTER COLUMN template_id_uuid SET NOT NULL
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      RENAME COLUMN id_uuid TO id,
      RENAME COLUMN template_id_uuid TO template_id
    `);
    
    // User agreements table
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      DROP COLUMN id CASCADE,
      DROP COLUMN form_version_id CASCADE,
      ALTER COLUMN id_uuid SET NOT NULL,
      ALTER COLUMN id_uuid DROP DEFAULT,
      ALTER COLUMN form_version_id_uuid SET NOT NULL
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      RENAME COLUMN id_uuid TO id,
      RENAME COLUMN form_version_id_uuid TO document_version_id
    `);
    
    // Step 6: Add primary key constraints
    logger.debug('6. Adding primary key constraints...');
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_templates 
      ADD CONSTRAINT legal_compliance_templates_pkey PRIMARY KEY (id)
    `);
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      ADD CONSTRAINT legal_compliance_versions_pkey PRIMARY KEY (id)
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      ADD CONSTRAINT user_legal_agreements_pkey PRIMARY KEY (id)
    `);
    
    // Step 7: Add foreign key constraints
    logger.debug('7. Adding new foreign key constraints...');
    
    await db.execute(sql`
      ALTER TABLE legal_compliance_versions 
      ADD CONSTRAINT legal_compliance_versions_template_id_fkey 
      FOREIGN KEY (template_id) REFERENCES legal_compliance_templates(id)
    `);
    
    await db.execute(sql`
      ALTER TABLE user_legal_agreements 
      ADD CONSTRAINT user_legal_agreements_document_version_id_fkey 
      FOREIGN KEY (document_version_id) REFERENCES legal_compliance_versions(id)
    `);
    
    // Step 8: Add indexes for performance
    logger.debug('8. Adding indexes...');
    
    await db.execute(sql`
      CREATE INDEX idx_legal_compliance_versions_template_id 
      ON legal_compliance_versions(template_id)
    `);
    
    await db.execute(sql`
      CREATE INDEX idx_legal_compliance_versions_is_active 
      ON legal_compliance_versions(is_active)
    `);
    
    await db.execute(sql`
      CREATE INDEX idx_user_legal_agreements_user_id 
      ON user_legal_agreements(user_id)
    `);
    
    await db.execute(sql`
      CREATE INDEX idx_user_legal_agreements_document_version_id 
      ON user_legal_agreements(document_version_id)
    `);
    
    logger.debug('✅ UUID migration completed successfully!');
    logger.debug('Security improvements:');
    logger.debug('Debug output', undefined, { data: '  - Template IDs are now UUIDs (prevents enumeration attacks });');
    logger.debug('Debug output', undefined, { data: '  - Version IDs are now UUIDs (prevents enumeration attacks });');
    logger.info('  - User agreement IDs are now UUIDs (prevents enumeration attacks)', { requestId: 'context-needed' }, { data: undefined });
    logger.debug('  - All foreign key relationships preserved');
    
    // Verify the migration
    logger.debug('\n🔍 Verifying migration...');
    const verification = await db.execute(sql`
      SELECT 
        t.table_name,
        c.column_name,
        c.data_type
      FROM information_schema.tables t
      JOIN information_schema.columns c ON t.table_name = c.table_name
      WHERE t.table_schema = 'public' 
      AND t.table_name IN ('legal_compliance_templates', 'legal_compliance_versions', 'user_legal_agreements')
      AND c.column_name IN ('id', 'template_id', 'document_version_id')
      ORDER BY t.table_name, c.column_name
    `);
    
    logger.debug('Column types after migration:');
    for (const row of verification) {
      const tableName = (row as any).table_name;
      const columnName = (row as any).column_name;
      const dataType = (row as any).data_type;
      logger.debug(`  ${tableName}.${columnName}: ${dataType}`);
    }
    
    // Check data count
    const counts = await db.execute(sql`
      SELECT 
        (SELECT COUNT(*) FROM legal_compliance_templates) as templates,
        (SELECT COUNT(*) FROM legal_compliance_versions) as versions,
        (SELECT COUNT(*) FROM user_legal_agreements) as agreements
    `);
    
    const countsRow = counts[0] as any;
    logger.debug('\nData preserved:');
    logger.debug(`  Templates: ${countsRow.templates}`);
    logger.debug(`  Versions: ${countsRow.versions}`);
    logger.info('  User Agreements: ${countsRow.agreements}', { requestId: 'context-needed' }, { data: undefined });
    
  } catch (error) {
    logger.error('❌ UUID migration failed:', error);
    throw error;
  }
}

async function main() {
  try {
    await migrateToUUIDs();
    logger.debug('\n🎉 UUID migration completed successfully!');
  } catch (error) {
    logger.error('\n💥 UUID migration failed:', error);
    process.exit(1);
  } finally {
    logger.debug('Migration process completed.');
  }
}

// Run the migration
main();

export { migrateToUUIDs };
