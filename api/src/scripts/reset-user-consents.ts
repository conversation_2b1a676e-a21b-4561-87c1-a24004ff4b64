import { db } from '../db';
import { userLegalAgreements } from '../db/schema';
import { sql } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
/**
 * Reset all user consents for testing
 * This will clear all consent records so users will need to accept them again
 */
async function resetUserConsents() {
  try {
    logger.info('🔄 Resetting all user consents...', { requestId: 'context-needed' }, { data: undefined });

    // Delete all existing user legal agreements
    const result = await db.execute(sql`
      DELETE FROM user_legal_agreements;
    `);

    logger.info('✅ All user consents have been reset', { requestId: 'context-needed' }, { data: undefined });
    logger.info('📝 Users will now be prompted to accept consents again on login', { requestId: 'context-needed' }, { data: undefined });
    
    // Show current user count for reference
    const userCount = await db.execute(sql`
      SELECT COUNT(*) as count FROM users;
    `);
    
    logger.info('👥 Total users in system: ${userCount[0]?.count || 0}', { requestId: 'context-needed' }, { data: undefined });
    
    process.exit(0);
  } catch (error) {
    logger.error('❌ Error resetting user consents:', error);
    process.exit(1);
  }
}

// Run the script
resetUserConsents();
