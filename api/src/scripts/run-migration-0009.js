import { Client } from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

import { logger } from '../utils/structuredLogger';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'continuia',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  });

  try {
    await client.connect();
    logger.debug('Connected to database');

    // Read the migration file
    const migrationPath = path.join(__dirname, '../../drizzle/0009_add_doctor_acceptance_status.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    logger.debug('Running migration 0009_add_doctor_acceptance_status...');
    
    // Split by semicolon and execute each statement
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    for (const statement of statements) {
      logger.debug('Debug output', undefined, { data: `Executing: ${statement.substring(0, 50 });}...`);
      await client.query(statement);
    }

    logger.debug('Migration completed successfully!');

    // Verify the changes
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'case_doctors' 
      ORDER BY ordinal_position;
    `);
    
    logger.debug('\nUpdated case_doctors table structure:');
    console.table(result.rows);

  } catch (error) {
    logger.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

runMigration();