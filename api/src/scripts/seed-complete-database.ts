#!/usr/bin/env tsx

/**
 * Complete Database Seeding Script
 * This script contains ALL the seed data from migrations-clean
 * Run this after <PERSON><PERSON><PERSON> generates the schema to populate initial data
 */

import { db } from '../db/index.js';
import { roles, permissions, rolePermissions, users, noteTypes } from '../db/schema/index.js';
import { logger } from '../utils/structuredLogger.js';
import bcrypt from 'bcryptjs';

async function seedCompleteDatabase() {
  logger.info('🌱 Starting complete database seeding...');
  
  try {
    // 1. Seed Roles
    logger.info('👥 Seeding roles...');
    const rolesToInsert = [
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        name: 'patient',
        displayName: 'Patient',
        description: 'Healthcare patients seeking medical opinions',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        name: 'doctor',
        displayName: 'Doctor',
        description: 'Healthcare providers giving medical opinions',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        name: 'agent',
        displayName: 'Agent',
        description: 'Customer service agents managing patient interactions',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440004',
        name: 'admin',
        displayName: 'Administrator',
        description: 'System administrators with full access',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    for (const role of rolesToInsert) {
      await db.insert(roles).values(role).onConflictDoNothing();
    }
    logger.info('✅ Roles seeded');

    // 2. Seed Permissions
    logger.info('🔐 Seeding permissions...');
    const permissionsToInsert = [
      // Cases permissions
      {
        id: '650e8400-e29b-41d4-a716-446655440001',
        name: 'cases:read',
        displayName: 'Read Cases',
        description: 'View medical cases',
        resource: 'cases',
        action: 'read',
        scope: 'own',
        filterConditions: '{"patientId": "{{userId}}"}',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '650e8400-e29b-41d4-a716-446655440002',
        name: 'cases:write',
        displayName: 'Write Cases',
        description: 'Create and edit medical cases',
        resource: 'cases',
        action: 'write',
        scope: 'own',
        filterConditions: '{"patientId": "{{userId}}"}',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '650e8400-e29b-41d4-a716-446655440003',
        name: 'cases:read:assigned',
        displayName: 'Read Assigned Cases',
        description: 'View assigned medical cases',
        resource: 'cases',
        action: 'read',
        scope: 'assigned',
        filterConditions: null,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Appointments permissions
      {
        id: '650e8400-e29b-41d4-a716-446655440014',
        name: 'appointments:read',
        displayName: 'Read Appointments',
        description: 'View appointments',
        resource: 'appointments',
        action: 'read',
        scope: 'own',
        filterConditions: null,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '650e8400-e29b-41d4-a716-446655440015',
        name: 'appointments:write',
        displayName: 'Write Appointments',
        description: 'Create and edit appointments',
        resource: 'appointments',
        action: 'write',
        scope: 'own',
        filterConditions: null,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Documents permissions
      {
        id: '650e8400-e29b-41d4-a716-446655440007',
        name: 'documents:read',
        displayName: 'Read Documents',
        description: 'View medical documents',
        resource: 'documents',
        action: 'read',
        scope: 'own',
        filterConditions: '{"uploadedBy": "{{userId}}"}',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '650e8400-e29b-41d4-a716-446655440008',
        name: 'documents:write',
        displayName: 'Write Documents',
        description: 'Upload and edit medical documents',
        resource: 'documents',
        action: 'write',
        scope: 'own',
        filterConditions: '{"uploadedBy": "{{userId}}"}',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    for (const permission of permissionsToInsert) {
      await db.insert(permissions).values(permission).onConflictDoNothing();
    }
    logger.info('✅ Permissions seeded');

    // 3. Seed Role-Permission Assignments
    logger.info('🔗 Seeding role-permission assignments...');
    const rolePermissionAssignments = [
      // Patient permissions
      { roleId: '550e8400-e29b-41d4-a716-446655440001', permissionId: '650e8400-e29b-41d4-a716-446655440001' }, // cases:read
      { roleId: '550e8400-e29b-41d4-a716-446655440001', permissionId: '650e8400-e29b-41d4-a716-446655440002' }, // cases:write
      { roleId: '550e8400-e29b-41d4-a716-446655440001', permissionId: '650e8400-e29b-41d4-a716-446655440007' }, // documents:read
      { roleId: '550e8400-e29b-41d4-a716-446655440001', permissionId: '650e8400-e29b-41d4-a716-446655440008' }, // documents:write
      { roleId: '550e8400-e29b-41d4-a716-446655440001', permissionId: '650e8400-e29b-41d4-a716-446655440014' }, // appointments:read
      { roleId: '550e8400-e29b-41d4-a716-446655440001', permissionId: '650e8400-e29b-41d4-a716-446655440015' }, // appointments:write
      
      // Doctor permissions
      { roleId: '550e8400-e29b-41d4-a716-446655440002', permissionId: '650e8400-e29b-41d4-a716-446655440003' }, // cases:read:assigned
      { roleId: '550e8400-e29b-41d4-a716-446655440002', permissionId: '650e8400-e29b-41d4-a716-446655440014' }, // appointments:read
      { roleId: '550e8400-e29b-41d4-a716-446655440002', permissionId: '650e8400-e29b-41d4-a716-446655440015' }, // appointments:write
    ];

    for (const assignment of rolePermissionAssignments) {
      await db.insert(rolePermissions).values({
        roleId: assignment.roleId,
        permissionId: assignment.permissionId,
        isActive: true,
        createdAt: new Date()
      }).onConflictDoNothing();
    }
    logger.info('✅ Role-permission assignments seeded');

    // 4. Seed Initial Users
    logger.info('👤 Seeding initial users...');
    const passwordHash = await bcrypt.hash('Continuia', 12);
    
    const usersToInsert = [
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        email: '<EMAIL>',
        passwordHash,
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        isActive: true,
        isEmailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        email: '<EMAIL>',
        passwordHash,
        firstName: 'Dr. John',
        lastName: 'Smith',
        role: 'doctor',
        isActive: true,
        isEmailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        email: '<EMAIL>',
        passwordHash,
        firstName: 'Shree',
        lastName: 'Mandadi',
        role: 'patient',
        isActive: true,
        isEmailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    for (const user of usersToInsert) {
      await db.insert(users).values(user).onConflictDoNothing();
    }
    logger.info('✅ Initial users seeded');

    // 5. Seed Note Types
    logger.info('📝 Seeding note types...');
    const noteTypesToInsert = [
      {
        id: 'c6867d50-6dc7-443d-aa14-061ab153d685',
        key: 'clinical_notes',
        name: 'Clinical Notes',
        description: 'Initial clinical information provided by patient during case creation',
        icon: 'FileText',
        color: 'blue',
        category: 'clinical',
        allowedRoles: JSON.stringify(['patient', 'doctor', 'admin']),
        requiresDoctor: false,
        requiresPermission: null,
        autoSave: true,
        autoSaveDelay: 2000,
        richText: true,
        showDoctorInfo: true,
        showInSidebar: false,
        placeholder: 'Enter clinical information...',
        template: null,
        aiEnabled: false,
        aiModel: null,
        aiPrompt: null,
        sortOrder: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: '550e8400-e29b-41d4-a716-446655440001' // admin user
      }
    ];

    for (const noteType of noteTypesToInsert) {
      await db.insert(noteTypes).values(noteType).onConflictDoNothing();
    }
    logger.info('✅ Note types seeded');

    logger.info('');
    logger.info('🎉 Complete database seeding finished successfully!');
    logger.info('');
    logger.info('📋 Seeded data:');
    logger.info('  ✅ 4 roles (patient, doctor, agent, admin)');
    logger.info('  ✅ 7 permissions (cases, appointments, documents)');
    logger.info('  ✅ Role-permission assignments');
    logger.info('  ✅ 3 initial users (admin, doctor, patient)');
    logger.info('  ✅ 1 note type (clinical_notes)');
    logger.info('');
    logger.info('🔐 Login credentials:');
    logger.info('  Admin: <EMAIL> / Continuia');
    logger.info('  Doctor: <EMAIL> / Continuia');
    logger.info('  Patient: <EMAIL> / Continuia');
    logger.info('');
    logger.info('⚠️  Change passwords in production!');

  } catch (error) {
    logger.error('❌ Database seeding failed:', error);
    throw error;
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedCompleteDatabase()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedCompleteDatabase };
