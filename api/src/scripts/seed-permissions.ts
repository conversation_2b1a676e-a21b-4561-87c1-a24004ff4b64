#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to seed the database with proper roles and permissions
 * This ensures the OPAL policy engine has the correct permissions to work with
 */

import { db } from '../db/index.js';
import { roles, permissions, rolePermissions } from '../db/schema/permissions.js';
import { eq, and } from 'drizzle-orm';
import { logger } from '../utils/structuredLogger.js';

// Define the roles that should exist
const REQUIRED_ROLES = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    name: 'patient',
    displayName: 'Patient',
    description: 'Healthcare patients seeking medical opinions',
    isActive: true
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    name: 'doctor',
    displayName: 'Doctor',
    description: 'Healthcare providers giving medical opinions',
    isActive: true
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    name: 'agent',
    displayName: 'Agent',
    description: 'Customer service agents managing patient interactions',
    isActive: true
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440004',
    name: 'admin',
    displayName: 'Administrator',
    description: 'System administrators with full access',
    isActive: true
  }
];

// Define the permissions that should exist
const REQUIRED_PERMISSIONS = [
  // Appointments permissions
  {
    id: '650e8400-e29b-41d4-a716-446655440020',
    name: 'appointments:read',
    displayName: 'Read Appointments',
    description: 'View appointments',
    resource: 'appointments',
    action: 'read',
    scope: 'own',
    filterConditions: '{"patientId": "{{userId}}"}'
  },
  {
    id: '650e8400-e29b-41d4-a716-446655440021',
    name: 'appointments:write',
    displayName: 'Write Appointments',
    description: 'Create and edit appointments',
    resource: 'appointments',
    action: 'write',
    scope: 'own',
    filterConditions: '{"patientId": "{{userId}}"}'
  },
  {
    id: '650e8400-e29b-41d4-a716-446655440022',
    name: 'appointments:read:assigned',
    displayName: 'Read Assigned Appointments',
    description: 'View assigned appointments',
    resource: 'appointments',
    action: 'read',
    scope: 'assigned',
    filterConditions: '{"doctorId": "{{userId}}"}'
  },
  {
    id: '650e8400-e29b-41d4-a716-446655440023',
    name: 'appointments:write:assigned',
    displayName: 'Write Assigned Appointments',
    description: 'Edit assigned appointments',
    resource: 'appointments',
    action: 'write',
    scope: 'assigned',
    filterConditions: '{"doctorId": "{{userId}}"}'
  },
  {
    id: '650e8400-e29b-41d4-a716-446655440024',
    name: 'appointments:read:all',
    displayName: 'Read All Appointments',
    description: 'View all appointments',
    resource: 'appointments',
    action: 'read',
    scope: 'global',
    filterConditions: null
  },
  // Cases permissions
  {
    id: '650e8400-e29b-41d4-a716-446655440001',
    name: 'cases:read',
    displayName: 'Read Cases',
    description: 'View medical cases',
    resource: 'cases',
    action: 'read',
    scope: 'own',
    filterConditions: '{"patientId": "{{userId}}"}'
  },
  {
    id: '650e8400-e29b-41d4-a716-446655440002',
    name: 'cases:write',
    displayName: 'Write Cases',
    description: 'Create and edit medical cases',
    resource: 'cases',
    action: 'write',
    scope: 'own',
    filterConditions: '{"patientId": "{{userId}}"}'
  },
  {
    id: '650e8400-e29b-41d4-a716-446655440003',
    name: 'cases:read:assigned',
    displayName: 'Read Assigned Cases',
    description: 'View assigned medical cases',
    resource: 'cases',
    action: 'read',
    scope: 'assigned',
    filterConditions: '{"assignedDoctor": "{{userId}}"}'
  },
  {
    id: '650e8400-e29b-41d4-a716-446655440004',
    name: 'cases:write:assigned',
    displayName: 'Write Assigned Cases',
    description: 'Edit assigned medical cases',
    resource: 'cases',
    action: 'write',
    scope: 'assigned',
    filterConditions: '{"assignedDoctor": "{{userId}}"}'
  },
  {
    id: '650e8400-e29b-41d4-a716-446655440005',
    name: 'cases:read:all',
    displayName: 'Read All Cases',
    description: 'View all medical cases',
    resource: 'cases',
    action: 'read',
    scope: 'global',
    filterConditions: null
  }
];

// Define role-permission assignments
const ROLE_PERMISSION_ASSIGNMENTS = [
  // Patient permissions
  { roleId: '550e8400-e29b-41d4-a716-446655440001', permissionId: '650e8400-e29b-41d4-a716-446655440001' }, // cases:read
  { roleId: '550e8400-e29b-41d4-a716-446655440001', permissionId: '650e8400-e29b-41d4-a716-446655440002' }, // cases:write
  { roleId: '550e8400-e29b-41d4-a716-446655440001', permissionId: '650e8400-e29b-41d4-a716-446655440020' }, // appointments:read
  { roleId: '550e8400-e29b-41d4-a716-446655440001', permissionId: '650e8400-e29b-41d4-a716-446655440021' }, // appointments:write
  
  // Doctor permissions
  { roleId: '550e8400-e29b-41d4-a716-446655440002', permissionId: '650e8400-e29b-41d4-a716-446655440003' }, // cases:read:assigned
  { roleId: '550e8400-e29b-41d4-a716-446655440002', permissionId: '650e8400-e29b-41d4-a716-446655440004' }, // cases:write:assigned
  { roleId: '550e8400-e29b-41d4-a716-446655440002', permissionId: '650e8400-e29b-41d4-a716-446655440022' }, // appointments:read:assigned
  { roleId: '550e8400-e29b-41d4-a716-446655440002', permissionId: '650e8400-e29b-41d4-a716-446655440023' }, // appointments:write:assigned
  
  // Agent permissions
  { roleId: '550e8400-e29b-41d4-a716-446655440003', permissionId: '650e8400-e29b-41d4-a716-446655440005' }, // cases:read:all
  { roleId: '550e8400-e29b-41d4-a716-446655440003', permissionId: '650e8400-e29b-41d4-a716-446655440024' }, // appointments:read:all
  
  // Admin permissions (gets all permissions)
  { roleId: '550e8400-e29b-41d4-a716-446655440004', permissionId: '650e8400-e29b-41d4-a716-446655440001' },
  { roleId: '550e8400-e29b-41d4-a716-446655440004', permissionId: '650e8400-e29b-41d4-a716-446655440002' },
  { roleId: '550e8400-e29b-41d4-a716-446655440004', permissionId: '650e8400-e29b-41d4-a716-446655440003' },
  { roleId: '550e8400-e29b-41d4-a716-446655440004', permissionId: '650e8400-e29b-41d4-a716-446655440004' },
  { roleId: '550e8400-e29b-41d4-a716-446655440004', permissionId: '650e8400-e29b-41d4-a716-446655440005' },
  { roleId: '550e8400-e29b-41d4-a716-446655440004', permissionId: '650e8400-e29b-41d4-a716-446655440020' },
  { roleId: '550e8400-e29b-41d4-a716-446655440004', permissionId: '650e8400-e29b-41d4-a716-446655440021' },
  { roleId: '550e8400-e29b-41d4-a716-446655440004', permissionId: '650e8400-e29b-41d4-a716-446655440022' },
  { roleId: '550e8400-e29b-41d4-a716-446655440004', permissionId: '650e8400-e29b-41d4-a716-446655440023' },
  { roleId: '550e8400-e29b-41d4-a716-446655440004', permissionId: '650e8400-e29b-41d4-a716-446655440024' }
];

async function seedPermissions() {
  logger.info('Starting permissions seeding...');
  
  try {
    // 1. Seed roles
    logger.info('Seeding roles...');
    for (const role of REQUIRED_ROLES) {
      const existing = await db.select().from(roles).where(eq(roles.name, role.name)).limit(1);
      
      if (existing.length === 0) {
        await db.insert(roles).values({
          id: role.id,
          name: role.name,
          displayName: role.displayName,
          description: role.description,
          isActive: role.isActive,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        logger.info(`✅ Created role: ${role.name}`);
      } else {
        logger.info(`Role '${role.name}' already exists, skipping...`);
      }
    }
    
    // 2. Seed permissions
    logger.info('Seeding permissions...');
    for (const permission of REQUIRED_PERMISSIONS) {
      const existing = await db.select().from(permissions).where(eq(permissions.name, permission.name)).limit(1);
      
      if (existing.length === 0) {
        await db.insert(permissions).values({
          id: permission.id,
          name: permission.name,
          displayName: permission.displayName,
          description: permission.description,
          resource: permission.resource,
          action: permission.action,
          scope: permission.scope,
          filterConditions: permission.filterConditions,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        logger.info(`✅ Created permission: ${permission.name}`);
      } else {
        logger.info(`Permission '${permission.name}' already exists, skipping...`);
      }
    }
    
    // 3. Seed role-permission assignments
    logger.info('Seeding role-permission assignments...');
    for (const assignment of ROLE_PERMISSION_ASSIGNMENTS) {
      const existing = await db.select().from(rolePermissions).where(
        and(
          eq(rolePermissions.roleId, assignment.roleId),
          eq(rolePermissions.permissionId, assignment.permissionId)
        )
      ).limit(1);
      
      if (existing.length === 0) {
        await db.insert(rolePermissions).values({
          roleId: assignment.roleId,
          permissionId: assignment.permissionId,
          isActive: true,
          createdAt: new Date()
        });
        logger.info(`✅ Assigned permission to role`);
      }
    }
    
    logger.info('✅ Permissions seeding completed successfully!');
    
  } catch (error) {
    logger.error('Error during permissions seeding:', error);
    process.exit(1);
  }
}

// Run the script
seedPermissions().catch(console.error);
