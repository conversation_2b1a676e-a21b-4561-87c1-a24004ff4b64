#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to seed the database with default note types
 * This script will only add missing note types without wiping existing data
 */

import { db } from '../db/index.js';
import { noteTypes } from '../db/schema/note-types.js';
import { eq } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
async function seedNoteTypes() {
  logger.debug('Starting note types seeding...');
  
  try {
    // Import the default note types from the schema
    const { defaultNoteTypes } = await import('../db/schema/note-types.js');
    
    logger.debug(`Found ${defaultNoteTypes.length} default note types to seed`);
    
    for (const noteType of defaultNoteTypes) {
      // Check if note type already exists
      const existing = await db.select().from(noteTypes).where(eq(noteTypes.key, noteType.key)).limit(1);
      
      if (existing.length > 0) {
        logger.debug('Debug output', undefined, { data: `Note type '${noteType.name}' already exists, skipping...` });
        continue;
      }
      
      // Create the note type
      const [created] = await db.insert(noteTypes).values({
        key: noteType.key,
        name: noteType.name,
        description: noteType.description,
        icon: noteType.icon || 'FileText',
        color: noteType.color || 'blue',
        category: noteType.category || 'clinical',
        allowedRoles: noteType.allowedRoles,
        requiresDoctor: noteType.requiresDoctor || false,
        showInSidebar: noteType.showInSidebar || false,
        placeholder: noteType.placeholder,
        aiEnabled: noteType.aiEnabled || false,
        aiModel: noteType.aiModel,
        aiPrompt: noteType.aiPrompt,
        sortOrder: noteType.sortOrder || 0,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }).returning();
      
      logger.debug(`✅ Created note type: ${noteType.name}`);
    }
    
    logger.debug('Note types seeding completed successfully!');
    
  } catch (error) {
    logger.error('Error during note types seeding:', error);
    process.exit(1);
  }
}

// Run the script
seedNoteTypes().catch(console.error);