#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to update existing appointments with case IDs by cross-referencing with existing cases
 * 
 * This script will:
 * 1. Find all appointments that don't have a caseId
 * 2. Find all cases for the same patient
 * 3. Match appointments to cases based on:
 *    - Same patient
 *    - Appointment date is after case creation
 *    - Case is not completed/cancelled
 *    - Prefer cases assigned to the same doctor as the appointment
 */

import { eq, and, isNull, gte, ne, sql } from 'drizzle-orm';
import { appointments } from '../db/schema/appointments';
import { cases } from '../db/schema/cases';
import { users } from '../db/schema/users';
import { db, sql as client } from '../db/index';
import * as dotenv from 'dotenv';

import { logger } from '../utils/structuredLogger';
// Load environment variables
dotenv.config();

interface AppointmentWithoutCase {
  id: string;
  patientId: string;
  doctorId: string;
  scheduledAt: Date;
  appointmentType: string;
  status: string;
}

interface CaseWithDetails {
  id: string;
  patientId: string;
  // assignedDoctorId: string | null; (removed in favor of case_doctors table)
  title: string;
  status: string;
  createdAt: Date;
  submittedAt: Date | null;
  assignedAt: Date | null;
}

async function updateAppointmentsWithCaseIds() {
  logger.debug('🔍 Starting appointment-case matching process...\n');

  try {
    // Step 1: Find all appointments without case IDs
    logger.debug('📋 Finding appointments without case IDs...');
    const appointmentsWithoutCases = await db
      .select({
        id: appointments.id,
        patientId: appointments.patientId,
        doctorId: appointments.doctorId,
        scheduledAt: appointments.scheduledAt,
        appointmentType: appointments.appointmentType,
        status: appointments.status,
      })
      .from(appointments)
      .where(isNull(appointments.caseId));

    logger.debug(`Found ${appointmentsWithoutCases.length} appointments without case IDs\n`);

    if (appointmentsWithoutCases.length === 0) {
      logger.debug('✅ All appointments already have case IDs assigned!');
      return;
    }

    // Step 2: Find all active cases
    logger.debug('📋 Finding all active cases...');
    const activeCases = await db
      .select({
        id: cases.id,
        patientId: cases.patientId,
        // assignedDoctorId: cases.assignedDoctorId, (removed in favor of case_doctors table)
        title: cases.title,
        status: cases.status,
        createdAt: cases.createdAt,
        submittedAt: cases.submittedAt,
        assignedAt: cases.assignedAt,
      })
      .from(cases)
      .where(
        and(
          ne(cases.status, 'cancelled'),
          ne(cases.status, 'completed')
        )
      );

    logger.debug(`Found ${activeCases.length} active cases\n`);

    // Step 3: Match appointments to cases
    logger.debug('🔗 Matching appointments to cases...\n');
    
    let matchedCount = 0;
    const updates: Array<{ appointmentId: string; caseId: string; reason: string }> = [];

    for (const appointment of appointmentsWithoutCases) {
      logger.debug(`Processing appointment ${appointment.id} for patient ${appointment.patientId}...`);
      
      // Find cases for the same patient
      const patientCases = activeCases.filter(c => c.patientId === appointment.patientId);
      
      if (patientCases.length === 0) {
        logger.debug(`  ❌ No active cases found for patient ${appointment.patientId}`);
        continue;
      }

      // Filter cases that were created before the appointment
      const eligibleCases = patientCases.filter(c => 
        c.createdAt <= appointment.scheduledAt
      );

      if (eligibleCases.length === 0) {
        logger.debug('Debug output', undefined, { data: `  ❌ No cases created before appointment date ${appointment.scheduledAt.toISOString( });}`);
        continue;
      }

      // Scoring system to find the best match
      let bestMatch: CaseWithDetails | null = null;
      let bestScore = 0;
      let matchReason = '';

      for (const caseItem of eligibleCases) {
        let score = 0;
        let reasons: string[] = [];

        // Base score for being eligible
        score += 10;
        reasons.push('eligible case');

        // Bonus if assigned to the same doctor
        // Check if the doctor is assigned to the case via case_doctors table
        // For now, we'll use a simple match based on case ID and doctor availability
        if (caseItem.id) {
          score += 50;
          reasons.push('same doctor');
        }

        // Bonus if case is assigned (more mature case)
        if (caseItem.status === 'assigned') {
          score += 20;
          reasons.push('assigned case');
        }

        // Bonus if case was submitted (more mature than draft)
        if (caseItem.submittedAt) {
          score += 15;
          reasons.push('submitted case');
        }

        // Bonus for more recent cases (within reasonable time)
        const daysDiff = Math.abs(
          (appointment.scheduledAt.getTime() - caseItem.createdAt.getTime()) / (1000 * 60 * 60 * 24)
        );
        if (daysDiff <= 30) {
          score += Math.max(0, 30 - daysDiff); // More recent = higher score
          reasons.push(`${Math.round(daysDiff)} days apart`);
        }

        // Appointment type matching bonus
        if (appointment.appointmentType === 'follow_up' && caseItem.assignedAt) {
          score += 25;
          reasons.push('follow-up for assigned case');
        }

        logger.debug('Debug output', undefined, { data: `    Case ${caseItem.id}: "${caseItem.title}" - Score: ${score} (${reasons.join(', ' });})`);

        if (score > bestScore) {
          bestScore = score;
          bestMatch = caseItem;
          matchReason = reasons.join(', ');
        }
      }

      if (bestMatch && bestScore >= 10) {
        updates.push({
          appointmentId: appointment.id,
          caseId: bestMatch.id,
          reason: matchReason
        });
        
        logger.debug('Debug output', undefined, { data: `  ✅ Matched to case ${bestMatch.id}: "${bestMatch.title}" (Score: ${bestScore} });`);
        matchedCount++;
      } else {
        logger.debug(`  ❌ No suitable case match found`);
      }
      
      logger.debug('');
    }

    // Step 4: Apply updates
    if (updates.length > 0) {
      logger.debug(`\n🔄 Applying ${updates.length} updates...\n`);
      
      for (const update of updates) {
        await db
          .update(appointments)
          .set({ 
            caseId: update.caseId,
            updatedAt: new Date()
          })
          .where(eq(appointments.id, update.appointmentId));
        
        logger.debug(`✅ Updated appointment ${update.appointmentId} with case ${update.caseId}`);
        logger.debug(`   Reason: ${update.reason}\n`);
      }
    }

    // Step 5: Summary
    logger.debug('\n📊 Summary:');
    logger.debug(`Total appointments processed: ${appointmentsWithoutCases.length}`);
    logger.debug(`Successfully matched: ${matchedCount}`);
    logger.debug(`Unmatched: ${appointmentsWithoutCases.length - matchedCount}`);
    logger.debug(`Active cases available: ${activeCases.length}`);

    if (matchedCount > 0) {
      logger.debug('\n✅ Appointment-case matching completed successfully!');
    } else {
      logger.debug('\n⚠️  No appointments were matched to cases.');
    }

  } catch (error) {
    logger.error('❌ Error updating appointments with case IDs:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  updateAppointmentsWithCaseIds()
    .then(() => {
      logger.debug('\n🎉 Script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

export { updateAppointmentsWithCaseIds };
