#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to update existing note types with new configuration
 * This script will update the showInSidebar field for existing note types
 */

import { db } from '../db/index.js';
import { noteTypes } from '../db/schema/note-types.js';
import { eq } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
async function updateNoteTypes() {
  logger.debug('Starting note types update...');
  
  try {
    // Update Clinical Notes
    await db.update(noteTypes)
      .set({ showInSidebar: true })
      .where(eq(noteTypes.key, 'clinical_notes'));
    logger.debug('✅ Updated Clinical Notes: showInSidebar = true');

    // Update Second Opinion
    await db.update(noteTypes)
      .set({ showInSidebar: true })
      .where(eq(noteTypes.key, 'second_opinion'));
    logger.debug('✅ Updated Second Opinion: showInSidebar = true');

    // Update Consultation Notes
    await db.update(noteTypes)
      .set({ showInSidebar: true })
      .where(eq(noteTypes.key, 'consultation'));
    logger.debug('✅ Updated Consultation Notes: showInSidebar = true');

    // Update Progress Notes
    await db.update(noteTypes)
      .set({ showInSidebar: true })
      .where(eq(noteTypes.key, 'progress_note'));
    logger.debug('✅ Updated Progress Notes: showInSidebar = true');

    // Verify the updates
    logger.debug('\n🔍 Verifying updates...');
    const updatedNoteTypes = await db.select().from(noteTypes).where(eq(noteTypes.showInSidebar, true));
    logger.debug(`Found ${updatedNoteTypes.length} note types with showInSidebar = true:`);
    
    for (const noteType of updatedNoteTypes) {
      logger.debug('Debug output', undefined, { data: `  - ${noteType.name} (${noteType.key} });`);
    }
    
    logger.debug('\n✅ Note types update completed successfully!');
    
  } catch (error) {
    logger.error('❌ Error during note types update:', error);
    process.exit(1);
  }
}

// Run the script
updateNoteTypes().catch(console.error);