#!/usr/bin/env tsx

/**
 * Database Configuration Validation Script
 * Validates that DATABASE_URL is properly configured and accessible
 */

import { db, sql } from '../db/index.js';
import { logger } from '../utils/structuredLogger.js';
import * as dotenv from 'dotenv';

dotenv.config();

async function validateDatabaseConfig() {
  logger.info('🔍 Validating database configuration...');
  
  try {
    // 1. Check if DATABASE_URL is set
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL environment variable is not set');
    }
    
    logger.info('✅ DATABASE_URL is configured');
    
    // 2. Parse DATABASE_URL
    try {
      const url = new URL(databaseUrl);
      logger.info('📊 Database connection details:');
      logger.info(`   Host: ${url.hostname}`);
      logger.info(`   Port: ${url.port || '5432'}`);
      logger.info(`   Database: ${url.pathname.slice(1)}`);
      logger.info(`   Username: ${url.username}`);
      logger.info(`   SSL: ${url.searchParams.get('sslmode') || 'not specified'}`);
    } catch (parseError) {
      throw new Error(`Invalid DATABASE_URL format: ${parseError.message}`);
    }
    
    // 3. Test database connection
    logger.info('🔌 Testing database connection...');
    const result = await sql`SELECT version() as version, current_database() as database, current_user as user`;
    
    if (result.length > 0) {
      const info = result[0];
      logger.info('✅ Database connection successful!');
      logger.info(`   PostgreSQL Version: ${info.version}`);
      logger.info(`   Connected Database: ${info.database}`);
      logger.info(`   Connected User: ${info.user}`);
    }
    
    // 4. Test Drizzle ORM connection
    logger.info('🔧 Testing Drizzle ORM connection...');
    const tables = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    logger.info(`✅ Drizzle ORM connection successful! Found ${tables.length} tables:`);
    tables.forEach((table: any) => {
      logger.info(`   - ${table.table_name}`);
    });
    
    // 5. Check for required tables
    const requiredTables = ['users', 'cases', 'roles', 'permissions'];
    const existingTables = tables.map((t: any) => t.table_name);
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));
    
    if (missingTables.length > 0) {
      logger.warn(`⚠️  Missing required tables: ${missingTables.join(', ')}`);
      logger.info('   Run migrations to create missing tables: npm run db:migrate');
    } else {
      logger.info('✅ All required tables are present');
    }
    
    // 6. Test a simple query
    logger.info('📝 Testing a simple query...');
    const userCount = await db.execute(sql`SELECT COUNT(*) as count FROM users`);
    logger.info(`✅ Query successful! Found ${userCount[0].count} users in the database`);
    
    logger.info('');
    logger.info('🎉 Database configuration validation completed successfully!');
    logger.info('');
    logger.info('Summary:');
    logger.info('✅ DATABASE_URL is properly configured');
    logger.info('✅ Database connection is working');
    logger.info('✅ Drizzle ORM is functioning correctly');
    logger.info(`✅ Found ${tables.length} database tables`);
    logger.info(`✅ Found ${userCount[0].count} users`);
    
  } catch (error) {
    logger.error('❌ Database configuration validation failed:');
    logger.error(error.message);
    
    // Provide helpful troubleshooting tips
    logger.info('');
    logger.info('🔧 Troubleshooting tips:');
    logger.info('1. Check that DATABASE_URL is set in your .env file');
    logger.info('2. Verify the database server is running');
    logger.info('3. Ensure the database exists and credentials are correct');
    logger.info('4. Run migrations if tables are missing: npm run db:migrate');
    logger.info('5. Check network connectivity to the database host');
    
    process.exit(1);
  } finally {
    // Close the database connection
    await sql.end();
  }
}

// Run the validation
validateDatabaseConfig().catch(console.error);
