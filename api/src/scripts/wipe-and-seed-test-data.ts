#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to wipe and reseed the database with deterministic test data
 * 
 * This script will:
 * 1. Wipe all existing data from tables
 * 2. Create deterministic users (patients 0-9, doctors 0-9, admin)
 * 3. Create realistic medical cases with proper assignments
 * 4. Create case notes with appropriate content
 */

import { db } from '../db/index';
import { users, userProfiles, auditLogs } from '../db/schema/users';
import { cases } from '../db/schema/cases';
import { caseDoctors } from '../db/schema/case-doctors';
import { caseNotes } from '../db/schema/case-notes';
import { noteTypes } from '../db/schema/note-types';
import { eq, and } from 'drizzle-orm';
import { randomUUID } from 'crypto';

import { logger } from '../utils/structuredLogger';
// Deterministic test data
const TEST_PATIENTS = [
  { email: '<EMAIL>', firstName: '<PERSON>', lastName: 'Patient<PERSON><PERSON>', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Jane', lastName: 'PatientOne', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Bob', lastName: 'PatientTwo', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Alice', lastName: 'PatientThree', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Charlie', lastName: 'PatientFour', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Diana', lastName: 'PatientFive', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Eve', lastName: 'PatientSix', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Frank', lastName: 'PatientSeven', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Grace', lastName: 'PatientEight', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Henry', lastName: 'PatientNine', password: 'password123' }
];

const TEST_DOCTORS = [
  { email: '<EMAIL>', firstName: 'Dr. Alan', lastName: 'DoctorZero', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Dr. Betty', lastName: 'DoctorOne', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Dr. Charles', lastName: 'DoctorTwo', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Dr. Diana', lastName: 'DoctorThree', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Dr. Edward', lastName: 'DoctorFour', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Dr. Fiona', lastName: 'DoctorFive', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Dr. George', lastName: 'DoctorSix', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Dr. Helen', lastName: 'DoctorSeven', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Dr. Ian', lastName: 'DoctorEight', password: 'password123' },
  { email: '<EMAIL>', firstName: 'Dr. Julia', lastName: 'DoctorNine', password: 'password123' }
];

const ADMIN_USER = {
  email: '<EMAIL>',
  firstName: 'Admin',
  lastName: 'User',
  password: 'password123'
};

// Simple password hashing function (in a real app, use bcrypt)
function hashPassword(password: string): string {
  // This is just for test data - in a real app, use proper bcrypt
  return `hashed_${password}`;
}

async function wipeAndSeedDatabase() {
  logger.debug('Starting database wipe and seed process...');
  
  try {
    // 1. Wipe all existing data
    logger.debug('Wiping existing data...');
    
    // Delete in correct order to respect foreign key constraints
    await db.delete(caseNotes);
    await db.delete(caseDoctors);
    await db.delete(cases);
    await db.delete(noteTypes);
    await db.delete(userProfiles);
    await db.delete(auditLogs);
    await db.delete(users);
    
    logger.debug('Data wipe completed.');
    
    // 2. Create deterministic users
    logger.info('Creating deterministic users...', { requestId: 'context-needed' }, { data: undefined });
    
    // Create patients
    const patientIds: string[] = [];
    for (const patient of TEST_PATIENTS) {
      const userId = randomUUID();
      await db.insert(users).values({
        id: userId,
        email: patient.email,
        passwordHash: hashPassword(patient.password),
        firstName: patient.firstName,
        lastName: patient.lastName,
        role: 'patient',
        isActive: true,
        isEmailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      // Create a basic profile for each patient
      await db.insert(userProfiles).values({
        id: randomUUID(),
        userId: userId,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      patientIds.push(userId);
      logger.debug(`Created patient: ${patient.firstName} ${patient.lastName}`);
    }
    
    // Create doctors
    const doctorIds: string[] = [];
    for (const doctor of TEST_DOCTORS) {
      const userId = randomUUID();
      await db.insert(users).values({
        id: userId,
        email: doctor.email,
        passwordHash: hashPassword(doctor.password),
        firstName: doctor.firstName,
        lastName: doctor.lastName,
        role: 'doctor',
        isActive: true,
        isEmailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      // Create a basic profile for each doctor
      await db.insert(userProfiles).values({
        id: randomUUID(),
        userId: userId,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      doctorIds.push(userId);
      logger.debug(`Created doctor: ${doctor.firstName} ${doctor.lastName}`);
    }
    
    // Create admin user
    const adminId = randomUUID();
    await db.insert(users).values({
      id: adminId,
      email: ADMIN_USER.email,
      passwordHash: hashPassword(ADMIN_USER.password),
      firstName: ADMIN_USER.firstName,
      lastName: ADMIN_USER.lastName,
      role: 'admin',
      isActive: true,
      isEmailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create a basic profile for admin
    await db.insert(userProfiles).values({
      id: randomUUID(),
      userId: adminId,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    logger.debug(`Created admin: ${ADMIN_USER.firstName} ${ADMIN_USER.lastName}`);
    
    logger.info('User creation completed.', { requestId: 'context-needed' }, { data: undefined });
    
    // 3. Create default note types for doctors
    logger.debug('Creating default note types...');
    
    // Import the default note types from the schema
    const { defaultNoteTypes } = await import('../db/schema/note-types.js');
    
    const createdNoteTypes = [];
    for (const noteType of defaultNoteTypes) {
      const [created] = await db.insert(noteTypes).values({
        key: noteType.key,
        name: noteType.name,
        description: noteType.description,
        icon: noteType.icon || 'FileText',
        color: noteType.color || 'blue',
        category: noteType.category || 'clinical',
        allowedRoles: noteType.allowedRoles,
        requiresDoctor: noteType.requiresDoctor || false,
        showInSidebar: noteType.showInSidebar || false,
        placeholder: noteType.placeholder,
        aiEnabled: noteType.aiEnabled || false,
        aiModel: noteType.aiModel,
        aiPrompt: noteType.aiPrompt,
        sortOrder: noteType.sortOrder || 0,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }).returning();
      
      createdNoteTypes.push(created);
      logger.debug(`Created note type: ${noteType.name}`);
    }
    
    logger.debug('Note types creation completed.');
    
    // 4. Create realistic medical cases
    logger.debug('Creating medical cases...');
    
    // Get the clinical notes type we just created
    const clinicalNoteType = createdNoteTypes.find(nt => nt.key === 'clinical_notes');
    if (!clinicalNoteType) {
      throw new Error('Clinical note type not found');
    }
    const clinicalNoteTypeId = clinicalNoteType.id;
    
    // Create 100+ cases with realistic titles and assignments
    const caseIds: string[] = [];
    
    // Sample case titles for variety
    const caseTitles = [
      "Persistent Headaches and Visual Disturbances",
      "Chronic Lower Back Pain with Neurological Symptoms",
      "Acute Abdominal Pain with Nausea",
      "Shortness of Breath and Chest Tightness",
      "Joint Swelling and Morning Stiffness",
      "Recurrent Skin Rashes and Allergic Reactions",
      "Fatigue and Unexplained Weight Loss",
      "Frequent Urinary Tract Infections",
      "Memory Problems and Cognitive Decline",
      "Persistent Cough with Blood-Tinged Sputum",
      "Severe Migraine Episodes",
      "Chronic Fatigue Syndrome",
      "Type 2 Diabetes Management",
      "Hypertension Control Issues",
      "Anxiety and Panic Attacks",
      "Depression and Mood Disorders",
      "Chronic Kidney Disease",
      "Heart Failure Symptoms",
      "Asthma Exacerbation",
      "Gastroesophageal Reflux Disease"
    ];
    
    // Create cases
    for (let i = 0; i < 120; i++) {  // Create 120 cases
      const caseId = randomUUID();
      const patientId = patientIds[i % patientIds.length]; // Rotate through patients
      
      const caseTitle = caseTitles[i % caseTitles.length] + ` - Case #${i + 1}`;
      
      await db.insert(cases).values({
        id: caseId,
        patientId: patientId,
        title: caseTitle,
        urgencyLevel: ['low', 'medium', 'high', 'urgent'][Math.floor(Math.random() * 4)] as any,
        status: ['draft', 'submitted', 'in_review', 'assigned', 'completed'][Math.floor(Math.random() * 5)] as any,
        createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000), // Random date in last 30 days
        updatedAt: new Date()
      });
      
      caseIds.push(caseId);
      
      // Create case notes for each case
      await db.insert(caseNotes).values({
        id: randomUUID(),
        caseId: caseId,
        doctorId: doctorIds[Math.floor(Math.random() * doctorIds.length)], // Random doctor
        noteTypeId: clinicalNoteTypeId,
        structuredContent: {},
        // rawContent field removed
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      logger.debug(`Created case: ${caseTitle}`);
    }
    
    logger.debug('Case creation completed.');
    
    // 4. Create doctor assignments using the case_doctors table
    logger.debug('Creating doctor assignments...');
    
    // Assign doctors to cases (approximately 20% of cases to multiple doctors)
    for (let i = 0; i < caseIds.length; i++) {
      const caseId = caseIds[i];
      
      // Every 5th case gets assigned to multiple doctors
      if (i % 5 === 0) {
        // Assign to 2-3 doctors
        const numDoctors = 2 + Math.floor(Math.random() * 2);
        const assignedDoctors = new Set<string>();
        
        for (let j = 0; j < numDoctors; j++) {
          let doctorId: string;
          do {
            doctorId = doctorIds[Math.floor(Math.random() * doctorIds.length)];
          } while (assignedDoctors.has(doctorId));
          
          assignedDoctors.add(doctorId);
          
          await db.insert(caseDoctors).values({
            id: randomUUID(),
            caseId: caseId,
            doctorId: doctorId,
            assignedAt: new Date(),
            assignedBy: adminId,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          });
          
          logger.debug(`Assigned doctor ${doctorId} to case ${caseId}`);
        }
      } else {
        // Assign to single doctor
        const doctorId = doctorIds[Math.floor(Math.random() * doctorIds.length)];
        
        await db.insert(caseDoctors).values({
          id: randomUUID(),
          caseId: caseId,
          doctorId: doctorId,
          assignedAt: new Date(),
          assignedBy: adminId,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        
        logger.debug(`Assigned doctor ${doctorId} to case ${caseId}`);
      }
    }
    
    logger.debug('Doctor assignments completed.');
    
    logger.debug('Database wipe and seed process completed successfully!');
    
  } catch (error) {
    logger.error('Error during database wipe and seed:', error);
    process.exit(1);
  }
}

// Run the script
// Run the script
wipeAndSeedDatabase().catch(console.error);
