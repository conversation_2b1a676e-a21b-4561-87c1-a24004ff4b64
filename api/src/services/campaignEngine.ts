import { db } from '../db/index.js';
import { 
  campaigns, 
  campaignExecutions, 
  campaignTriggers, 
  campaignAnalytics,
  emailTemplates,
  campaignRules,
  unsubscribeList
} from '../db/schema/campaigns.js';
import { users } from '../db/schema/users.js';
import { leads } from '../db/schema/crm.js';
import { cases } from '../db/schema/cases.js';
import { eq, and, or, sql, inArray, gte, lte, isNull, isNotNull } from 'drizzle-orm';
import { logger } from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

// Email service interface (implement with your preferred provider)
interface EmailService {
  sendEmail(params: {
    to: string;
    subject: string;
    html: string;
    text?: string;
    trackingPixelUrl?: string;
  }): Promise<{ messageId: string; success: boolean; error?: string }>;
}

// Import the real email service
import { EmailServiceFactory, EmailParams, EmailResult } from './emailService.js';

export class CampaignEngine {
  private emailService: EmailService;

  constructor(emailService?: EmailService) {
    this.emailService = emailService || EmailServiceFactory.getInstance();
  }

  /**
   * Process campaign triggers based on events
   */
  async processTrigger(eventType: string, entityType: string, entityId: string, contextData: Record<string, any> = {}) {
    try {
      // Find active campaigns that match this trigger
      const activeCampaigns = await db
        .select()
        .from(campaigns)
        .where(
          and(
            eq(campaigns.status, 'Active'),
            eq(campaigns.trigger_type, this.mapEventToTriggerType(eventType))
          )
        );

      for (const campaign of activeCampaigns) {
        // Check if trigger conditions are met
        if (await this.evaluateTriggerConditions(campaign, entityType, entityId, contextData)) {
          // Create campaign trigger record
          await this.createCampaignTrigger(campaign.campaign_id, eventType, entityType, entityId, contextData);
        }
      }
    } catch (error) {
      logger.error('Error processing campaign trigger', { eventType, entityType, entityId, error });
    }
  }

  /**
   * Execute pending campaign triggers
   */
  async executePendingTriggers() {
    try {
      const pendingTriggers = await db
        .select({
          trigger: campaignTriggers,
          campaign: campaigns,
          template: emailTemplates,
          rule: campaignRules,
        })
        .from(campaignTriggers)
        .leftJoin(campaigns, eq(campaignTriggers.campaign_id, campaigns.campaign_id))
        .leftJoin(emailTemplates, eq(campaigns.template_id, emailTemplates.template_id))
        .leftJoin(campaignRules, eq(campaigns.rule_id, campaignRules.rule_id))
        .where(
          and(
            eq(campaignTriggers.is_processed, false),
            lte(campaignTriggers.scheduled_at, new Date())
          )
        );

      for (const triggerData of pendingTriggers) {
        await this.executeCampaignTrigger(triggerData);
      }
    } catch (error) {
      logger.error('Error executing pending triggers', { error });
    }
  }

  /**
   * Execute a specific campaign trigger
   */
  private async executeCampaignTrigger(triggerData: any) {
    const { trigger, campaign, template, rule } = triggerData;

    try {
      // Get target audience based on campaign configuration
      const audience = await this.getTargetAudience(campaign, trigger.entity_type, trigger.entity_id);

      // Apply campaign rules if defined
      let filteredAudience = audience;
      if (rule) {
        filteredAudience = await this.applyOpalRules(rule.rule_definition, audience, trigger.context_data);
      }

      // Create executions for each recipient
      for (const recipient of filteredAudience) {
        await this.createCampaignExecution(campaign, template, recipient, trigger);
      }

      // Mark trigger as processed
      await db
        .update(campaignTriggers)
        .set({ 
          is_processed: true, 
          executed_at: new Date(),
          updated_at: new Date()
        })
        .where(eq(campaignTriggers.trigger_id, trigger.trigger_id));

    } catch (error) {
      logger.error('Error executing campaign trigger', { triggerId: trigger.trigger_id, error });
      
      // Update trigger with error
      await db
        .update(campaignTriggers)
        .set({ 
          error_message: error.message,
          updated_at: new Date()
        })
        .where(eq(campaignTriggers.trigger_id, trigger.trigger_id));
    }
  }

  /**
   * Apply OPAL-style rules to filter audience
   */
  private async applyOpalRules(ruleDefinition: any, audience: any[], contextData: Record<string, any>) {
    const filteredAudience = [];

    for (const recipient of audience) {
      if (await this.evaluateOpalConditions(ruleDefinition.conditions, recipient, contextData)) {
        filteredAudience.push(recipient);
      }
    }

    return filteredAudience;
  }

  /**
   * Evaluate OPAL-style conditions
   */
  private async evaluateOpalConditions(conditions: any[], recipient: any, contextData: Record<string, any>): Promise<boolean> {
    if (!conditions || conditions.length === 0) return true;

    let result = true;
    let currentLogicalOperator = 'AND';

    for (const condition of conditions) {
      const conditionResult = await this.evaluateCondition(condition, recipient, contextData);
      
      if (currentLogicalOperator === 'AND') {
        result = result && conditionResult;
      } else {
        result = result || conditionResult;
      }

      currentLogicalOperator = condition.logical_operator || 'AND';
    }

    return result;
  }

  /**
   * Evaluate individual condition
   */
  private async evaluateCondition(condition: any, recipient: any, contextData: Record<string, any>): Promise<boolean> {
    const { field, operator, value } = condition;
    
    // Get field value from recipient or context
    let fieldValue = this.getFieldValue(field, recipient, contextData);

    switch (operator) {
      case 'equals':
        return fieldValue === value;
      case 'not_equals':
        return fieldValue !== value;
      case 'contains':
        return String(fieldValue).toLowerCase().includes(String(value).toLowerCase());
      case 'not_contains':
        return !String(fieldValue).toLowerCase().includes(String(value).toLowerCase());
      case 'greater_than':
        return Number(fieldValue) > Number(value);
      case 'less_than':
        return Number(fieldValue) < Number(value);
      case 'in':
        return Array.isArray(value) && value.includes(fieldValue);
      case 'not_in':
        return Array.isArray(value) && !value.includes(fieldValue);
      case 'exists':
        return fieldValue !== null && fieldValue !== undefined;
      case 'not_exists':
        return fieldValue === null || fieldValue === undefined;
      default:
        return true;
    }
  }

  /**
   * Get field value from recipient or context data
   */
  private getFieldValue(field: string, recipient: any, contextData: Record<string, any>): any {
    // Support nested field access with dot notation
    const fieldParts = field.split('.');
    
    // Try recipient first
    let value = recipient;
    for (const part of fieldParts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        value = undefined;
        break;
      }
    }

    // If not found in recipient, try context data
    if (value === undefined) {
      value = contextData;
      for (const part of fieldParts) {
        if (value && typeof value === 'object' && part in value) {
          value = value[part];
        } else {
          value = undefined;
          break;
        }
      }
    }

    return value;
  }

  /**
   * Get target audience for campaign
   */
  private async getTargetAudience(campaign: any, entityType?: string, entityId?: string) {
    let baseQuery;

    // Base audience selection
    switch (campaign.audience_type) {
      case 'AllUsers':
        baseQuery = db.select().from(users);
        break;
      case 'Doctors':
        baseQuery = db.select().from(users).where(eq(users.role, 'doctor'));
        break;
      case 'Patients':
        baseQuery = db.select().from(users).where(eq(users.role, 'patient'));
        break;
      case 'Agents':
        baseQuery = db.select().from(users).where(eq(users.role, 'agent'));
        break;
      case 'Admins':
        baseQuery = db.select().from(users).where(eq(users.role, 'admin'));
        break;
      case 'Custom':
        // Apply custom audience rules
        baseQuery = await this.buildCustomAudienceQuery(campaign.audience_rules);
        break;
      default:
        baseQuery = db.select().from(users);
    }

    // If this is triggered by a specific entity, include that context
    if (entityType && entityId) {
      // Add entity-specific filtering logic here
      // For example, if triggered by a case closure, include case-related users
    }

    const audience = await baseQuery;

    // Filter out unsubscribed users
    const unsubscribedEmails = await db
      .select({ email: unsubscribeList.email })
      .from(unsubscribeList)
      .where(
        or(
          eq(unsubscribeList.unsubscribe_all, true),
          sql`${unsubscribeList.unsubscribe_categories} @> ${JSON.stringify([campaign.type])}`
        )
      );

    const unsubscribedEmailSet = new Set(unsubscribedEmails.map(u => u.email));

    return audience.filter(user => !unsubscribedEmailSet.has(user.email));
  }

  /**
   * Build custom audience query based on rules
   */
  private async buildCustomAudienceQuery(audienceRules: any) {
    // This would build a dynamic query based on the audience rules
    // For now, return all users as a fallback
    return db.select().from(users);
  }

  /**
   * Create campaign execution record
   */
  private async createCampaignExecution(campaign: any, template: any, recipient: any, trigger: any) {
    try {
      // Check if user is unsubscribed
      const isUnsubscribed = await this.isUserUnsubscribed(recipient.email, campaign.type);
      if (isUnsubscribed) {
        logger.info('Skipping unsubscribed user', { email: recipient.email, campaignId: campaign.campaign_id });
        return;
      }

      // Render email content with template variables
      const renderedContent = await this.renderEmailTemplate(template, recipient, trigger.context_data);

      // Generate tracking pixel URL
      const trackingPixelUrl = campaign.track_opens 
        ? `${process.env.API_BASE_URL}/campaigns/track/open/${uuidv4()}`
        : undefined;

      // Create execution record
      const [execution] = await db
        .insert(campaignExecutions)
        .values({
          campaign_id: campaign.campaign_id,
          recipient_user_id: recipient.id,
          recipient_email: recipient.email,
          recipient_name: `${recipient.firstName} ${recipient.lastName}`,
          related_lead_id: trigger.entity_type === 'lead' ? trigger.entity_id : null,
          related_case_id: trigger.entity_type === 'case' ? trigger.entity_id : null,
          status: 'Pending',
          scheduled_at: new Date(),
          subject: renderedContent.subject,
          html_content: renderedContent.html,
          text_content: renderedContent.text,
          tracking_pixel_url: trackingPixelUrl,
        })
        .returning();

      // Send email immediately or queue for later
      await this.sendCampaignEmail(execution);

    } catch (error) {
      logger.error('Error creating campaign execution', { 
        campaignId: campaign.campaign_id, 
        recipientEmail: recipient.email, 
        error 
      });
    }
  }

  /**
   * Send campaign email
   */
  private async sendCampaignEmail(execution: any) {
    try {
      const result = await this.emailService.sendEmail({
        to: execution.recipient_email,
        subject: execution.subject,
        html: execution.html_content,
        text: execution.text_content,
        trackingPixelUrl: execution.tracking_pixel_url,
      });

      // Update execution status
      await db
        .update(campaignExecutions)
        .set({
          status: result.success ? 'Sent' : 'Failed',
          sent_at: result.success ? new Date() : null,
          error_message: result.error || null,
          updated_at: new Date(),
        })
        .where(eq(campaignExecutions.execution_id, execution.execution_id));

      // Update campaign analytics
      await this.updateCampaignAnalytics(execution.campaign_id);

    } catch (error) {
      logger.error('Error sending campaign email', { executionId: execution.execution_id, error });
      
      await db
        .update(campaignExecutions)
        .set({
          status: 'Failed',
          error_message: error.message,
          updated_at: new Date(),
        })
        .where(eq(campaignExecutions.execution_id, execution.execution_id));
    }
  }

  /**
   * Render email template with variables
   */
  private async renderEmailTemplate(template: any, recipient: any, contextData: Record<string, any>) {
    const variables = {
      user: recipient,
      ...contextData,
      unsubscribe_url: `${process.env.API_BASE_URL}/campaigns/unsubscribe/${uuidv4()}`,
    };

    // Simple template variable replacement
    let subject = template.subject;
    let html = template.html_content;
    let text = template.text_content || '';

    // Replace variables in format {{variable.path}}
    const variableRegex = /\{\{([^}]+)\}\}/g;
    
    subject = subject.replace(variableRegex, (match: string, path: string) => {
      return this.getNestedValue(variables, path.trim()) || match;
    });

    html = html.replace(variableRegex, (match: string, path: string) => {
      return this.getNestedValue(variables, path.trim()) || match;
    });

    if (text) {
      text = text.replace(variableRegex, (match: string, path: string) => {
        return this.getNestedValue(variables, path.trim()) || match;
      });
    }

    return { subject, html, text };
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Check if user is unsubscribed
   */
  private async isUserUnsubscribed(email: string, campaignType: string): Promise<boolean> {
    const unsubscribe = await db
      .select()
      .from(unsubscribeList)
      .where(eq(unsubscribeList.email, email))
      .limit(1);

    if (unsubscribe.length === 0) return false;

    const entry = unsubscribe[0];
    return entry.unsubscribe_all || (entry.unsubscribe_categories || []).includes(campaignType);
  }

  /**
   * Update campaign analytics
   */
  private async updateCampaignAnalytics(campaignId: string) {
    try {
      // Get current stats
      const stats = await db
        .select({
          total_sent: sql<number>`count(case when status = 'Sent' then 1 end)`,
          total_delivered: sql<number>`count(case when status = 'Delivered' then 1 end)`,
          total_bounced: sql<number>`count(case when status = 'Bounced' then 1 end)`,
          total_opened: sql<number>`count(case when open_count > 0 then 1 end)`,
          total_clicked: sql<number>`count(case when click_count > 0 then 1 end)`,
          total_unsubscribed: sql<number>`count(case when unsubscribed_at is not null then 1 end)`,
        })
        .from(campaignExecutions)
        .where(eq(campaignExecutions.campaign_id, campaignId))
        .groupBy(campaignExecutions.campaign_id);

      if (stats.length > 0) {
        const stat = stats[0];
        
        // Calculate rates
        const deliveryRate = stat.total_sent > 0 ? Math.round((stat.total_delivered / stat.total_sent) * 10000) : 0;
        const openRate = stat.total_delivered > 0 ? Math.round((stat.total_opened / stat.total_delivered) * 10000) : 0;
        const clickRate = stat.total_opened > 0 ? Math.round((stat.total_clicked / stat.total_opened) * 10000) : 0;
        const bounceRate = stat.total_sent > 0 ? Math.round((stat.total_bounced / stat.total_sent) * 10000) : 0;
        const unsubscribeRate = stat.total_sent > 0 ? Math.round((stat.total_unsubscribed / stat.total_sent) * 10000) : 0;

        // Upsert analytics record
        await db
          .insert(campaignAnalytics)
          .values({
            campaign_id: campaignId,
            total_sent: stat.total_sent,
            total_delivered: stat.total_delivered,
            total_bounced: stat.total_bounced,
            total_opened: stat.total_opened,
            total_clicked: stat.total_clicked,
            total_unsubscribed: stat.total_unsubscribed,
            delivery_rate: deliveryRate,
            open_rate: openRate,
            click_rate: clickRate,
            bounce_rate: bounceRate,
            unsubscribe_rate: unsubscribeRate,
            last_updated_at: new Date(),
          })
          .onConflictDoUpdate({
            target: campaignAnalytics.campaign_id,
            set: {
              total_sent: stat.total_sent,
              total_delivered: stat.total_delivered,
              total_bounced: stat.total_bounced,
              total_opened: stat.total_opened,
              total_clicked: stat.total_clicked,
              total_unsubscribed: stat.total_unsubscribed,
              delivery_rate: deliveryRate,
              open_rate: openRate,
              click_rate: clickRate,
              bounce_rate: bounceRate,
              unsubscribe_rate: unsubscribeRate,
              last_updated_at: new Date(),
              updated_at: new Date(),
            }
          });
      }
    } catch (error) {
      logger.error('Error updating campaign analytics', { campaignId, error });
    }
  }

  /**
   * Map event type to trigger type
   */
  private mapEventToTriggerType(eventType: string): 'Manual' | 'UserRegistration' | 'CaseClosed' | 'ContactCreated' | 'CaseAging' | 'LeadStatusChange' | 'AppointmentScheduled' | 'DocumentUploaded' | 'Custom' {
    const mapping: Record<string, 'Manual' | 'UserRegistration' | 'CaseClosed' | 'ContactCreated' | 'CaseAging' | 'LeadStatusChange' | 'AppointmentScheduled' | 'DocumentUploaded' | 'Custom'> = {
      'user.registered': 'UserRegistration',
      'case.closed': 'CaseClosed',
      'contact.created': 'ContactCreated',
      'case.aging': 'CaseAging',
      'lead.status_changed': 'LeadStatusChange',
      'appointment.scheduled': 'AppointmentScheduled',
      'document.uploaded': 'DocumentUploaded',
    };
    return mapping[eventType] || 'Custom';
  }

  /**
   * Evaluate trigger conditions
   */
  private async evaluateTriggerConditions(
    campaign: any,
    entityType: string,
    entityId: string,
    contextData: Record<string, any>
  ): Promise<boolean> {
    if (!campaign.trigger_config?.conditions) return true;

    // Simple condition evaluation - can be expanded
    const conditions = campaign.trigger_config.conditions;
    
    for (const [key, value] of Object.entries(conditions)) {
      if (contextData[key] !== value) {
        return false;
      }
    }

    return true;
  }

  /**
   * Create campaign trigger
   */
  private async createCampaignTrigger(
    campaignId: string,
    eventType: string,
    entityType: string,
    entityId: string,
    contextData: Record<string, any>
  ) {
    const campaign = await db
      .select()
      .from(campaigns)
      .where(eq(campaigns.campaign_id, campaignId))
      .limit(1);

    if (campaign.length === 0) return;

    const delayMinutes = campaign[0].trigger_config?.delay_minutes || 0;
    const scheduledAt = new Date(Date.now() + delayMinutes * 60 * 1000);

    await db
      .insert(campaignTriggers)
      .values({
        campaign_id: campaignId,
        trigger_event: eventType,
        entity_type: entityType,
        entity_id: entityId,
        scheduled_at: scheduledAt,
        context_data: contextData,
      });
  }

  /**
   * Track email open
   */
  async trackEmailOpen(executionId: string) {
    try {
      await db
        .update(campaignExecutions)
        .set({
          status: 'Opened',
          opened_at: new Date(),
          open_count: sql`${campaignExecutions.open_count} + 1`,
          updated_at: new Date(),
        })
        .where(eq(campaignExecutions.execution_id, executionId));

      // Update campaign analytics
      const execution = await db
        .select({ campaign_id: campaignExecutions.campaign_id })
        .from(campaignExecutions)
        .where(eq(campaignExecutions.execution_id, executionId))
        .limit(1);

      if (execution.length > 0) {
        await this.updateCampaignAnalytics(execution[0].campaign_id);
      }
    } catch (error) {
      logger.error('Error tracking email open', { executionId, error });
    }
  }

  /**
   * Track email click
   */
  async trackEmailClick(executionId: string, clickUrl: string) {
    try {
      await db
        .update(campaignExecutions)
        .set({
          clicked_at: new Date(),
          click_count: sql`${campaignExecutions.click_count} + 1`,
          updated_at: new Date(),
        })
        .where(eq(campaignExecutions.execution_id, executionId));

      // Update campaign analytics
      const execution = await db
        .select({ campaign_id: campaignExecutions.campaign_id })
        .from(campaignExecutions)
        .where(eq(campaignExecutions.execution_id, executionId))
        .limit(1);

      if (execution.length > 0) {
        await this.updateCampaignAnalytics(execution[0].campaign_id);
      }

      logger.info('Email click tracked', { executionId, clickUrl });
    } catch (error) {
      logger.error('Error tracking email click', { executionId, error });
    }
  }

  /**
   * Handle unsubscribe
   */
  async handleUnsubscribe(token: string, reason?: string) {
    try {
      // Find execution by unsubscribe token (you'd need to store this)
      // For now, we'll create a simple unsubscribe entry
      
      // This would need to be implemented based on how you store unsubscribe tokens
      logger.info('Unsubscribe handled', { token, reason });
    } catch (error) {
      logger.error('Error handling unsubscribe', { token, error });
    }
  }
}

// Export singleton instance
export const campaignEngine = new CampaignEngine();