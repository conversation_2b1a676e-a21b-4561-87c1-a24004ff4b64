import { campaignEngine } from './campaignEngine.js';
import { logger } from '../utils/logger.js';

/**
 * Campaign trigger service - connects application events to campaign engine
 */
export class CampaignTriggerService {
  
  /**
   * Trigger when a new user registers
   */
  static async onUserRegistered(userId: string, userData: {
    email: string;
    firstName: string;
    lastName: string;
    role: string;
  }) {
    try {
      await campaignEngine.processTrigger(
        'user.registered',     // Event type
        'user',               // Entity type
        userId,               // Entity ID
        {                     // Context data for template variables
          user: userData,
          registration_date: new Date().toISOString(),
          welcome_message: `Welcome ${userData.firstName}!`
        }
      );
      
      logger.info('User registration campaign triggered', { userId, email: userData.email });
    } catch (error) {
      logger.error('Failed to trigger user registration campaign', { userId, error });
    }
  }

  /**
   * Trigger when a case is closed
   */
  static async onCaseClosed(caseId: string, caseData: {
    title: string;
    status: string;
    patientId: string;
    doctorId?: string;
  }) {
    try {
      await campaignEngine.processTrigger(
        'case.closed',
        'case',
        caseId,
        {
          case: caseData,
          closure_date: new Date().toISOString(),
        }
      );
      
      logger.info('Case closure campaign triggered', { caseId });
    } catch (error) {
      logger.error('Failed to trigger case closure campaign', { caseId, error });
    }
  }

  /**
   * Trigger when a new contact is created
   */
  static async onContactCreated(contactId: string, contactData: {
    firstName: string;
    lastName: string;
    email?: string;
    organizationId?: string;
  }) {
    try {
      await campaignEngine.processTrigger(
        'contact.created',
        'contact',
        contactId,
        {
          contact: contactData,
          creation_date: new Date().toISOString(),
        }
      );
      
      logger.info('Contact creation campaign triggered', { contactId });
    } catch (error) {
      logger.error('Failed to trigger contact creation campaign', { contactId, error });
    }
  }

  /**
   * Trigger when a case is aging (scheduled job)
   */
  static async onCaseAging(caseId: string, caseData: {
    title: string;
    createdAt: Date;
    daysSinceCreation: number;
    patientId: string;
  }) {
    try {
      await campaignEngine.processTrigger(
        'case.aging',
        'case',
        caseId,
        {
          case: caseData,
          aging_check_date: new Date().toISOString(),
        }
      );
      
      logger.info('Case aging campaign triggered', { caseId, daysSinceCreation: caseData.daysSinceCreation });
    } catch (error) {
      logger.error('Failed to trigger case aging campaign', { caseId, error });
    }
  }

  /**
   * Trigger when an appointment is scheduled
   */
  static async onAppointmentScheduled(appointmentId: string, appointmentData: {
    patientId: string;
    doctorId: string;
    scheduledDate: Date;
    type: string;
  }) {
    try {
      await campaignEngine.processTrigger(
        'appointment.scheduled',
        'appointment',
        appointmentId,
        {
          appointment: appointmentData,
          scheduled_date: appointmentData.scheduledDate.toISOString(),
        }
      );
      
      logger.info('Appointment scheduled campaign triggered', { appointmentId });
    } catch (error) {
      logger.error('Failed to trigger appointment scheduled campaign', { appointmentId, error });
    }
  }

  /**
   * Trigger when a document is uploaded
   */
  static async onDocumentUploaded(documentId: string, documentData: {
    filename: string;
    uploadedBy: string;
    caseId?: string;
    patientId?: string;
  }) {
    try {
      await campaignEngine.processTrigger(
        'document.uploaded',
        'document',
        documentId,
        {
          document: documentData,
          upload_date: new Date().toISOString(),
        }
      );
      
      logger.info('Document upload campaign triggered', { documentId });
    } catch (error) {
      logger.error('Failed to trigger document upload campaign', { documentId, error });
    }
  }

  /**
   * Trigger when lead status changes
   */
  static async onLeadStatusChanged(leadId: string, leadData: {
    oldStatus: string;
    newStatus: string;
    contactId: string;
  }) {
    try {
      await campaignEngine.processTrigger(
        'lead.status_changed',
        'lead',
        leadId,
        {
          lead: leadData,
          status_change_date: new Date().toISOString(),
        }
      );
      
      logger.info('Lead status change campaign triggered', { leadId, oldStatus: leadData.oldStatus, newStatus: leadData.newStatus });
    } catch (error) {
      logger.error('Failed to trigger lead status change campaign', { leadId, error });
    }
  }
}

export default CampaignTriggerService;