import { db } from '../db/index.js';
import {
  emailSequences,
  emailSequenceSteps,
  emailSequenceSubscribers,
  emailSequenceExecutions,
  emailSequenceAnalytics,
  type EmailSequence,
  type EmailSequenceStep,
  type EmailSequenceSubscriber,
  type NewEmailSequenceSubscriber,
  type NewEmailSequenceExecution
} from '../db/schema/email-sequences.js';
import {
  campaigns,
  campaignExecutions,
  emailTemplates,
  type Campaign
} from '../db/schema/campaigns.js';
import { users } from '../db/schema/users.js';
import { leads } from '../db/schema/crm.js';
import { cases } from '../db/schema/cases.js';
import { eq, and, or, sql, inArray, gte, lte, isNull, isNotNull, lt, desc, asc } from 'drizzle-orm';
import { EmailServiceFactory } from './emailService.js';
import { logAuditEvent } from '../utils/logger.js';

import { logger } from '../utils/structuredLogger';
export class SequenceEngine {
  private static instance: SequenceEngine;
  private isProcessing = false;
  private processingInterval: NodeJS.Timeout | null = null;

  private constructor() {}

  public static getInstance(): SequenceEngine {
    if (!SequenceEngine.instance) {
      SequenceEngine.instance = new SequenceEngine();
    }
    return SequenceEngine.instance;
  }

  /**
   * Start the sequence processing engine
   */
  public startEngine(intervalMinutes: number = 5): void {
    if (this.processingInterval) {
      logger.debug('Sequence engine is already running');
      return;
    }

    logger.debug(`Starting sequence engine with ${intervalMinutes} minute intervals`);
    
    // Process immediately on start
    this.processScheduledExecutions();
    
    // Set up recurring processing
    this.processingInterval = setInterval(() => {
      this.processScheduledExecutions();
    }, intervalMinutes * 60 * 1000);
  }

  /**
   * Stop the sequence processing engine
   */
  public stopEngine(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      logger.debug('Sequence engine stopped');
    }
  }

  /**
   * Add a subscriber to an email sequence
   */
  public async addSubscriber(
    sequenceId: string,
    subscriberData: {
      userId?: string;
      email: string;
      name?: string;
      contextData?: Record<string, any>;
    }
  ): Promise<string> {
    try {
      // Check if sequence exists and is active
      const [sequence] = await db
        .select()
        .from(emailSequences)
        .where(and(
          eq(emailSequences.sequence_id, sequenceId),
          eq(emailSequences.status, 'Active')
        ));

      if (!sequence) {
        throw new Error('Sequence not found or not active');
      }

      // Check if subscriber already exists
      const [existingSubscriber] = await db
        .select()
        .from(emailSequenceSubscribers)
        .where(and(
          eq(emailSequenceSubscribers.sequence_id, sequenceId),
          eq(emailSequenceSubscribers.email, subscriberData.email),
          eq(emailSequenceSubscribers.status, 'active')
        ));

      if (existingSubscriber) {
        return existingSubscriber.subscriber_id;
      }

      // Get first step of the sequence
      const [firstStep] = await db
        .select()
        .from(emailSequenceSteps)
        .where(and(
          eq(emailSequenceSteps.sequence_id, sequenceId),
          eq(emailSequenceSteps.is_active, true)
        ))
        .orderBy(asc(emailSequenceSteps.step_order))
        .limit(1);

      if (!firstStep) {
        throw new Error('No active steps found in sequence');
      }

      // Calculate next execution time
      const nextExecutionAt = this.calculateNextExecutionTime(
        new Date(),
        firstStep,
        sequence
      );

      // Create subscriber
      const [newSubscriber] = await db
        .insert(emailSequenceSubscribers)
        .values({
          sequence_id: sequenceId,
          user_id: subscriberData.userId,
          email: subscriberData.email,
          name: subscriberData.name,
          current_step_id: firstStep.step_id,
          current_step_order: firstStep.step_order,
          next_execution_at: nextExecutionAt,
          context_data: subscriberData.contextData || {}
        })
        .returning();

      // Update sequence subscriber count
      await db
        .update(emailSequences)
        .set({
          total_subscribers: sql`${emailSequences.total_subscribers} + 1`,
          active_subscribers: sql`${emailSequences.active_subscribers} + 1`,
          updated_at: new Date()
        })
        .where(eq(emailSequences.sequence_id, sequenceId));

      // Schedule first execution
      await this.scheduleStepExecution(newSubscriber.subscriber_id, firstStep.step_id, nextExecutionAt);

      logger.debug(`Added subscriber ${subscriberData.email} to sequence ${sequence.name}`);
      return newSubscriber.subscriber_id;

    } catch (error) {
      logger.error('Error adding subscriber to sequence:', error);
      throw error;
    }
  }

  /**
   * Process all scheduled executions that are due
   */
  public async processScheduledExecutions(): Promise<void> {
    if (this.isProcessing) {
      logger.debug('Sequence processing already in progress, skipping...');
      return;
    }

    this.isProcessing = true;
    logger.debug('Processing scheduled sequence executions...');

    try {
      // Get all executions that are due for processing
      const dueExecutions = await db
        .select({
          execution: emailSequenceExecutions,
          subscriber: emailSequenceSubscribers,
          step: emailSequenceSteps,
          sequence: emailSequences
        })
        .from(emailSequenceExecutions)
        .innerJoin(emailSequenceSubscribers, eq(emailSequenceExecutions.subscriber_id, emailSequenceSubscribers.subscriber_id))
        .innerJoin(emailSequenceSteps, eq(emailSequenceExecutions.step_id, emailSequenceSteps.step_id))
        .innerJoin(emailSequences, eq(emailSequenceSteps.sequence_id, emailSequences.sequence_id))
        .where(and(
          eq(emailSequenceExecutions.status, 'Pending'),
          lte(emailSequenceExecutions.scheduled_at, new Date()),
          eq(emailSequenceSubscribers.status, 'active'),
          eq(emailSequences.status, 'Active')
        ))
        .orderBy(asc(emailSequenceExecutions.scheduled_at));

      logger.debug(`Found ${dueExecutions.length} executions to process`);

      for (const execution of dueExecutions) {
        await this.processStepExecution(execution);
      }

    } catch (error) {
      logger.error('Error processing scheduled executions:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process a single step execution
   */
  private async processStepExecution(execution: {
    execution: any;
    subscriber: any;
    step: any;
    sequence: any;
  }): Promise<void> {
    try {
      const { execution: exec, subscriber, step, sequence } = execution;

      logger.debug(`Processing ${step.step_type} step for subscriber ${subscriber.email}`);

      // Update execution status to processing
      await db
        .update(emailSequenceExecutions)
        .set({
          status: 'Scheduled',
          updated_at: new Date()
        })
        .where(eq(emailSequenceExecutions.execution_id, exec.execution_id));

      let success = false;
      let errorMessage = '';
      let resultData = {};

      switch (step.step_type) {
        case 'Email':
          ({ success, errorMessage, resultData } = await this.processEmailStep(exec, subscriber, step, sequence));
          break;
        case 'Wait':
          ({ success, errorMessage, resultData } = await this.processWaitStep(exec, subscriber, step, sequence));
          break;
        case 'Condition':
          ({ success, errorMessage, resultData } = await this.processConditionStep(exec, subscriber, step, sequence));
          break;
        case 'Action':
          ({ success, errorMessage, resultData } = await this.processActionStep(exec, subscriber, step, sequence));
          break;
        default:
          errorMessage = `Unknown step type: ${step.step_type}`;
      }

      // Update execution status
      await db
        .update(emailSequenceExecutions)
        .set({
          status: success ? 'Completed' : 'Failed',
          executed_at: new Date(),
          error_message: errorMessage || null,
          result_data: resultData,
          updated_at: new Date()
        })
        .where(eq(emailSequenceExecutions.execution_id, exec.execution_id));

      if (success) {
        // Move to next step
        await this.moveToNextStep(subscriber, step, sequence);
      } else {
        // Handle retry logic
        await this.handleExecutionFailure(exec, errorMessage);
      }

    } catch (error) {
      logger.error('Error processing step execution:', error);
      
      // Mark execution as failed
      await db
        .update(emailSequenceExecutions)
        .set({
          status: 'Failed',
          executed_at: new Date(),
          error_message: error instanceof Error ? error.message : 'Unknown error',
          retry_count: sql`${emailSequenceExecutions.retry_count} + 1`,
          updated_at: new Date()
        })
        .where(eq(emailSequenceExecutions.execution_id, execution.execution.execution_id));
    }
  }

  /**
   * Process an email step
   */
  private async processEmailStep(
    execution: any,
    subscriber: any,
    step: any,
    sequence: any
  ): Promise<{ success: boolean; errorMessage: string; resultData: any }> {
    try {
      if (!step.template_id) {
        return { success: false, errorMessage: 'No template specified for email step', resultData: {} };
      }

      // Get email template
      const [template] = await db
        .select()
        .from(emailTemplates)
        .where(eq(emailTemplates.template_id, step.template_id));

      if (!template) {
        return { success: false, errorMessage: 'Email template not found', resultData: {} };
      }

      // Render template with subscriber context
      const renderedContent = this.renderTemplate(template, subscriber.context_data);
      const subject = step.subject_override || renderedContent.subject;

      // Send email using email service
      const emailService = EmailServiceFactory.getInstance();
      const emailResult = await emailService.sendEmail({
        to: subscriber.email,
        subject,
        html: renderedContent.html,
        text: renderedContent.text,
        headers: {
          'X-Sequence-ID': sequence.sequence_id,
          'X-Subscriber-ID': subscriber.subscriber_id,
          'X-Step-ID': step.step_id
        }
      });

      // Update subscriber email stats
      await db
        .update(emailSequenceSubscribers)
        .set({
          emails_sent: sql`${emailSequenceSubscribers.emails_sent} + 1`,
          updated_at: new Date()
        })
        .where(eq(emailSequenceSubscribers.subscriber_id, subscriber.subscriber_id));

      // Update execution with email details
      await db
        .update(emailSequenceExecutions)
        .set({
          email_sent: true,
          email_delivered: emailResult.success,
          updated_at: new Date()
        })
        .where(eq(emailSequenceExecutions.execution_id, execution.execution_id));

      return {
        success: emailResult.success,
        errorMessage: emailResult.error || '',
        resultData: {
          messageId: emailResult.messageId,
          subject,
          recipient: subscriber.email
        }
      };

    } catch (error) {
      return {
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error sending email',
        resultData: {}
      };
    }
  }

  /**
   * Process a wait step
   */
  private async processWaitStep(
    execution: any,
    subscriber: any,
    step: any,
    sequence: any
  ): Promise<{ success: boolean; errorMessage: string; resultData: any }> {
    // Wait steps are automatically successful - they just introduce delays
    return {
      success: true,
      errorMessage: '',
      resultData: {
        waitType: 'delay',
        delayAmount: step.delay_amount,
        delayUnit: step.delay_unit
      }
    };
  }

  /**
   * Process a condition step
   */
  private async processConditionStep(
    execution: any,
    subscriber: any,
    step: any,
    sequence: any
  ): Promise<{ success: boolean; errorMessage: string; resultData: any }> {
    try {
      if (!step.condition_rules) {
        return { success: false, errorMessage: 'No condition rules defined', resultData: {} };
      }

      const rules = step.condition_rules;
      const conditionResult = await this.evaluateConditions(rules.conditions, subscriber);

      return {
        success: true,
        errorMessage: '',
        resultData: {
          conditionResult,
          nextStepId: conditionResult ? rules.true_step_id : rules.false_step_id
        }
      };

    } catch (error) {
      return {
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Error evaluating condition',
        resultData: {}
      };
    }
  }

  /**
   * Process an action step
   */
  private async processActionStep(
    execution: any,
    subscriber: any,
    step: any,
    sequence: any
  ): Promise<{ success: boolean; errorMessage: string; resultData: any }> {
    try {
      if (!step.action_config) {
        return { success: false, errorMessage: 'No action configuration defined', resultData: {} };
      }

      const action = step.action_config;
      let actionResult = {};

      switch (action.action_type) {
        case 'add_tag':
          // Add tag to subscriber context
          const currentTags = subscriber.context_data.tags || [];
          const newTags = [...currentTags, action.parameters.tag];
          await db
            .update(emailSequenceSubscribers)
            .set({
              context_data: { ...subscriber.context_data, tags: newTags },
              updated_at: new Date()
            })
            .where(eq(emailSequenceSubscribers.subscriber_id, subscriber.subscriber_id));
          actionResult = { addedTag: action.parameters.tag };
          break;

        case 'update_field':
          // Update subscriber context field
          await db
            .update(emailSequenceSubscribers)
            .set({
              context_data: { 
                ...subscriber.context_data, 
                [action.parameters.field]: action.parameters.value 
              },
              updated_at: new Date()
            })
            .where(eq(emailSequenceSubscribers.subscriber_id, subscriber.subscriber_id));
          actionResult = { 
            updatedField: action.parameters.field, 
            newValue: action.parameters.value 
          };
          break;

        case 'move_to_sequence':
          // Move subscriber to another sequence
          if (action.parameters.sequence_id) {
            await this.addSubscriber(action.parameters.sequence_id, {
              userId: subscriber.user_id,
              email: subscriber.email,
              name: subscriber.name,
              contextData: subscriber.context_data
            });
            actionResult = { movedToSequence: action.parameters.sequence_id };
          }
          break;

        default:
          return { 
            success: false, 
            errorMessage: `Unknown action type: ${action.action_type}`, 
            resultData: {} 
          };
      }

      return {
        success: true,
        errorMessage: '',
        resultData: actionResult
      };

    } catch (error) {
      return {
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Error executing action',
        resultData: {}
      };
    }
  }

  /**
   * Move subscriber to the next step in the sequence
   */
  private async moveToNextStep(subscriber: any, currentStep: any, sequence: any): Promise<void> {
    try {
      // Get next step
      const [nextStep] = await db
        .select()
        .from(emailSequenceSteps)
        .where(and(
          eq(emailSequenceSteps.sequence_id, sequence.sequence_id),
          eq(emailSequenceSteps.is_active, true),
          sql`${emailSequenceSteps.step_order} > ${currentStep.step_order}`
        ))
        .orderBy(asc(emailSequenceSteps.step_order))
        .limit(1);

      if (!nextStep) {
        // Sequence completed
        await db
          .update(emailSequenceSubscribers)
          .set({
            status: 'completed',
            completed_at: new Date(),
            updated_at: new Date()
          })
          .where(eq(emailSequenceSubscribers.subscriber_id, subscriber.subscriber_id));

        // Update sequence stats
        await db
          .update(emailSequences)
          .set({
            active_subscribers: sql`${emailSequences.active_subscribers} - 1`,
            completed_subscribers: sql`${emailSequences.completed_subscribers} + 1`,
            updated_at: new Date()
          })
          .where(eq(emailSequences.sequence_id, sequence.sequence_id));

        logger.debug(`Subscriber ${subscriber.email} completed sequence ${sequence.name}`);
        return;
      }

      // Calculate next execution time
      const nextExecutionAt = this.calculateNextExecutionTime(
        new Date(),
        nextStep,
        sequence
      );

      // Update subscriber
      await db
        .update(emailSequenceSubscribers)
        .set({
          current_step_id: nextStep.step_id,
          current_step_order: nextStep.step_order,
          next_execution_at: nextExecutionAt,
          updated_at: new Date()
        })
        .where(eq(emailSequenceSubscribers.subscriber_id, subscriber.subscriber_id));

      // Schedule next execution
      await this.scheduleStepExecution(subscriber.subscriber_id, nextStep.step_id, nextExecutionAt);

    } catch (error) {
      logger.error('Error moving to next step:', error);
      throw error;
    }
  }

  /**
   * Calculate the next execution time based on step delay and sequence settings
   */
  private calculateNextExecutionTime(
    baseTime: Date,
    step: EmailSequenceStep,
    sequence: EmailSequence
  ): Date {
    let nextTime = new Date(baseTime);

    // Add step delay
    switch (step.delay_unit) {
      case 'minutes':
        nextTime.setMinutes(nextTime.getMinutes() + step.delay_amount);
        break;
      case 'hours':
        nextTime.setHours(nextTime.getHours() + step.delay_amount);
        break;
      case 'days':
        nextTime.setDate(nextTime.getDate() + step.delay_amount);
        break;
      case 'weeks':
        nextTime.setDate(nextTime.getDate() + (step.delay_amount * 7));
        break;
    }

    // Apply business hours constraints
    if (sequence.respect_business_hours) {
      nextTime = this.adjustForBusinessHours(nextTime, sequence);
    }

    // Skip weekends if configured
    if (step.skip_weekends || sequence.business_days_only) {
      nextTime = this.adjustForWeekends(nextTime);
    }

    // Apply wait until specific time/day constraints
    if (step.wait_until_time) {
      nextTime = this.adjustForWaitUntilTime(nextTime, step.wait_until_time);
    }

    if (step.wait_until_day) {
      nextTime = this.adjustForWaitUntilDay(nextTime, step.wait_until_day);
    }

    return nextTime;
  }

  /**
   * Adjust time to respect business hours
   */
  private adjustForBusinessHours(time: Date, sequence: EmailSequence): Date {
    const adjusted = new Date(time);
    const startHour = parseInt(sequence.business_hours_start?.split(':')[0] || '9');
    const endHour = parseInt(sequence.business_hours_end?.split(':')[0] || '17');

    if (adjusted.getHours() < startHour) {
      adjusted.setHours(startHour, 0, 0, 0);
    } else if (adjusted.getHours() >= endHour) {
      adjusted.setDate(adjusted.getDate() + 1);
      adjusted.setHours(startHour, 0, 0, 0);
    }

    return adjusted;
  }

  /**
   * Adjust time to skip weekends
   */
  private adjustForWeekends(time: Date): Date {
    const adjusted = new Date(time);
    const dayOfWeek = adjusted.getDay();

    if (dayOfWeek === 0) { // Sunday
      adjusted.setDate(adjusted.getDate() + 1);
    } else if (dayOfWeek === 6) { // Saturday
      adjusted.setDate(adjusted.getDate() + 2);
    }

    return adjusted;
  }

  /**
   * Adjust time to wait until specific time
   */
  private adjustForWaitUntilTime(time: Date, waitTime: string): Date {
    const adjusted = new Date(time);
    const [hours, minutes] = waitTime.split(':').map(Number);
    
    adjusted.setHours(hours, minutes, 0, 0);
    
    // If the time has already passed today, move to tomorrow
    if (adjusted <= time) {
      adjusted.setDate(adjusted.getDate() + 1);
    }

    return adjusted;
  }

  /**
   * Adjust time to wait until specific day
   */
  private adjustForWaitUntilDay(time: Date, waitDay: string): Date {
    const adjusted = new Date(time);
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const targetDay = dayNames.indexOf(waitDay.toLowerCase());
    
    if (targetDay === -1) return adjusted;

    const currentDay = adjusted.getDay();
    const daysToAdd = (targetDay - currentDay + 7) % 7;
    
    if (daysToAdd === 0 && adjusted <= time) {
      adjusted.setDate(adjusted.getDate() + 7); // Next week
    } else {
      adjusted.setDate(adjusted.getDate() + daysToAdd);
    }

    return adjusted;
  }

  /**
   * Schedule a step execution
   */
  private async scheduleStepExecution(
    subscriberId: string,
    stepId: string,
    scheduledAt: Date
  ): Promise<void> {
    await db
      .insert(emailSequenceExecutions)
      .values({
        subscriber_id: subscriberId,
        step_id: stepId,
        scheduled_at: scheduledAt,
        status: 'Pending'
      });
  }

  /**
   * Handle execution failure and retry logic
   */
  private async handleExecutionFailure(execution: any, errorMessage: string): Promise<void> {
    const retryCount = execution.retry_count + 1;
    
    if (retryCount < execution.max_retries) {
      // Schedule retry
      const retryTime = new Date();
      retryTime.setMinutes(retryTime.getMinutes() + (retryCount * 15)); // Exponential backoff

      await db
        .update(emailSequenceExecutions)
        .set({
          status: 'Pending',
          scheduled_at: retryTime,
          retry_count: retryCount,
          updated_at: new Date()
        })
        .where(eq(emailSequenceExecutions.execution_id, execution.execution_id));

      logger.debug(`Scheduled retry ${retryCount} for execution ${execution.execution_id}`);
    } else {
      logger.error('Max retries exceeded for execution ${execution.execution_id}: ${errorMessage}');
    }
  }

  /**
   * Render email template with context data
   */
  private renderTemplate(template: any, contextData: Record<string, any>): {
    subject: string;
    html: string;
    text: string;
  } {
    const renderString = (str: string, data: Record<string, any>): string => {
      return str.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        return data[key] || match;
      });
    };

    return {
      subject: renderString(template.subject, contextData),
      html: renderString(template.html_content, contextData),
      text: renderString(template.text_content || '', contextData)
    };
  }

  /**
   * Evaluate condition rules
   */
  private async evaluateConditions(
    conditions: Array<{
      field: string;
      operator: string;
      value: any;
      logical_operator?: 'AND' | 'OR';
    }>,
    subscriber: any
  ): Promise<boolean> {
    if (!conditions || conditions.length === 0) return true;

    let result = true;
    let currentLogicalOp = 'AND';

    for (const condition of conditions) {
      const fieldValue = this.getFieldValue(condition.field, subscriber);
      const conditionResult = this.evaluateCondition(fieldValue, condition.operator, condition.value);

      if (currentLogicalOp === 'AND') {
        result = result && conditionResult;
      } else {
        result = result || conditionResult;
      }

      currentLogicalOp = condition.logical_operator || 'AND';
    }

    return result;
  }

  /**
   * Get field value from subscriber context
   */
  private getFieldValue(field: string, subscriber: any): any {
    const parts = field.split('.');
    let value = subscriber;

    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * Evaluate a single condition
   */
  private evaluateCondition(fieldValue: any, operator: string, expectedValue: any): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === expectedValue;
      case 'not_equals':
        return fieldValue !== expectedValue;
      case 'contains':
        return String(fieldValue).includes(String(expectedValue));
      case 'not_contains':
        return !String(fieldValue).includes(String(expectedValue));
      case 'greater_than':
        return Number(fieldValue) > Number(expectedValue);
      case 'less_than':
        return Number(fieldValue) < Number(expectedValue);
      case 'in':
        return Array.isArray(expectedValue) && expectedValue.includes(fieldValue);
      case 'not_in':
        return Array.isArray(expectedValue) && !expectedValue.includes(fieldValue);
      case 'exists':
        return fieldValue !== undefined && fieldValue !== null;
      case 'not_exists':
        return fieldValue === undefined || fieldValue === null;
      default:
        return false;
    }
  }

  /**
   * Pause a subscriber in a sequence
   */
  public async pauseSubscriber(subscriberId: string): Promise<void> {
    await db
      .update(emailSequenceSubscribers)
      .set({
        status: 'paused',
        paused_at: new Date(),
        updated_at: new Date()
      })
      .where(eq(emailSequenceSubscribers.subscriber_id, subscriberId));

    // Cancel pending executions
    await db
      .update(emailSequenceExecutions)
      .set({
        status: 'Skipped',
        updated_at: new Date()
      })
      .where(and(
        eq(emailSequenceExecutions.subscriber_id, subscriberId),
        eq(emailSequenceExecutions.status, 'Pending')
      ));
  }

  /**
   * Resume a paused subscriber
   */
  public async resumeSubscriber(subscriberId: string): Promise<void> {
    const [subscriber] = await db
      .select()
      .from(emailSequenceSubscribers)
      .where(eq(emailSequenceSubscribers.subscriber_id, subscriberId));

    if (!subscriber || subscriber.status !== 'paused') {
      throw new Error('Subscriber not found or not paused');
    }

    // Get current step and sequence
    if (!subscriber.current_step_id) {
      throw new Error('Subscriber has no current step');
    }

    const [step] = await db
      .select()
      .from(emailSequenceSteps)
      .where(eq(emailSequenceSteps.step_id, subscriber.current_step_id));

    if (!step) {
      throw new Error('Current step not found');
    }

    const [sequence] = await db
      .select()
      .from(emailSequences)
      .where(eq(emailSequences.sequence_id, step.sequence_id));

    if (!sequence) {
      throw new Error('Sequence not found');
    }

    // Calculate next execution time
    const nextExecutionAt = this.calculateNextExecutionTime(new Date(), step, sequence);

    // Resume subscriber
    await db
      .update(emailSequenceSubscribers)
      .set({
        status: 'active',
        next_execution_at: nextExecutionAt,
        paused_at: null,
        updated_at: new Date()
      })
      .where(eq(emailSequenceSubscribers.subscriber_id, subscriberId));

    // Schedule next execution
    await this.scheduleStepExecution(subscriberId, step.step_id, nextExecutionAt);
  }

  /**
   * Unsubscribe a subscriber from a sequence
   */
  public async unsubscribeSubscriber(subscriberId: string): Promise<void> {
    await db
      .update(emailSequenceSubscribers)
      .set({
        status: 'unsubscribed',
        unsubscribed_at: new Date(),
        updated_at: new Date()
      })
      .where(eq(emailSequenceSubscribers.subscriber_id, subscriberId));

    // Cancel pending executions
    await db
      .update(emailSequenceExecutions)
      .set({
        status: 'Skipped',
        updated_at: new Date()
      })
      .where(and(
        eq(emailSequenceExecutions.subscriber_id, subscriberId),
        eq(emailSequenceExecutions.status, 'Pending')
      ));

    // Update sequence stats
    const [subscriber] = await db
      .select()
      .from(emailSequenceSubscribers)
      .innerJoin(emailSequenceSteps, eq(emailSequenceSubscribers.current_step_id, emailSequenceSteps.step_id))
      .where(eq(emailSequenceSubscribers.subscriber_id, subscriberId));

    if (subscriber) {
      await db
        .update(emailSequences)
        .set({
          active_subscribers: sql`${emailSequences.active_subscribers} - 1`,
          updated_at: new Date()
        })
        .where(eq(emailSequences.sequence_id, subscriber.crm_email_sequence_steps.sequence_id));
    }
  }

  /**
   * Get sequence analytics
   */
  public async getSequenceAnalytics(sequenceId: string): Promise<any> {
    const [analytics] = await db
      .select({
        totalSubscribers: emailSequences.total_subscribers,
        activeSubscribers: emailSequences.active_subscribers,
        completedSubscribers: emailSequences.completed_subscribers,
        totalSent: sql<number>`COALESCE(SUM(${emailSequenceSubscribers.emails_sent}), 0)`,
        totalOpened: sql<number>`COALESCE(SUM(${emailSequenceSubscribers.emails_opened}), 0)`,
        totalClicked: sql<number>`COALESCE(SUM(${emailSequenceSubscribers.emails_clicked}), 0)`
      })
      .from(emailSequences)
      .leftJoin(emailSequenceSubscribers, eq(emailSequences.sequence_id, emailSequenceSubscribers.sequence_id))
      .where(eq(emailSequences.sequence_id, sequenceId))
      .groupBy(
        emailSequences.sequence_id,
        emailSequences.total_subscribers,
        emailSequences.active_subscribers,
        emailSequences.completed_subscribers
      );

    return analytics || {
      totalSubscribers: 0,
      activeSubscribers: 0,
      completedSubscribers: 0,
      totalSent: 0,
      totalOpened: 0,
      totalClicked: 0
    };
  }
}

// Export singleton instance
export const sequenceEngine = SequenceEngine.getInstance();