import { sequenceEngine } from './sequenceEngine.js';
import { db } from '../db/index.js';
import { emailSequences } from '../db/schema/email-sequences.js';
import { eq, and } from 'drizzle-orm';

import { logger } from '../utils/structuredLogger';
/**
 * Service to handle sequence triggers from various application events
 */
export class SequenceTriggerService {
  /**
   * Trigger sequences when a user registers
   */
  static async onUserRegistered(userId: string, userData: {
    email: string;
    firstName?: string;
    lastName?: string;
    role?: string;
  }): Promise<void> {
    try {
      // Find active sequences triggered by user registration
      const sequences = await db
        .select()
        .from(emailSequences)
        .where(and(
          eq(emailSequences.trigger_event, 'user_registration'),
          eq(emailSequences.status, 'Active')
        ));

      for (const sequence of sequences) {
        await sequenceEngine.addSubscriber(sequence.sequence_id, {
          userId,
          email: userData.email,
          name: userData.firstName && userData.lastName 
            ? `${userData.firstName} ${userData.lastName}` 
            : userData.firstName || userData.lastName,
          contextData: {
            firstName: userData.firstName,
            lastName: userData.lastName,
            role: userData.role,
            triggerEvent: 'user_registration',
            registrationDate: new Date().toISOString()
          }
        });
      }

      logger.info('Triggered ${sequences.length} sequences for user registration: ${userData.email}', { requestId: 'context-needed' }, { data: undefined });
    } catch (error) {
      logger.error('Error triggering user registration sequences:', error);
    }
  }

  /**
   * Trigger sequences when a case is closed
   */
  static async onCaseClosed(caseId: string, caseData: {
    patientEmail?: string;
    patientName?: string;
    caseType?: string;
    closureReason?: string;
    doctorId?: string;
  }): Promise<void> {
    try {
      if (!caseData.patientEmail) return;

      const sequences = await db
        .select()
        .from(emailSequences)
        .where(and(
          eq(emailSequences.trigger_event, 'case_closed'),
          eq(emailSequences.status, 'Active')
        ));

      for (const sequence of sequences) {
        await sequenceEngine.addSubscriber(sequence.sequence_id, {
          email: caseData.patientEmail,
          name: caseData.patientName,
          contextData: {
            caseId,
            caseType: caseData.caseType,
            closureReason: caseData.closureReason,
            doctorId: caseData.doctorId,
            triggerEvent: 'case_closed',
            closureDate: new Date().toISOString()
          }
        });
      }

      logger.debug(`Triggered ${sequences.length} sequences for case closure: ${caseId}`);
    } catch (error) {
      logger.error('Error triggering case closure sequences:', error);
    }
  }

  /**
   * Trigger sequences when a contact is created
   */
  static async onContactCreated(contactId: string, contactData: {
    email?: string;
    firstName?: string;
    lastName?: string;
    role?: string;
    leadId?: string;
    organizationId?: string;
  }): Promise<void> {
    try {
      if (!contactData.email) return;

      const sequences = await db
        .select()
        .from(emailSequences)
        .where(and(
          eq(emailSequences.trigger_event, 'contact_created'),
          eq(emailSequences.status, 'Active')
        ));

      for (const sequence of sequences) {
        await sequenceEngine.addSubscriber(sequence.sequence_id, {
          email: contactData.email,
          name: contactData.firstName && contactData.lastName 
            ? `${contactData.firstName} ${contactData.lastName}` 
            : contactData.firstName || contactData.lastName,
          contextData: {
            contactId,
            firstName: contactData.firstName,
            lastName: contactData.lastName,
            role: contactData.role,
            leadId: contactData.leadId,
            organizationId: contactData.organizationId,
            triggerEvent: 'contact_created',
            creationDate: new Date().toISOString()
          }
        });
      }

      logger.debug(`Triggered ${sequences.length} sequences for contact creation: ${contactData.email}`);
    } catch (error) {
      logger.error('Error triggering contact creation sequences:', error);
    }
  }

  /**
   * Trigger sequences when a lead status changes
   */
  static async onLeadStatusChange(leadId: string, leadData: {
    email?: string;
    name?: string;
    oldStatus?: string;
    newStatus?: string;
    assignedToId?: string;
  }): Promise<void> {
    try {
      if (!leadData.email) return;

      const sequences = await db
        .select()
        .from(emailSequences)
        .where(and(
          eq(emailSequences.trigger_event, 'lead_status_change'),
          eq(emailSequences.status, 'Active')
        ));

      for (const sequence of sequences) {
        await sequenceEngine.addSubscriber(sequence.sequence_id, {
          email: leadData.email,
          name: leadData.name,
          contextData: {
            leadId,
            oldStatus: leadData.oldStatus,
            newStatus: leadData.newStatus,
            assignedToId: leadData.assignedToId,
            triggerEvent: 'lead_status_change',
            statusChangeDate: new Date().toISOString()
          }
        });
      }

      logger.debug(`Triggered ${sequences.length} sequences for lead status change: ${leadId}`);
    } catch (error) {
      logger.error('Error triggering lead status change sequences:', error);
    }
  }

  /**
   * Trigger sequences when an appointment is scheduled
   */
  static async onAppointmentScheduled(appointmentId: string, appointmentData: {
    patientEmail?: string;
    patientName?: string;
    doctorId?: string;
    appointmentDate?: Date;
    appointmentType?: string;
  }): Promise<void> {
    try {
      if (!appointmentData.patientEmail) return;

      const sequences = await db
        .select()
        .from(emailSequences)
        .where(and(
          eq(emailSequences.trigger_event, 'appointment_scheduled'),
          eq(emailSequences.status, 'Active')
        ));

      for (const sequence of sequences) {
        await sequenceEngine.addSubscriber(sequence.sequence_id, {
          email: appointmentData.patientEmail,
          name: appointmentData.patientName,
          contextData: {
            appointmentId,
            doctorId: appointmentData.doctorId,
            appointmentDate: appointmentData.appointmentDate?.toISOString(),
            appointmentType: appointmentData.appointmentType,
            triggerEvent: 'appointment_scheduled',
            scheduledDate: new Date().toISOString()
          }
        });
      }

      logger.debug(`Triggered ${sequences.length} sequences for appointment scheduling: ${appointmentId}`);
    } catch (error) {
      logger.error('Error triggering appointment scheduling sequences:', error);
    }
  }

  /**
   * Trigger sequences when a document is uploaded
   */
  static async onDocumentUploaded(documentId: string, documentData: {
    uploaderEmail?: string;
    uploaderName?: string;
    documentType?: string;
    caseId?: string;
    fileName?: string;
  }): Promise<void> {
    try {
      if (!documentData.uploaderEmail) return;

      const sequences = await db
        .select()
        .from(emailSequences)
        .where(and(
          eq(emailSequences.trigger_event, 'document_uploaded'),
          eq(emailSequences.status, 'Active')
        ));

      for (const sequence of sequences) {
        await sequenceEngine.addSubscriber(sequence.sequence_id, {
          email: documentData.uploaderEmail,
          name: documentData.uploaderName,
          contextData: {
            documentId,
            documentType: documentData.documentType,
            caseId: documentData.caseId,
            fileName: documentData.fileName,
            triggerEvent: 'document_uploaded',
            uploadDate: new Date().toISOString()
          }
        });
      }

      logger.info('Triggered ${sequences.length} sequences for document upload: ${documentId}', { requestId: 'context-needed' }, { data: undefined });
    } catch (error) {
      logger.error('Error triggering document upload sequences:', error);
    }
  }

  /**
   * Trigger sequences for case aging (cases that have been open for a certain period)
   */
  static async onCaseAging(caseId: string, caseData: {
    patientEmail?: string;
    patientName?: string;
    daysSinceOpened?: number;
    caseType?: string;
    assignedDoctorId?: string;
  }): Promise<void> {
    try {
      if (!caseData.patientEmail) return;

      const sequences = await db
        .select()
        .from(emailSequences)
        .where(and(
          eq(emailSequences.trigger_event, 'case_aging'),
          eq(emailSequences.status, 'Active')
        ));

      for (const sequence of sequences) {
        await sequenceEngine.addSubscriber(sequence.sequence_id, {
          email: caseData.patientEmail,
          name: caseData.patientName,
          contextData: {
            caseId,
            daysSinceOpened: caseData.daysSinceOpened,
            caseType: caseData.caseType,
            assignedDoctorId: caseData.assignedDoctorId,
            triggerEvent: 'case_aging',
            agingCheckDate: new Date().toISOString()
          }
        });
      }

      logger.debug(`Triggered ${sequences.length} sequences for case aging: ${caseId}`);
    } catch (error) {
      logger.error('Error triggering case aging sequences:', error);
    }
  }

  /**
   * Manually trigger a sequence for a specific user
   */
  static async triggerManualSequence(sequenceId: string, userData: {
    userId?: string;
    email: string;
    name?: string;
    contextData?: Record<string, any>;
  }): Promise<void> {
    try {
      await sequenceEngine.addSubscriber(sequenceId, {
        userId: userData.userId,
        email: userData.email,
        name: userData.name,
        contextData: {
          ...userData.contextData,
          triggerEvent: 'manual',
          triggeredDate: new Date().toISOString()
        }
      });

      logger.info('Manually triggered sequence ${sequenceId} for user: ${userData.email}', { requestId: 'context-needed' }, { data: undefined });
    } catch (error) {
      logger.error('Error manually triggering sequence:', error);
      throw error;
    }
  }

  /**
   * Get available trigger events
   */
  static getTriggerEvents(): Array<{
    value: string;
    label: string;
    description: string;
  }> {
    return [
      {
        value: 'user_registration',
        label: 'User Registration',
        description: 'Triggered when a new user registers'
      },
      {
        value: 'case_closed',
        label: 'Case Closed',
        description: 'Triggered when a medical case is closed'
      },
      {
        value: 'contact_created',
        label: 'Contact Created',
        description: 'Triggered when a new contact is created in CRM'
      },
      {
        value: 'lead_status_change',
        label: 'Lead Status Change',
        description: 'Triggered when a lead status changes'
      },
      {
        value: 'appointment_scheduled',
        label: 'Appointment Scheduled',
        description: 'Triggered when an appointment is scheduled'
      },
      {
        value: 'document_uploaded',
        label: 'Document Uploaded',
        description: 'Triggered when a document is uploaded'
      },
      {
        value: 'case_aging',
        label: 'Case Aging',
        description: 'Triggered for cases that have been open for a certain period'
      },
      {
        value: 'manual',
        label: 'Manual Trigger',
        description: 'Manually triggered sequence'
      }
    ];
  }
}