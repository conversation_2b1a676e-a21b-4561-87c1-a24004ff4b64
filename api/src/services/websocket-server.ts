import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import * as Y from 'yjs';
import WebSocket, { WebSocketServer } from 'ws';
import { db } from '../db/index';
import { caseNotes, collaborationSessions } from '../db/schema';
import { eq, and } from 'drizzle-orm';
import jwt from 'jsonwebtoken';
import * as syncProtocol from 'y-protocols/sync';
import * as awarenessProtocol from 'y-protocols/awareness';
import * as encoding from 'lib0/encoding';
import * as decoding from 'lib0/decoding';

interface AuthenticatedSocket extends WebSocket {
  userId?: string;
  caseId?: string;
  noteType?: string;
  userColor?: string;
}

interface CollaborationData {
  caseId: string;
  noteType: string;
  userId: string;
  userName: string;
  userColor: string;
}

class CollaborativeNotesServer {
  private wss: WebSocketServer;
  private documents: Map<string, Y.Doc> = new Map();
  private connections: Map<string, Set<AuthenticatedSocket>> = new Map();

  constructor(server: HttpServer) {
    // Create WebSocket server
    this.wss = new WebSocketServer({
      server,
      path: '/ws',
      verifyClient: this.verifyClient.bind(this),
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    console.log('🔗 WebSocket server initialized for collaborative notes');
  }

  private async verifyClient(info: any): Promise<boolean> {
    try {
      const url = new URL(info.req.url, 'http://localhost');
      const token = url.searchParams.get('token');
      
      if (!token) {
        console.log('WebSocket connection rejected: No token provided');
        return false;
      }

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      if (!decoded.id) {
        console.log('WebSocket connection rejected: Invalid token');
        return false;
      }

      // Store user info for later use
      info.req.userId = decoded.id;
      info.req.caseId = url.searchParams.get('caseId');
      info.req.noteType = url.searchParams.get('noteType');
      
      return true;
    } catch (error) {
      console.error('WebSocket verification error:', error);
      return false;
    }
  }

  private async handleConnection(ws: AuthenticatedSocket, req: any) {
    const userId = req.userId;
    const caseId = req.caseId;
    const noteType = req.noteType;

    if (!userId || !caseId || !noteType) {
      ws.close(1008, 'Missing required parameters');
      return;
    }

    ws.userId = userId;
    ws.caseId = caseId;
    ws.noteType = noteType;
    ws.userColor = this.generateUserColor(userId);

    const documentId = `${caseId}:${noteType}`;
    
    try {
      // Initialize or get existing document
      let ydoc = this.documents.get(documentId);
      if (!ydoc) {
        ydoc = new Y.Doc();
        await this.loadDocumentFromDatabase(ydoc, caseId, noteType);
        this.documents.set(documentId, ydoc);
      }

      // Track connections
      if (!this.connections.has(documentId)) {
        this.connections.set(documentId, new Set());
      }
      this.connections.get(documentId)!.add(ws);

      // Start collaboration session
      await this.startCollaborationSession(userId, caseId, noteType, ws.userColor);

      // Handle yjs messages
      ws.on('message', (message: Buffer) => {
        try {
          const decoder = decoding.createDecoder(message);
          const encoder = encoding.createEncoder();
          const messageType = decoding.readVarUint(decoder);

          switch (messageType) {
            case 0: // sync message
              encoding.writeVarUint(encoder, 0);
              syncProtocol.readSyncMessage(decoder, encoder, ydoc, ws);
              if (encoding.length(encoder) > 1) {
                ws.send(encoding.toUint8Array(encoder));
              }
              break;
            case 1: // awareness message
              // Handle awareness updates for user presence
              break;
          }
        } catch (error) {
          console.error('Error handling WebSocket message:', error);
        }
      });

      // Handle document updates
      ydoc.on('update', async (update: Uint8Array) => {
        await this.saveDocumentToDatabase(caseId, noteType, ydoc, userId);
      });

      // Handle connection close
      ws.on('close', async () => {
        await this.endCollaborationSession(userId, caseId, noteType);
        this.connections.get(documentId)?.delete(ws);

        // Clean up document if no more connections
        if (this.connections.get(documentId)?.size === 0) {
          this.documents.delete(documentId);
          this.connections.delete(documentId);
        }
      });

      // Send initial sync message
      const encoder = encoding.createEncoder();
      encoding.writeVarUint(encoder, 0);
      syncProtocol.writeSyncStep1(encoder, ydoc);
      ws.send(encoding.toUint8Array(encoder));

      // Send initial collaborators list
      await this.broadcastCollaborators(documentId);

      console.log(`✅ User ${userId} connected to document ${documentId}`);
    } catch (error) {
      console.error('Error handling WebSocket connection:', error);
      ws.close(1011, 'Internal server error');
    }
  }

  private async loadDocumentFromDatabase(ydoc: Y.Doc, caseId: string, noteType: string) {
    try {
      const [note] = await db
        .select()
        .from(caseNotes)
        .where(
          and(
            eq(caseNotes.caseId, caseId),
            eq(caseNotes.noteTypeId, noteType) // Assuming noteType is the ID
          )
        )
        .limit(1);

      if (note?.yjsDocument) {
        // Load existing yJS document
        const update = Buffer.from(note.yjsDocument, 'base64');
        Y.applyUpdate(ydoc, update);
      } else if (note?.structuredContent) {
        // Migrate from old format
        const text = ydoc.getText('content');
        const content = typeof note.structuredContent === 'string' 
          ? note.structuredContent 
          : JSON.stringify(note.structuredContent);
        text.insert(0, content);
      }
    } catch (error) {
      console.error('Error loading document from database:', error);
    }
  }

  private async saveDocumentToDatabase(caseId: string, noteType: string, ydoc: Y.Doc, doctorId?: string) {
    try {
      const update = Y.encodeStateAsUpdate(ydoc);
      const base64Update = Buffer.from(update).toString('base64');
      
      // Extract text content for backward compatibility
      const textContent = ydoc.getText('content').toString();
      
      // Update or insert document
      await db
        .insert(caseNotes)
        .values({
          caseId,
          doctorId: doctorId || 'system', // Use provided doctorId or default to 'system'
          noteTypeId: noteType,
          yjsDocument: base64Update,
          structuredContent: { content: textContent },
          documentVersion: 1,
          updatedAt: new Date(),
        })
        .onConflictDoUpdate({
          target: [caseNotes.caseId, caseNotes.noteTypeId],
          set: {
            yjsDocument: base64Update,
            structuredContent: { content: textContent },
            documentVersion: 1, // Increment in real implementation
            updatedAt: new Date(),
          },
        });
    } catch (error) {
      console.error('Error saving document to database:', error);
    }
  }

  private async startCollaborationSession(
    userId: string, 
    caseId: string, 
    noteType: string, 
    userColor: string
  ) {
    try {
      await db
        .insert(collaborationSessions)
        .values({
          userId,
          caseId,
          noteTypeId: noteType,
          userColor,
          isActive: true,
        })
        .onConflictDoUpdate({
          target: [
            collaborationSessions.caseId,
            collaborationSessions.noteTypeId,
            collaborationSessions.userId,
            collaborationSessions.isActive,
          ],
          set: {
            sessionStart: new Date(),
            userColor,
          },
        });
    } catch (error) {
      console.error('Error starting collaboration session:', error);
    }
  }

  private async endCollaborationSession(userId: string, caseId: string, noteType: string) {
    try {
      await db
        .update(collaborationSessions)
        .set({
          isActive: false,
          sessionEnd: new Date(),
        })
        .where(
          and(
            eq(collaborationSessions.userId, userId),
            eq(collaborationSessions.caseId, caseId),
            eq(collaborationSessions.noteTypeId, noteType),
            eq(collaborationSessions.isActive, true)
          )
        );
    } catch (error) {
      console.error('Error ending collaboration session:', error);
    }
  }

  private async broadcastCollaborators(documentId: string) {
    const connections = this.connections.get(documentId);
    if (!connections || connections.size === 0) return;

    const collaborators = Array.from(connections).map(ws => ({
      userId: ws.userId,
      userColor: ws.userColor,
    }));

    const message = JSON.stringify({
      type: 'collaborators',
      data: collaborators,
    });

    connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      }
    });
  }

  private generateUserColor(userId: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    
    const hash = userId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    return colors[Math.abs(hash) % colors.length];
  }

  public getStats() {
    return {
      activeDocuments: this.documents.size,
      totalConnections: Array.from(this.connections.values())
        .reduce((sum, connections) => sum + connections.size, 0),
    };
  }
}

export { CollaborativeNotesServer };
