import { PermissionService, PERMISSION_MATRIX, UserContext, ResourceContext } from '../../../shared/permissions.ts';

import { logger } from '../utils/structuredLogger';
// Test cases
const testCases = [
  // Patient creating a case
  {
    name: 'Patient creating a case',
    user: { id: 'patient1', role: 'patient', email: '<EMAIL>' } as User<PERSON>ontext,
    permission: 'cases:create',
    resource: undefined,
    expected: true
  },
  // Patient reading their own case
  {
    name: 'Patient reading their own case',
    user: { id: 'patient1', role: 'patient', email: '<EMAIL>' } as UserContext,
    permission: 'cases:read',
    resource: { patientId: 'patient1' } as ResourceContext,
    expected: true
  },
  // Patient reading someone else's case
  {
    name: 'Patient reading someone else\'s case',
    user: { id: 'patient1', role: 'patient', email: '<EMAIL>' } as User<PERSON>ontext,
    permission: 'cases:read',
    resource: { patientId: 'patient2' } as <PERSON><PERSON><PERSON>x<PERSON>,
    expected: false
  },
  // Doctor reading assigned case
  {
    name: 'Doctor reading assigned case',
    user: { id: 'doctor1', role: 'doctor', email: '<EMAIL>' } as User<PERSON>ontext,
    permission: 'cases:read',
    resource: { id: 'case1' } as ResourceContext,
    expected: true
  },
  // Doctor reading unassigned case
  {
    name: 'Doctor reading unassigned case',
    user: { id: 'doctor1', role: 'doctor', email: '<EMAIL>' } as UserContext,
    permission: 'cases:read',
    resource: { id: 'case2' } as ResourceContext,
    expected: false
  },
  // Admin doing anything
  {
    name: 'Admin deleting any case',
    user: { id: 'admin1', role: 'admin', email: '<EMAIL>' } as UserContext,
    permission: 'cases:delete',
    resource: { patientId: 'patient1' } as ResourceContext,
    expected: true
  }
];

logger.debug('Running permission tests...');
let passed = 0;
let failed = 0;

for (const test of testCases) {
  const result = PermissionService.canAccessResource(test.user, test.permission, test.resource);
  if (result === test.expected) {
    logger.debug(`✅ ${test.name}`);
    passed++;
  } else {
    logger.debug(`❌ ${test.name} - Expected: ${test.expected}, Got: ${result}`);
    failed++;
  }
}

logger.debug(`\nResults: ${passed} passed, ${failed} failed`);

// Test permission matrix
logger.debug('\nPermission Matrix:');
logger.debug('Debug output', undefined, { data: PERMISSION_MATRIX });
