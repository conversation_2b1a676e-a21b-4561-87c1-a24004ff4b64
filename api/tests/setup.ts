import { beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { db } from '../src/db/index.js';
import { sql } from 'drizzle-orm';

// Test database setup
beforeAll(async () => {
  // Ensure we're using test database
  if (!process.env.DATABASE_URL?.includes('test')) {
    throw new Error('Tests must run against test database');
  }
  
  // Run migrations
  console.log('Setting up test database...');
  // Add migration logic here if needed
});

afterAll(async () => {
  // Clean up database connections
  console.log('Cleaning up test database...');
  // Add cleanup logic here
});

beforeEach(async () => {
  // Start transaction for test isolation
  await db.execute(sql`BEGIN`);
});

afterEach(async () => {
  // Rollback transaction to clean up test data
  await db.execute(sql`ROLLBACK`);
});

// Mock external services
jest.mock('../src/services/storageService.js', () => ({
  storageService: {
    uploadFile: jest.fn().mockResolvedValue({
      fileName: 'test-file.pdf',
      filePath: 'test/path/test-file.pdf',
      originalFileName: 'test-file.pdf',
      fileSize: 1024,
      mimeType: 'application/pdf',
    }),
    getFile: jest.fn().mockResolvedValue({
      pipe: jest.fn(),
    }),
    deleteFile: jest.fn().mockResolvedValue(undefined),
    fileExists: jest.fn().mockResolvedValue(true),
  },
}));

// Mock authentication middleware
jest.mock('../src/middleware/opalAuth.js', () => ({
  opalAuthMiddleware: (req: any, res: any, next: any) => {
    req.user = {
      id: 'test-user-id',
      role: 'Admin',
      email: '<EMAIL>',
    };
    next();
  },
  requireResourceAccess: () => (req: any, res: any, next: any) => next(),
  requireRole: () => (req: any, res: any, next: any) => next(),
}));

// Mock logger
jest.mock('../src/utils/logger.js', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  logAuditEvent: jest.fn(),
}));