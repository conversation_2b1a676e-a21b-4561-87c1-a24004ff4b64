import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import MarkdownViewer from '@/components/ui/markdown-viewer'
import {
  Building,
  MapPin,
  Phone,
  Mail,
  Globe,
  Calendar,
  Clock,
  ChevronLeft,
  Edit,
  Trash2,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface OrganizationDetailProps {
  organizationId: string
  onClose?: () => void
  showBackButton?: boolean
}

interface Organization {
  id: string
  name: string
  type: 'hospital' | 'clinic' | 'private_practice' | 'insurance' | 'pharmacy' | 'other'
  address?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  notes?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
}

// Organization type configuration for consistent UI
const typeConfig = {
  hospital: { label: 'Hospital', color: 'bg-blue-100 text-blue-800' },
  clinic: { label: 'Clinic', color: 'bg-green-100 text-green-800' },
  private_practice: { label: 'Private Practice', color: 'bg-purple-100 text-purple-800' },
  insurance: { label: 'Insurance', color: 'bg-yellow-100 text-yellow-800' },
  pharmacy: { label: 'Pharmacy', color: 'bg-orange-100 text-orange-800' },
  other: { label: 'Other', color: 'bg-gray-100 text-gray-800' }
}

export function OrganizationDetail({ 
  organizationId, 
  onClose,
  showBackButton = true 
}: OrganizationDetailProps) {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  // Load organization details
  useEffect(() => {
    const fetchOrganizationDetails = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        // @ts-ignore - apiClient.request is private but used throughout the codebase
        const response = await apiClient.request(`/crm/organizations/${organizationId}`) as any
        
        if (response.organization) {
          setOrganization(response.organization)
        } else {
          setError('Organization not found')
        }
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load organization details:', error)
        setError('Failed to load organization details. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    fetchOrganizationDetails()
  }, [organizationId])

  // Format date to readable format
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Format full address
  const formatFullAddress = (org: Organization) => {
    const parts = []
    if (org.address) parts.push(org.address)
    
    const cityStateZip = []
    if (org.city) cityStateZip.push(org.city)
    if (org.state) cityStateZip.push(org.state)
    if (org.zipCode) cityStateZip.push(org.zipCode)
    
    if (cityStateZip.length > 0) {
      parts.push(cityStateZip.join(', '))
    }
    
    if (org.country) parts.push(org.country)
    
    return parts.length > 0 ? parts.join('\n') : 'No address provided'
  }

  // Handle organization deletion
  const handleDelete = async () => {
    if (!organization) return
    
    try {
      setIsDeleting(true)
      
      // @ts-ignore - apiClient.request is private but used throughout the codebase
      await apiClient.request(`/crm/organizations/${organizationId}`, {
        method: 'DELETE'
      })
      
      setIsDeleting(false)
      setShowDeleteConfirm(false)
      
      // Navigate back or close modal
      if (onClose) {
        onClose()
      } else {
        navigate('/crm/organizations')
      }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to delete organization:', error)
      setError('Failed to delete organization. Please try again.')
      setIsDeleting(false)
    }
  }

  // Handle edit navigation
  const handleEdit = () => {
    navigate(`/crm/organizations/${organizationId}/edit`)
  }

  // Handle back navigation
  const handleBack = () => {
    if (onClose) {
      onClose()
    } else {
      navigate('/crm/organizations')
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-12">
        <Loader2 className="h-8 w-8 text-indigo-500 animate-spin" />
        <span className="ml-2 text-gray-600">Loading organization details...</span>
      </div>
    )
  }

  if (error || !organization) {
    return (
      <div className="p-6 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">Error</h3>
        <p className="mt-1 text-sm text-gray-500">{error || 'Organization not found'}</p>
        <button
          onClick={handleBack}
          className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back to Organizations
        </button>
      </div>
    )
  }

  return (
    <div className="bg-white">
      {/* Header with actions */}
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <div className="flex items-center">
          {showBackButton && (
            <button
              onClick={handleBack}
              className="mr-4 text-gray-500 hover:text-gray-700"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
          )}
          <h2 className="text-xl font-semibold text-gray-900">{organization.name}</h2>
          <span className={`ml-3 text-xs font-medium px-2.5 py-0.5 rounded-full ${typeConfig[organization.type]?.color}`}>
            {typeConfig[organization.type]?.label}
          </span>
        </div>
        
        {(user?.role === 'admin' || (user?.role === 'doctor' && user?.id === organization.createdBy)) && (
          <div className="flex space-x-2">
            <button
              onClick={handleEdit}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </button>
            
            {user?.role === 'admin' && (
              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </button>
            )}
          </div>
        )}
      </div>
      
      {/* Organization details */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Contact Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
            
            <div className="space-y-4">
              {/* Address */}
              <div className="flex">
                <MapPin className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Address</h4>
                  <pre className="mt-1 text-sm text-gray-500 whitespace-pre-line">
                    {formatFullAddress(organization)}
                  </pre>
                </div>
              </div>
              
              {/* Phone */}
              <div className="flex">
                <Phone className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Phone</h4>
                  <p className="mt-1 text-sm text-gray-500">
                    {organization.phone || 'No phone number provided'}
                  </p>
                </div>
              </div>
              
              {/* Email */}
              <div className="flex">
                <Mail className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Email</h4>
                  {organization.email ? (
                    <a 
                      href={`mailto:${organization.email}`} 
                      className="mt-1 text-sm text-indigo-600 hover:text-indigo-800"
                    >
                      {organization.email}
                    </a>
                  ) : (
                    <p className="mt-1 text-sm text-gray-500">No email provided</p>
                  )}
                </div>
              </div>
              
              {/* Website */}
              <div className="flex">
                <Globe className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Website</h4>
                  {organization.website ? (
                    <a 
                      href={organization.website.startsWith('http') ? organization.website : `https://${organization.website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mt-1 text-sm text-indigo-600 hover:text-indigo-800"
                    >
                      {organization.website.replace(/^https?:\/\//i, '')}
                    </a>
                  ) : (
                    <p className="mt-1 text-sm text-gray-500">No website provided</p>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* Additional Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
            
            <div className="space-y-4">
              {/* Created */}
              <div className="flex">
                <Calendar className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Created</h4>
                  <p className="mt-1 text-sm text-gray-500">{formatDate(organization.createdAt)}</p>
                </div>
              </div>
              
              {/* Last Updated */}
              <div className="flex">
                <Clock className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Last Updated</h4>
                  <p className="mt-1 text-sm text-gray-500">{formatDate(organization.updatedAt)}</p>
                </div>
              </div>
              
              {/* Status */}
              <div className="flex">
                <Building className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Status</h4>
                  <p className="mt-1 text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      organization.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {organization.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Notes */}
        <div className="mt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Notes</h3>
          <div className="bg-gray-50 rounded-lg p-4">
            {organization.notes ? (
              <MarkdownViewer content={organization.notes} height={200} />
            ) : (
              <p className="text-sm text-gray-500 italic">No notes available</p>
            )}
          </div>
        </div>
        
        {/* Related contacts section could be added here */}
      </div>
      
      {/* Delete confirmation modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
          <div className="relative p-5 bg-white w-full max-w-md mx-auto rounded-lg shadow-xl">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">Delete Organization</h3>
              <p className="mt-2 text-sm text-gray-500">
                Are you sure you want to delete {organization.name}? This action cannot be undone.
              </p>
              <div className="mt-6 flex justify-center space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  disabled={isDeleting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 inline animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    'Delete'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
