#!/bin/bash

# Clean up all temporary scripts and migration folders
# This will leave only the essential files for a clean Docker startup

set -e

echo "🧹 Cleaning up all temporary scripts and migration folders..."
echo ""

# Create archive for backup
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
ARCHIVE_DIR="temp-cleanup-archive-$TIMESTAMP"
mkdir -p "$ARCHIVE_DIR"

echo "📦 Archiving files to: $ARCHIVE_DIR"

# Archive all migration folders except drizzle
echo "🗂️ Archiving migration folders..."
[ -d "api/migrations-clean" ] && mv api/migrations-clean "$ARCHIVE_DIR/" && echo "  ✅ Archived migrations-clean"
[ -d "api/src/db/migrations" ] && mv api/src/db/migrations "$ARCHIVE_DIR/" && echo "  ✅ Archived src/db/migrations"
[ -d "api/src/migrations" ] && mv api/src/migrations "$ARCHIVE_DIR/" && echo "  ✅ Archived src/migrations"

# Archive migration scripts
echo "📜 Archiving migration scripts..."
[ -f "api/run-migrations.cjs" ] && mv api/run-migrations.cjs "$ARCHIVE_DIR/"
[ -f "api/src/run-migrations.ts" ] && mv api/src/run-migrations.ts "$ARCHIVE_DIR/"
[ -f "api/docker-compose.migrations.yml" ] && mv api/docker-compose.migrations.yml "$ARCHIVE_DIR/"

# Archive temporary utility scripts
echo "🔧 Archiving utility scripts..."
[ -f "api/add-appointments.ts" ] && mv api/add-appointments.ts "$ARCHIVE_DIR/"
[ -f "api/create_test_user.js" ] && mv api/create_test_user.js "$ARCHIVE_DIR/"
[ -f "api/check_users.js" ] && mv api/check_users.js "$ARCHIVE_DIR/"
[ -d "api/scripts" ] && mv api/scripts "$ARCHIVE_DIR/"

# Archive root-level temp scripts
echo "🧽 Archiving root temp scripts..."
[ -f "consolidate-migrations.sh" ] && mv consolidate-migrations.sh "$ARCHIVE_DIR/"
[ -f "docker-consolidate-migrations.sh" ] && mv docker-consolidate-migrations.sh "$ARCHIVE_DIR/"
[ -f "run-consolidation.sh" ] && mv run-consolidation.sh "$ARCHIVE_DIR/"
[ -f "update-package-scripts.sh" ] && mv update-package-scripts.sh "$ARCHIVE_DIR/"
[ -f "parse-database-url.sh" ] && mv parse-database-url.sh "$ARCHIVE_DIR/"
[ -f "fix-permissions.sh" ] && mv fix-permissions.sh "$ARCHIVE_DIR/"

# Archive documentation files that are no longer needed
echo "📚 Archiving temp documentation..."
[ -f "DATABASE_URL_MIGRATION.md" ] && mv DATABASE_URL_MIGRATION.md "$ARCHIVE_DIR/"
[ -f "MIGRATION_CONSOLIDATION.md" ] && mv MIGRATION_CONSOLIDATION.md "$ARCHIVE_DIR/"

# Clear old drizzle migrations (will be regenerated)
echo "🗑️ Clearing old drizzle migrations..."
rm -rf api/drizzle/*

# Archive package.json backup
[ -f "api/package.json.backup" ] && mv api/package.json.backup "$ARCHIVE_DIR/"

echo ""
echo "✅ Cleanup complete!"
echo ""
echo "📁 Current clean structure:"
echo "api/"
echo "├── drizzle/                 # Will be auto-generated"
echo "├── src/"
echo "│   ├── db/schema/          # Source of truth"
echo "│   └── scripts/            # Seed scripts only"
echo "├── drizzle.config.ts       # Drizzle configuration"
echo "└── package.json            # Clean scripts"
echo ""
echo "📦 All temp files archived in: $ARCHIVE_DIR"
echo ""
echo "🚀 Next steps:"
echo "1. Set up auto-initialization in API startup"
echo "2. Test with: docker-compose up --build"
echo "3. Remove archive when confident: rm -rf $ARCHIVE_DIR"
