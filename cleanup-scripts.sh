#!/bin/bash

# Clean up scripts folder - keep only essential scripts

set -e

echo "🧹 Cleaning up scripts folder..."
echo ""

# Create archive for backup
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
ARCHIVE_DIR="scripts-cleanup-archive-$TIMESTAMP"
mkdir -p "$ARCHIVE_DIR"

echo "📦 Archiving temporary scripts to: $ARCHIVE_DIR"

# Essential scripts to KEEP
ESSENTIAL_SCRIPTS=(
    "auto-initialize-database.ts"
    "seed-complete-database.ts"
    "validate-db-config.ts"
    "wipe-and-seed-test-data.ts"
    "seed.ts"
)

# Archive all temporary/one-time scripts
TEMP_SCRIPTS=(
    "add-doctor-credentials-permissions.ts"
    "complete-uuid-migration.ts"
    "create-document-versions-clean.ts"
    "create-document-versions.ts"
    "enhance-case-details.js"
    "enhance-case-details.mjs"
    "enhance-case-details.ts"
    "finalize-uuid-migration.ts"
    "fix-corrupted-notes.ts"
    "migrate-consent-to-legal-compliance.ts"
    "migrate-to-uuid-ids.ts"
    "reset-user-consents.ts"
    "run-migration-0009.js"
    "seed-permissions.ts"
    "update-appointments-with-case-ids.ts"
    "update-note-types.ts"
)

echo ""
echo "🗂️ Archiving temporary scripts..."

for script in "${TEMP_SCRIPTS[@]}"; do
    if [ -f "api/src/scripts/$script" ]; then
        mv "api/src/scripts/$script" "$ARCHIVE_DIR/"
        echo "  ✅ Archived $script"
    fi
done

echo ""
echo "✅ Scripts cleanup complete!"
echo ""
echo "📁 Essential scripts remaining:"
for script in "${ESSENTIAL_SCRIPTS[@]}"; do
    if [ -f "api/src/scripts/$script" ]; then
        echo "  ✅ $script"
    else
        echo "  ❌ $script (missing!)"
    fi
done

echo ""
echo "📦 Temporary scripts archived in: $ARCHIVE_DIR"
echo ""
echo "🎯 Final clean scripts folder:"
echo "api/src/scripts/"
echo "├── auto-initialize-database.ts    # Auto-setup on startup"
echo "├── seed-complete-database.ts      # Complete database seeding"
echo "├── validate-db-config.ts          # Database connection testing"
echo "├── wipe-and-seed-test-data.ts     # Test data for development"
echo "└── seed.ts                        # Basic note types seeding"
echo ""
echo "🚀 Ready for production with clean, essential scripts only!"
