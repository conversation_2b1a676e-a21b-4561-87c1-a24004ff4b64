#!/bin/bash

# Migration Consolidation Script - Docker Version
# This script consolidates all migration systems into a single, clean Drizzle-based system
# Run this from the project root: docker-compose exec api bash /app/consolidate-migrations.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 Starting Migration Consolidation Process (Docker)${NC}"
echo ""

# Check if we're running inside Docker
if [ ! -f /.dockerenv ]; then
    echo -e "${RED}❌ ERROR: This script should be run inside the Docker container${NC}"
    echo "Please run: docker-compose exec api bash /app/consolidate-migrations.sh"
    exit 1
fi

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo -e "${RED}❌ ERROR: DATABASE_URL environment variable is not set${NC}"
    echo "DATABASE_URL should be set in docker-compose.yml"
    exit 1
fi

# Parse DATABASE_URL for individual components
echo -e "${BLUE}📊 Database Configuration:${NC}"
echo "DATABASE_URL: $DATABASE_URL"

# Extract components from DATABASE_URL
URL_WITHOUT_PROTOCOL=${DATABASE_URL#postgresql://}
URL_WITHOUT_PROTOCOL=${URL_WITHOUT_PROTOCOL#postgres://}

USER_PASS=${URL_WITHOUT_PROTOCOL%%@*}
POSTGRES_USER=${USER_PASS%%:*}
POSTGRES_PASSWORD=${USER_PASS#*:}

HOST_PORT_DB=${URL_WITHOUT_PROTOCOL#*@}
HOST_PORT=${HOST_PORT_DB%%/*}
POSTGRES_HOST=${HOST_PORT%%:*}
POSTGRES_PORT=${HOST_PORT#*:}

if [ "$POSTGRES_PORT" = "$POSTGRES_HOST" ]; then
    POSTGRES_PORT=5432
fi

POSTGRES_DB=${HOST_PORT_DB#*/}
POSTGRES_DB=${POSTGRES_DB%%\?*}

echo "Host: $POSTGRES_HOST:$POSTGRES_PORT"
echo "Database: $POSTGRES_DB"
echo "User: $POSTGRES_USER"

echo -e "${YELLOW}📊 Migration Consolidation Plan:${NC}"
echo "1. Backup current database (if exists)"
echo "2. Create fresh database"
echo "3. Apply migrations-clean (most complete system)"
echo "4. Run seed scripts for initial data"
echo "5. Generate new Drizzle schema from consolidated database"
echo "6. Create single comprehensive Drizzle migration"
echo "7. Clean up old migration folders"
echo ""

read -p "Do you want to proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}Operation cancelled${NC}"
    exit 0
fi

echo ""
echo -e "${BLUE}=== Phase 1: Database Preparation ===${NC}"

# Step 1: Backup current database (if it exists)
echo -e "${YELLOW}📦 Creating database backup...${NC}"
BACKUP_FILE="database_backup_$(date +%Y%m%d_%H%M%S).sql"

if PGPASSWORD="$POSTGRES_PASSWORD" pg_dump -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" > "$BACKUP_FILE" 2>/dev/null; then
    echo -e "${GREEN}✅ Database backed up to: $BACKUP_FILE${NC}"
else
    echo -e "${YELLOW}⚠️  No existing database to backup (this is fine for fresh setup)${NC}"
fi

# Step 2: Drop and recreate database
echo -e "${YELLOW}🗑️  Dropping and recreating database...${NC}"
PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "postgres" -c "DROP DATABASE IF EXISTS \"$POSTGRES_DB\";"
PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "postgres" -c "CREATE DATABASE \"$POSTGRES_DB\";"
echo -e "${GREEN}✅ Fresh database created${NC}"

echo ""
echo -e "${BLUE}=== Phase 2: Apply migrations-clean ===${NC}"

# Step 3: Apply migrations-clean (most comprehensive system)
echo -e "${YELLOW}📋 Applying migrations-clean (18 migrations)...${NC}"
cd api/migrations-clean
if ./migrate.sh up; then
    echo -e "${GREEN}✅ migrations-clean applied successfully${NC}"
else
    echo -e "${RED}❌ Failed to apply migrations-clean${NC}"
    exit 1
fi
cd ../..

echo ""
echo -e "${BLUE}=== Phase 3: Seed Initial Data ===${NC}"

# Step 4: Run seed scripts
echo -e "${YELLOW}🌱 Running seed scripts...${NC}"

# Seed permissions (critical for OPAL)
echo -e "${BLUE}Seeding permissions...${NC}"
cd api
npm run tsx src/scripts/seed-permissions.ts
echo -e "${GREEN}✅ Permissions seeded${NC}"

# Seed note types
echo -e "${BLUE}Seeding note types...${NC}"
npm run tsx src/scripts/seed.ts
echo -e "${GREEN}✅ Note types seeded${NC}"

cd ..

echo ""
echo -e "${BLUE}=== Phase 4: Generate New Drizzle Schema ===${NC}"

# Step 5: Generate fresh Drizzle schema from consolidated database
echo -e "${YELLOW}🔧 Generating new Drizzle schema...${NC}"
cd api

# Clear existing drizzle migrations
rm -rf drizzle/*
echo -e "${GREEN}✅ Cleared old Drizzle migrations${NC}"

# Generate new schema
npm run db:generate
echo -e "${GREEN}✅ New Drizzle schema generated${NC}"

cd ..

echo ""
echo -e "${BLUE}=== Phase 5: Cleanup ===${NC}"

# Step 6: Archive old migration systems
echo -e "${YELLOW}📦 Archiving old migration systems...${NC}"

# Create archive directory
mkdir -p migration-archive/$(date +%Y%m%d_%H%M%S)
ARCHIVE_DIR="migration-archive/$(date +%Y%m%d_%H%M%S)"

# Archive old systems
mv api/src/db/migrations "$ARCHIVE_DIR/src-db-migrations" 2>/dev/null || true
mv api/src/migrations "$ARCHIVE_DIR/src-migrations" 2>/dev/null || true
mv api/run-migrations.cjs "$ARCHIVE_DIR/" 2>/dev/null || true
mv api/src/run-migrations.ts "$ARCHIVE_DIR/" 2>/dev/null || true

echo -e "${GREEN}✅ Old migration systems archived to: $ARCHIVE_DIR${NC}"

# Keep migrations-clean for now (as reference)
echo -e "${YELLOW}📝 Keeping migrations-clean as reference (can be removed later)${NC}"

echo ""
echo -e "${GREEN}🎉 Migration Consolidation Complete!${NC}"
echo ""
echo -e "${BLUE}=== Summary ===${NC}"
echo "✅ Database consolidated from migrations-clean (18 migrations)"
echo "✅ Permissions and roles seeded"
echo "✅ Note types seeded"
echo "✅ Fresh Drizzle schema generated"
echo "✅ Old migration systems archived"
echo ""
echo -e "${BLUE}=== Next Steps ===${NC}"
echo "1. Test your application with the consolidated database"
echo "2. Run additional seed scripts if needed:"
echo "   - npm run tsx api/src/scripts/wipe-and-seed-test-data.ts (for test data)"
echo "3. Update package.json to remove old migration commands"
echo "4. Remove migrations-clean folder when confident everything works"
echo ""
echo -e "${BLUE}=== Login Credentials ===${NC}"
echo "Admin: <EMAIL> / Continuia"
echo "Doctor: <EMAIL> / Continuia"  
echo "Patient: <EMAIL> / Continuia"
echo ""
echo -e "${YELLOW}⚠️  Remember to change passwords in production!${NC}"
