FROM node:18-alpine

WORKDIR /app

# Copy desk app package files first for better caching
COPY desk/package*.json ./desk/
WORKDIR /app/desk

# Install dependencies
RUN npm install --legacy-peer-deps

# Go back to app root and copy shared folder
WORKDIR /app
COPY shared/ ./shared/

# Copy desk app source code
COPY desk/ ./desk/

# Set working directory back to desk
WORKDIR /app/desk

# Expose port
EXPOSE 3000

# Start development server with hot reload
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
