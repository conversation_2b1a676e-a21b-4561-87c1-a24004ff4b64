import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  ArrowLeft,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Mail,
  Eye,
  MousePointer,
  UserMinus,
  RefreshCw,
  Calendar,
  Users,
  AlertCircle
} from 'lucide-react'

// Define types
interface CampaignAnalytics {
  campaign_id: string
  total_sent: number
  total_delivered: number
  total_bounced: number
  total_opened: number
  total_clicked: number
  total_unsubscribed: number
  delivery_rate: number
  open_rate: number
  click_rate: number
  bounce_rate: number
  unsubscribe_rate: number
  last_sent_at?: string
  last_updated_at: string
}

interface Campaign {
  campaign_id: string
  name: string
  description: string
  type: string
  status: string
  created_at: string
}

interface MetricCard {
  title: string
  value: string | number
  subtitle: string
  icon: React.ComponentType<any>
  color: string
  trend?: {
    value: number
    isPositive: boolean
  }
}

export function CampaignAnalytics() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const { campaignId } = useParams()

  const [campaign, setCampaign] = useState<Campaign | null>(null)
  const [analytics, setAnalytics] = useState<CampaignAnalytics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (campaignId) {
      loadCampaignData()
      loadAnalytics()
    }
  }, [campaignId])

  const loadCampaignData = async () => {
    try {
      // @ts-ignore
      const response = await (apiClient as any).request(`/crm/campaigns/${campaignId}`) as any
      setCampaign(response.data)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load campaign:', error)
      setError('Failed to load campaign data.')
    }
  }

  const loadAnalytics = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      // @ts-ignore
      const response = await (apiClient as any).request(`/crm/campaigns/${campaignId}/analytics`) as any
      setAnalytics(response.data)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load analytics:', error)
      setError('Failed to load analytics data.')
    } finally {
      setIsLoading(false)
    }
  }

  const formatPercentage = (value: number) => {
    return `${(value / 100).toFixed(1)}%`
  }

  const formatNumber = (value: number) => {
    return value.toLocaleString()
  }

  const getMetricCards = (): MetricCard[] => {
    if (!analytics) return []

    return [
      {
        title: 'Total Sent',
        value: formatNumber(analytics.total_sent),
        subtitle: 'Emails sent',
        icon: Mail,
        color: 'bg-blue-500'
      },
      {
        title: 'Delivery Rate',
        value: formatPercentage(analytics.delivery_rate),
        subtitle: `${formatNumber(analytics.total_delivered)} delivered`,
        icon: TrendingUp,
        color: 'bg-green-500'
      },
      {
        title: 'Open Rate',
        value: formatPercentage(analytics.open_rate),
        subtitle: `${formatNumber(analytics.total_opened)} opened`,
        icon: Eye,
        color: 'bg-purple-500'
      },
      {
        title: 'Click Rate',
        value: formatPercentage(analytics.click_rate),
        subtitle: `${formatNumber(analytics.total_clicked)} clicked`,
        icon: MousePointer,
        color: 'bg-indigo-500'
      },
      {
        title: 'Bounce Rate',
        value: formatPercentage(analytics.bounce_rate),
        subtitle: `${formatNumber(analytics.total_bounced)} bounced`,
        icon: TrendingDown,
        color: 'bg-red-500'
      },
      {
        title: 'Unsubscribe Rate',
        value: formatPercentage(analytics.unsubscribe_rate),
        subtitle: `${formatNumber(analytics.total_unsubscribed)} unsubscribed`,
        icon: UserMinus,
        color: 'bg-orange-500'
      }
    ]
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="flex justify-center items-center py-16">
              <div className="text-center">
                <RefreshCw className="animate-spin h-12 w-12 text-blue-500 mx-auto mb-4" />
                <p className="text-xl text-gray-600 font-medium">Loading analytics...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="px-6 py-8 sm:p-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => navigate('/campaigns')}
                    className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <ArrowLeft className="h-6 w-6 text-gray-600" />
                  </button>
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                      📊 Campaign Analytics
                    </h1>
                    {campaign && (
                      <p className="text-xl text-gray-600 mt-2">
                        {campaign.name}
                      </p>
                    )}
                  </div>
                </div>
                
                <button
                  onClick={loadAnalytics}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </button>
              </div>
            </div>
          </div>

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border-2 border-red-200 rounded-xl p-6">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-6 w-6 text-red-500 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-red-800 mb-1">Error</h3>
                  <p className="text-red-700 text-base leading-relaxed">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Campaign Info */}
          {campaign && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="px-6 py-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <Mail className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-500">Type</div>
                      <div className="text-lg font-semibold text-gray-900">{campaign.type}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-green-100 rounded-lg">
                      <BarChart3 className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-500">Status</div>
                      <div className="text-lg font-semibold text-gray-900">{campaign.status}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-purple-100 rounded-lg">
                      <Calendar className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-500">Created</div>
                      <div className="text-lg font-semibold text-gray-900">
                        {new Date(campaign.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  
                  {analytics?.last_sent_at && (
                    <div className="flex items-center space-x-3">
                      <div className="p-3 bg-orange-100 rounded-lg">
                        <Users className="h-6 w-6 text-orange-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-500">Last Sent</div>
                        <div className="text-lg font-semibold text-gray-900">
                          {new Date(analytics.last_sent_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Metrics Cards */}
          {analytics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {getMetricCards().map((metric, index) => (
                <div key={index} className="bg-white shadow-lg rounded-xl border border-gray-100 overflow-hidden">
                  <div className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-3 ${metric.color} rounded-lg`}>
                          <metric.icon className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-500">{metric.title}</div>
                          <div className="text-2xl font-bold text-gray-900">{metric.value}</div>
                          <div className="text-sm text-gray-600">{metric.subtitle}</div>
                        </div>
                      </div>
                      
                      {metric.trend && (
                        <div className={`flex items-center space-x-1 ${
                          metric.trend.isPositive ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {metric.trend.isPositive ? (
                            <TrendingUp className="h-4 w-4" />
                          ) : (
                            <TrendingDown className="h-4 w-4" />
                          )}
                          <span className="text-sm font-medium">
                            {Math.abs(metric.trend.value)}%
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Performance Summary */}
          {analytics && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="px-6 py-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Performance Summary</h2>
                
                <div className="space-y-6">
                  {/* Delivery Performance */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">Delivery Performance</span>
                      <span className="text-sm text-gray-500">
                        {formatNumber(analytics.total_delivered)} / {formatNumber(analytics.total_sent)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-green-500 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${analytics.delivery_rate / 100}%` }}
                      ></div>
                    </div>
                    <div className="text-right text-sm text-gray-600 mt-1">
                      {formatPercentage(analytics.delivery_rate)}
                    </div>
                  </div>

                  {/* Engagement Performance */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">Open Rate</span>
                      <span className="text-sm text-gray-500">
                        {formatNumber(analytics.total_opened)} / {formatNumber(analytics.total_delivered)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-purple-500 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${analytics.open_rate / 100}%` }}
                      ></div>
                    </div>
                    <div className="text-right text-sm text-gray-600 mt-1">
                      {formatPercentage(analytics.open_rate)}
                    </div>
                  </div>

                  {/* Click Performance */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">Click Rate</span>
                      <span className="text-sm text-gray-500">
                        {formatNumber(analytics.total_clicked)} / {formatNumber(analytics.total_opened)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-indigo-500 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${analytics.click_rate / 100}%` }}
                      ></div>
                    </div>
                    <div className="text-right text-sm text-gray-600 mt-1">
                      {formatPercentage(analytics.click_rate)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Insights and Recommendations */}
          {analytics && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="px-6 py-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">📈 Insights & Recommendations</h2>
                
                <div className="space-y-4">
                  {analytics.delivery_rate < 9500 && (
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-yellow-800">Low Delivery Rate</h4>
                          <p className="text-sm text-yellow-700 mt-1">
                            Your delivery rate is {formatPercentage(analytics.delivery_rate)}. 
                            Consider cleaning your email list and checking for spam triggers.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {analytics.open_rate < 2000 && (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <Eye className="h-5 w-5 text-blue-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-blue-800">Improve Open Rates</h4>
                          <p className="text-sm text-blue-700 mt-1">
                            Your open rate is {formatPercentage(analytics.open_rate)}. 
                            Try A/B testing subject lines and optimizing send times.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {analytics.click_rate < 300 && analytics.total_opened > 0 && (
                    <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <MousePointer className="h-5 w-5 text-purple-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-purple-800">Boost Click Rates</h4>
                          <p className="text-sm text-purple-700 mt-1">
                            Your click rate is {formatPercentage(analytics.click_rate)}. 
                            Consider improving your call-to-action buttons and email content relevance.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {analytics.delivery_rate >= 9500 && analytics.open_rate >= 2000 && analytics.click_rate >= 300 && (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <TrendingUp className="h-5 w-5 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-green-800">Great Performance! 🎉</h4>
                          <p className="text-sm text-green-700 mt-1">
                            Your campaign is performing well across all metrics. 
                            Keep up the good work and consider scaling similar campaigns.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* No Data State */}
          {!analytics && !error && !isLoading && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="text-center py-16 px-8">
                <BarChart3 className="h-20 w-20 text-gray-300 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  📊 No Analytics Data
                </h3>
                <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
                  This campaign hasn't been executed yet or no analytics data is available.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CampaignAnalytics