import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Save,
  ArrowLeft,
  Mail,
  Users,
  Zap,
  Calendar,
  Tag,
  AlertCircle,
  CheckCircle
} from 'lucide-react'

// Define types
interface Campaign {
  campaign_id?: string
  name: string
  description: string
  type: 'Email' | 'SMS' | 'Push' | 'InApp'
  status: 'Draft' | 'Active' | 'Paused' | 'Completed' | 'Archived'
  trigger_type: string
  trigger_config?: Record<string, any>
  audience_type: string
  audience_rules?: Record<string, any>
  template_id?: string
  rule_id?: string
  start_date?: string
  end_date?: string
  tags: string[]
}

interface Template {
  template_id: string
  name: string
  type: string
  subject: string
}

interface Rule {
  rule_id: string
  name: string
  description: string
}

const triggerTypes = [
  { value: 'Manual', label: 'Manual Execution', description: 'Execute manually when needed' },
  { value: 'UserRegistration', label: 'User Registration', description: 'Trigger when a new user registers' },
  { value: 'CaseClosed', label: 'Case Closed', description: 'Trigger when a case is closed' },
  { value: 'ContactCreated', label: 'Contact Created', description: 'Trigger when a new contact is created' },
  { value: 'CaseAging', label: 'Case Aging', description: 'Trigger based on case age' },
  { value: 'LeadStatusChange', label: 'Lead Status Change', description: 'Trigger when lead status changes' },
  { value: 'AppointmentScheduled', label: 'Appointment Scheduled', description: 'Trigger when appointment is scheduled' },
  { value: 'DocumentUploaded', label: 'Document Uploaded', description: 'Trigger when document is uploaded' },
  { value: 'Custom', label: 'Custom', description: 'Custom trigger conditions' }
]

const audienceTypes = [
  { value: 'AllUsers', label: 'All Users', description: 'Target all users in the system' },
  { value: 'Doctors', label: 'Doctors', description: 'Target only doctors' },
  { value: 'Patients', label: 'Patients', description: 'Target only patients' },
  { value: 'Agents', label: 'Agents', description: 'Target only agents' },
  { value: 'Admins', label: 'Admins', description: 'Target only administrators' },
  { value: 'Custom', label: 'Custom', description: 'Custom audience rules' }
]

export function CampaignForm() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const { campaignId } = useParams()
  const isEditing = Boolean(campaignId)

  const [campaign, setCampaign] = useState<Campaign>({
    name: '',
    description: '',
    type: 'Email',
    status: 'Draft',
    trigger_type: 'Manual',
    audience_type: 'AllUsers',
    tags: []
  })

  const [templates, setTemplates] = useState<Template[]>([])
  const [rules, setRules] = useState<Rule[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [newTag, setNewTag] = useState('')

  // Load campaign data if editing
  useEffect(() => {
    if (isEditing && campaignId) {
      loadCampaign(campaignId)
    }
  }, [isEditing, campaignId])

  // Load templates and rules
  useEffect(() => {
    loadTemplates()
    loadRules()
  }, [])

  const loadCampaign = async (id: string) => {
    try {
      setIsLoading(true)
      // @ts-ignore
      const response = await (apiClient as any).request(`/crm/campaigns/${id}`) as any
      setCampaign(response.data)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load campaign:', error)
      setError('Failed to load campaign. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const loadTemplates = async () => {
    try {
      // @ts-ignore
      const response = await (apiClient as any).request('/crm/campaigns/templates') as any
      setTemplates(response.data || [])
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load templates:', error)
    }
  }

  const loadRules = async () => {
    try {
      // @ts-ignore
      const response = await (apiClient as any).request('/crm/campaigns/rules') as any
      setRules(response.data || [])
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load rules:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const payload = {
        ...campaign,
        start_date: campaign.start_date || undefined,
        end_date: campaign.end_date || undefined
      }

      if (isEditing) {
        // @ts-ignore
        await (apiClient as any).request(`/crm/campaigns/${campaignId}`, {
          method: 'PUT',
          body: JSON.stringify(payload)
        })
        setSuccess('Campaign updated successfully!')
      } else {
        // @ts-ignore
        await (apiClient as any).request('/crm/campaigns', {
          method: 'POST',
          body: JSON.stringify(payload)
        })
        setSuccess('Campaign created successfully!')
        setTimeout(() => navigate('/campaigns'), 2000)
      }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to save campaign:', error)
      setError('Failed to save campaign. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field: keyof Campaign, value: any) => {
    setCampaign(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addTag = () => {
    if (newTag.trim() && !campaign.tags.includes(newTag.trim())) {
      setCampaign(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setCampaign(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white shadow rounded-lg border border-gray-200">
            <div className="flex justify-center items-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-xl text-gray-600 font-medium">Loading campaign...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        {/* Compact Header */}
        <div className="bg-white shadow rounded-lg border border-gray-200 mb-4">
          <div className="px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => navigate('/campaigns')}
                  className="p-1.5 rounded hover:bg-gray-100 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    {isEditing ? '✏️ Edit Campaign' : '📧 Create Campaign'}
                  </h1>
                  <p className="text-sm text-gray-600">
                    {isEditing ? 'Update your email campaign settings' : 'Set up a new automated email campaign'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-semibold text-red-800">Error</h3>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
            <div className="flex items-start space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-semibold text-green-800">Success</h3>
                <p className="text-sm text-green-700">{success}</p>
              </div>
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="bg-white shadow rounded-lg border border-gray-200">
            <div className="px-4 py-3 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                <Mail className="h-5 w-5 text-blue-600" />
                <h2 className="text-lg font-semibold text-gray-900">Basic Information</h2>
              </div>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Campaign Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={campaign.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter campaign name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type *
                  </label>
                  <select
                    required
                    value={campaign.type}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="Email">📧 Email</option>
                    <option value="SMS">💬 SMS</option>
                    <option value="Push">🔔 Push</option>
                    <option value="InApp">📲 In-App</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status *
                  </label>
                  <select
                    required
                    value={campaign.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="Draft">📝 Draft</option>
                    <option value="Active">✅ Active</option>
                    <option value="Paused">⏸️ Paused</option>
                    <option value="Completed">🏁 Completed</option>
                    <option value="Archived">📦 Archived</option>
                  </select>
                </div>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={campaign.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describe your campaign"
                />
              </div>
            </div>
          </div>

          {/* Trigger Configuration */}
          <div className="bg-white shadow rounded-lg border border-gray-200">
            <div className="px-4 py-3 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-yellow-600" />
                <h2 className="text-lg font-semibold text-gray-900">Trigger Configuration</h2>
              </div>
            </div>
            <div className="p-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Trigger Type *
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {triggerTypes.map((trigger) => (
                    <div
                      key={trigger.value}
                      className={`p-3 border rounded-lg cursor-pointer transition-all ${
                        campaign.trigger_type === trigger.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleInputChange('trigger_type', trigger.value)}
                    >
                      <div className="font-medium text-gray-900 text-sm">{trigger.label}</div>
                      <div className="text-xs text-gray-600 mt-1">{trigger.description}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Audience Configuration */}
          <div className="bg-white shadow rounded-lg border border-gray-200">
            <div className="px-4 py-3 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-green-600" />
                <h2 className="text-lg font-semibold text-gray-900">Audience Configuration</h2>
              </div>
            </div>
            <div className="p-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Audience Type *
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {audienceTypes.map((audience) => (
                    <div
                      key={audience.value}
                      className={`p-3 border rounded-lg cursor-pointer transition-all ${
                        campaign.audience_type === audience.value
                          ? 'border-green-500 bg-green-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleInputChange('audience_type', audience.value)}
                    >
                      <div className="font-medium text-gray-900 text-sm">{audience.label}</div>
                      <div className="text-xs text-gray-600 mt-1">{audience.description}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Template and Rules */}
          <div className="bg-white shadow rounded-lg border border-gray-200">
            <div className="px-4 py-3 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                <Mail className="h-5 w-5 text-purple-600" />
                <h2 className="text-lg font-semibold text-gray-900">Template & Rules</h2>
              </div>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Template
                  </label>
                  <select
                    value={campaign.template_id || ''}
                    onChange={(e) => handleInputChange('template_id', e.target.value || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select a template</option>
                    {templates.map((template) => (
                      <option key={template.template_id} value={template.template_id}>
                        {template.name} - {template.subject}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Campaign Rules
                  </label>
                  <select
                    value={campaign.rule_id || ''}
                    onChange={(e) => handleInputChange('rule_id', e.target.value || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select rules</option>
                    {rules.map((rule) => (
                      <option key={rule.rule_id} value={rule.rule_id}>
                        {rule.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Scheduling & Tags */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Scheduling */}
            <div className="bg-white shadow rounded-lg border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-indigo-600" />
                  <h2 className="text-lg font-semibold text-gray-900">Scheduling</h2>
                </div>
              </div>
              <div className="p-4">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Start Date
                    </label>
                    <input
                      type="datetime-local"
                      value={campaign.start_date || ''}
                      onChange={(e) => handleInputChange('start_date', e.target.value || undefined)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      End Date
                    </label>
                    <input
                      type="datetime-local"
                      value={campaign.end_date || ''}
                      onChange={(e) => handleInputChange('end_date', e.target.value || undefined)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="bg-white shadow rounded-lg border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200">
                <div className="flex items-center space-x-2">
                  <Tag className="h-5 w-5 text-pink-600" />
                  <h2 className="text-lg font-semibold text-gray-900">Tags</h2>
                </div>
              </div>
              <div className="p-4">
                <div className="space-y-3">
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Add a tag"
                    />
                    <button
                      type="button"
                      onClick={addTag}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Add
                    </button>
                  </div>
                  {campaign.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {campaign.tags.map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-1 text-blue-600 hover:text-blue-800"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="bg-white shadow rounded-lg border border-gray-200">
            <div className="p-4">
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => navigate('/campaigns')}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSaving}
                  className="inline-flex items-center px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      {isEditing ? 'Update' : 'Create'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}

export default CampaignForm