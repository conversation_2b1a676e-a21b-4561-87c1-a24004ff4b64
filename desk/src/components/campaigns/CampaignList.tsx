import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Search,
  Mail,
  Plus,
  RefreshCw,
  Grid3X3,
  List,
  MoreVertical,
  Edit,
  Trash2,
  Play,
  Bar<PERSON>hart3,
  Filter,
  X
} from 'lucide-react'

// Define types
interface Campaign {
  campaign_id: string
  name: string
  description: string
  type: 'Email' | 'SMS' | 'Push' | 'InApp'
  status: 'Draft' | 'Active' | 'Paused' | 'Completed' | 'Archived'
  trigger_type: string
  audience_type: string
  created_at: string
  updated_at: string
}

// Campaign type and status configurations for consistent UI
const typeConfig = {
  Email: { label: 'Email', color: 'bg-blue-100 text-blue-800', icon: Mail },
  SMS: { label: 'SMS', color: 'bg-green-100 text-green-800', icon: Mail },
  Push: { label: 'Push', color: 'bg-purple-100 text-purple-800', icon: Mail },
  InApp: { label: 'In-App', color: 'bg-orange-100 text-orange-800', icon: Mail }
}

const statusConfig = {
  Draft: { label: 'Draft', color: 'bg-gray-100 text-gray-800' },
  Active: { label: 'Active', color: 'bg-green-100 text-green-800' },
  Paused: { label: 'Paused', color: 'bg-yellow-100 text-yellow-800' },
  Completed: { label: 'Completed', color: 'bg-blue-100 text-blue-800' },
  Archived: { label: 'Archived', color: 'bg-gray-100 text-gray-800' }
}

export function CampaignList() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  
  // View mode state (card, list)
  const [viewMode, setViewMode] = useState<'card' | 'list'>(
    user?.role === 'admin' ? 'list' : 'card'
  )
  
  // Action menu state
  const [showActionMenu, setShowActionMenu] = useState<string | null>(null)
  const [showDeleteModal, setShowDeleteModal] = useState<Campaign | null>(null)

  // Load campaigns from API
  useEffect(() => {
    const loadCampaigns = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        // Build query parameters
        const params = new URLSearchParams({
          page: page.toString(),
          limit: '10'
        })
        if (searchTerm) params.append('search', searchTerm)
        if (statusFilter !== 'all') params.append('status', statusFilter)
        if (typeFilter !== 'all') params.append('type', typeFilter)
        
        // @ts-ignore - apiClient.request is private but used throughout the codebase
        const response = await (apiClient as any).request(`/crm/campaigns?${params.toString()}`) as any
        
        setCampaigns(response.data || [])
        setTotalPages(response.pagination?.pages || 1)
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load campaigns:', error)
        setError('Failed to load campaigns. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    loadCampaigns()
  }, [statusFilter, typeFilter, searchTerm, page])

  // Handle campaign execution
  const handleExecuteCampaign = async (campaignId: string) => {
    try {
      // @ts-ignore
      await (apiClient as any).request(`/crm/campaigns/${campaignId}/execute`, {
        method: 'POST'
      })
      
      // Refresh campaigns list
      setIsLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10'
      })
      if (searchTerm) params.append('search', searchTerm)
      if (statusFilter !== 'all') params.append('status', statusFilter)
      if (typeFilter !== 'all') params.append('type', typeFilter)
      
      // @ts-ignore
      const response = await (apiClient as any).request(`/crm/campaigns?${params.toString()}`) as any
      setCampaigns(response.data || [])
      setShowActionMenu(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to execute campaign:', error)
      setError('Failed to execute campaign. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle campaign deletion
  const handleDeleteCampaign = async (campaign: Campaign) => {
    try {
      // @ts-ignore
      await (apiClient as any).request(`/crm/campaigns/${campaign.campaign_id}`, {
        method: 'DELETE'
      })
      
      // Refresh campaigns list
      setCampaigns(campaigns.filter(c => c.campaign_id !== campaign.campaign_id))
      setShowDeleteModal(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to delete campaign:', error)
      setError('Failed to delete campaign. Please try again.')
    }
  }

  // Filter campaigns based on search and filters
  const filteredCampaigns = campaigns.filter(campaign => {
    // Search filter
    const matchesSearch = searchTerm
      ? campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        campaign.description?.toLowerCase().includes(searchTerm.toLowerCase())
      : true
    
    // Status filter
    const matchesStatus = statusFilter === 'all' ? true : campaign.status === statusFilter
    
    // Type filter
    const matchesType = typeFilter === 'all' ? true : campaign.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  // Campaign Card Component
  const CampaignCard = ({ campaign }: { campaign: Campaign }) => (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow duration-200">
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{campaign.name}</h3>
            {campaign.description && (
              <p className="text-sm text-gray-600 mb-3">{campaign.description}</p>
            )}
          </div>
          <div className="relative">
            <button
              onClick={() => setShowActionMenu(showActionMenu === campaign.campaign_id ? null : campaign.campaign_id)}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <MoreVertical className="h-5 w-5 text-gray-500" />
            </button>
            
            {showActionMenu === campaign.campaign_id && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button
                    onClick={() => navigate(`/campaigns/${campaign.campaign_id}`)}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </button>
                  <button
                    onClick={() => navigate(`/campaigns/${campaign.campaign_id}/analytics`)}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Analytics
                  </button>
                  {campaign.status === 'Active' && (
                    <button
                      onClick={() => handleExecuteCampaign(campaign.campaign_id)}
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Execute Now
                    </button>
                  )}
                  <button
                    onClick={() => setShowDeleteModal(campaign)}
                    className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2 mb-4">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeConfig[campaign.type].color}`}>
            {typeConfig[campaign.type].label}
          </span>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[campaign.status].color}`}>
            {statusConfig[campaign.status].label}
          </span>
        </div>
        
        <div className="text-sm text-gray-500 space-y-1">
          <div>Trigger: {campaign.trigger_type}</div>
          <div>Audience: {campaign.audience_type}</div>
          <div>Created: {new Date(campaign.created_at).toLocaleDateString()}</div>
        </div>
      </div>
    </div>
  )

  // Campaign List Row Component
  const CampaignRow = ({ campaign }: { campaign: Campaign }) => (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        <div>
          <div className="text-sm font-medium text-gray-900">{campaign.name}</div>
          {campaign.description && (
            <div className="text-sm text-gray-500">{campaign.description}</div>
          )}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeConfig[campaign.type].color}`}>
          {typeConfig[campaign.type].label}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[campaign.status].color}`}>
          {statusConfig[campaign.status].label}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {campaign.trigger_type}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {campaign.audience_type}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {new Date(campaign.created_at).toLocaleDateString()}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="relative">
          <button
            onClick={() => setShowActionMenu(showActionMenu === campaign.campaign_id ? null : campaign.campaign_id)}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <MoreVertical className="h-5 w-5 text-gray-500" />
          </button>
          
          {showActionMenu === campaign.campaign_id && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <div className="py-1">
                <button
                  onClick={() => navigate(`/campaigns/${campaign.campaign_id}`)}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </button>
                <button
                  onClick={() => navigate(`/campaigns/${campaign.campaign_id}/analytics`)}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analytics
                </button>
                {campaign.status === 'Active' && (
                  <button
                    onClick={() => handleExecuteCampaign(campaign.campaign_id)}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Execute Now
                  </button>
                )}
                <button
                  onClick={() => setShowDeleteModal(campaign)}
                  className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </button>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="px-6 py-8 sm:p-8">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-3">
                    📧 Email Campaigns
                  </h1>
                  <p className="text-xl text-gray-600 leading-relaxed">
                    Manage automated email campaigns and templates
                  </p>
                </div>
                
                <div className="mt-6 md:mt-0">
                  <button
                    onClick={() => navigate('/campaigns/create')}
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Create Campaign
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="px-6 py-6">
              <div className="flex flex-col lg:flex-row gap-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="text"
                      placeholder="🔍 Search campaigns..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-12 pr-4 py-3 w-full text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-4 py-3 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                  >
                    <option value="all">📋 All Statuses</option>
                    <option value="Draft">📝 Draft</option>
                    <option value="Active">✅ Active</option>
                    <option value="Paused">⏸️ Paused</option>
                    <option value="Completed">🏁 Completed</option>
                    <option value="Archived">📦 Archived</option>
                  </select>
                  
                  <select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    className="px-4 py-3 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                  >
                    <option value="all">📱 All Types</option>
                    <option value="Email">📧 Email</option>
                    <option value="SMS">💬 SMS</option>
                    <option value="Push">🔔 Push</option>
                    <option value="InApp">📲 In-App</option>
                  </select>
                  
                  {/* View Mode Toggle */}
                  <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-2">
                    <button
                      onClick={() => setViewMode('card')}
                      className={`p-3 rounded-lg transition-all duration-200 ${
                        viewMode === 'card'
                          ? 'bg-white text-blue-600 shadow-md transform scale-105'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                      title="Card View"
                    >
                      <Grid3X3 className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-3 rounded-lg transition-all duration-200 ${
                        viewMode === 'list'
                          ? 'bg-white text-blue-600 shadow-md transform scale-105'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                      title="List View"
                    >
                      <List className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="flex justify-center items-center py-16">
                <div className="text-center">
                  <RefreshCw className="animate-spin h-12 w-12 text-blue-500 mx-auto mb-4" />
                  <p className="text-xl text-gray-600 font-medium">Loading campaigns...</p>
                </div>
              </div>
            </div>
          )}
          
          {/* Error State */}
          {error && (
            <div className="bg-red-50 border-2 border-red-200 rounded-xl p-6">
              <div className="flex items-start space-x-3">
                <div className="text-red-500 text-2xl">⚠️</div>
                <div>
                  <h3 className="text-lg font-semibold text-red-800 mb-1">Something went wrong</h3>
                  <p className="text-red-700 text-base leading-relaxed">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !error && filteredCampaigns.length === 0 && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="text-center py-16 px-8">
                <div className="mb-6">
                  <Mail className="h-20 w-20 text-blue-300 mx-auto mb-4" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  📧 No campaigns found
                </h3>
                <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
                  {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                    ? 'Try adjusting your filters or search term to find what you\'re looking for'
                    : 'Create your first email campaign to start engaging with your audience'}
                </p>
                <button
                  onClick={() => navigate('/campaigns/create')}
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-xl rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
                >
                  <Plus className="h-6 w-6 mr-3" />
                  Create Campaign
                </button>
              </div>
            </div>
          )}

          {/* Campaigns Content */}
          {!isLoading && !error && filteredCampaigns.length > 0 && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              {viewMode === 'card' ? (
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredCampaigns.map((campaign) => (
                      <CampaignCard key={campaign.campaign_id} campaign={campaign} />
                    ))}
                  </div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Campaign
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Trigger
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Audience
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Created
                        </th>
                        <th className="relative px-6 py-3">
                          <span className="sr-only">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredCampaigns.map((campaign) => (
                        <CampaignRow key={campaign.campaign_id} campaign={campaign} />
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      Page {page} of {totalPages}
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setPage(Math.max(1, page - 1))}
                        disabled={page === 1}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>
                      <button
                        onClick={() => setPage(Math.min(totalPages, page + 1))}
                        disabled={page === totalPages}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <Trash2 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete Campaign</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete "{showDeleteModal.name}"? This action cannot be undone.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowDeleteModal(null)}
                    className="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => handleDeleteCampaign(showDeleteModal)}
                    className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close action menu */}
      {showActionMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowActionMenu(null)}
        />
      )}
    </div>
  )
}

export default CampaignList