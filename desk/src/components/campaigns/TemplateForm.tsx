import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Save,
  ArrowLeft,
  Mail,
  FileText,
  Eye,
  Code,
  AlertCircle,
  CheckCircle
} from 'lucide-react'

// Define types
interface Template {
  template_id?: string
  name: string
  type: 'Welcome' | 'FollowUp' | 'Reminder' | 'Notification' | 'Marketing' | 'Transactional' | 'Custom'
  subject: string
  html_content: string
  variables: string[]
  is_active: boolean
}

const templateTypes = [
  { value: 'Welcome', label: '👋 Welcome Template', description: 'Welcome new users' },
  { value: 'FollowUp', label: '📞 Follow-up Template', description: 'Follow-up communications' },
  { value: 'Reminder', label: '⏰ Reminder Template', description: 'Appointment and task reminders' },
  { value: 'Notification', label: '🔔 Notification Template', description: 'System notifications' },
  { value: 'Marketing', label: '📢 Marketing Template', description: 'Marketing campaigns' },
  { value: 'Transactional', label: '💼 Transactional Template', description: 'Transaction confirmations' },
  { value: 'Custom', label: '🎨 Custom Template', description: 'Custom template type' }
]

const commonVariables = [
  '{{user.name}}',
  '{{user.email}}',
  '{{user.role}}',
  '{{case.id}}',
  '{{case.title}}',
  '{{case.status}}',
  '{{contact.name}}',
  '{{contact.email}}',
  '{{organization.name}}',
  '{{appointment.date}}',
  '{{appointment.time}}',
  '{{document.name}}',
  '{{current.date}}',
  '{{current.time}}'
]

export function TemplateForm() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const { templateId } = useParams()
  const isEditing = Boolean(templateId)

  const [template, setTemplate] = useState<Template>({
    name: '',
    type: 'Welcome',
    subject: '',
    html_content: '',
    variables: [],
    is_active: true
  })

  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [previewMode, setPreviewMode] = useState(false)

  // Load template data if editing
  useEffect(() => {
    if (isEditing && templateId) {
      loadTemplate(templateId)
    }
  }, [isEditing, templateId])

  const loadTemplate = async (id: string) => {
    try {
      setIsLoading(true)
      // @ts-ignore
      const response = await (apiClient as any).request(`/crm/campaigns/templates/${id}`) as any
      setTemplate(response.data)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to load template:', error)
      setError('Failed to load template. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const payload = {
        ...template,
        variables: extractVariables(template.html_content)
      }

      if (isEditing) {
        // @ts-ignore
        await (apiClient as any).request(`/crm/campaigns/templates/${templateId}`, {
          method: 'PUT',
          body: JSON.stringify(payload)
        })
        setSuccess('Template updated successfully!')
      } else {
        // @ts-ignore
        await (apiClient as any).request('/crm/campaigns/templates', {
          method: 'POST',
          body: JSON.stringify(payload)
        })
        setSuccess('Template created successfully!')
        setTimeout(() => navigate('/campaigns/templates'), 2000)
      }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to save template:', error)
      setError('Failed to save template. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field: keyof Template, value: any) => {
    setTemplate(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const extractVariables = (content: string): string[] => {
    const variableRegex = /\{\{([^}]+)\}\}/g
    const matches = content.match(variableRegex) || []
    return [...new Set(matches)]
  }

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById('template-content') as HTMLTextAreaElement
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const newContent = template.html_content.substring(0, start) + variable + template.html_content.substring(end)
      handleInputChange('html_content', newContent)
      
      // Restore cursor position
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + variable.length, start + variable.length)
      }, 0)
    }
  }

  const renderPreview = () => {
    let previewContent = template.html_content
    
    // Replace variables with sample data for preview
    const sampleData: Record<string, string> = {
      '{{user.name}}': 'John Doe',
      '{{user.email}}': '<EMAIL>',
      '{{user.role}}': 'Patient',
      '{{case.id}}': 'CASE-001',
      '{{case.title}}': 'Medical Consultation',
      '{{case.status}}': 'Active',
      '{{contact.name}}': 'Dr. Smith',
      '{{contact.email}}': '<EMAIL>',
      '{{organization.name}}': 'City Hospital',
      '{{appointment.date}}': '2024-01-15',
      '{{appointment.time}}': '10:00 AM',
      '{{document.name}}': 'Medical Report.pdf',
      '{{current.date}}': new Date().toLocaleDateString(),
      '{{current.time}}': new Date().toLocaleTimeString()
    }

    Object.entries(sampleData).forEach(([variable, value]) => {
      previewContent = previewContent.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value)
    })

    return previewContent
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="flex justify-center items-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-xl text-gray-600 font-medium">Loading template...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        {/* Compact Header */}
        <div className="bg-white shadow rounded-lg border border-gray-200 mb-4">
          <div className="px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => navigate('/campaigns/templates')}
                  className="p-1.5 rounded hover:bg-gray-100 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    {isEditing ? '✏️ Edit Template' : '📝 Create Template'}
                  </h1>
                  <p className="text-sm text-gray-600">
                    {isEditing ? 'Update your email template' : 'Create a new email template for campaigns'}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  type="button"
                  onClick={() => setPreviewMode(!previewMode)}
                  className={`inline-flex items-center px-3 py-1.5 text-sm rounded transition-colors ${
                    previewMode
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'bg-gray-100 text-gray-700 border border-gray-200'
                  }`}
                >
                  {previewMode ? <Code className="h-4 w-4 mr-1" /> : <Eye className="h-4 w-4 mr-1" />}
                  {previewMode ? 'Edit' : 'Preview'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-semibold text-red-800">Error</h3>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
            <div className="flex items-start space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-semibold text-green-800">Success</h3>
                <p className="text-sm text-green-700">{success}</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          {/* Main Form - 3 columns */}
          <div className="lg:col-span-3 space-y-4">
            {/* Basic Information */}
            <div className="bg-white shadow rounded-lg border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200">
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <h2 className="text-lg font-semibold text-gray-900">Template Information</h2>
                </div>
              </div>
              <div className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Template Name *
                    </label>
                    <input
                      type="text"
                      required
                      value={template.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter template name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Template Type *
                    </label>
                    <select
                      required
                      value={template.type}
                      onChange={(e) => handleInputChange('type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {templateTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="flex items-center pt-6">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={template.is_active}
                        onChange={(e) => handleInputChange('is_active', e.target.checked)}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Active</span>
                    </label>
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Subject *
                  </label>
                  <input
                    type="text"
                    required
                    value={template.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter email subject"
                  />
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="bg-white shadow rounded-lg border border-gray-200 flex-1">
              <div className="px-4 py-3 border-b border-gray-200">
                <div className="flex items-center space-x-2">
                  <Mail className="h-5 w-5 text-purple-600" />
                  <h2 className="text-lg font-semibold text-gray-900">Template Content</h2>
                </div>
              </div>
              <div className="p-4">
                <div className="h-96">
                  {previewMode ? (
                    <div className="w-full h-full p-3 border border-gray-300 rounded-md bg-gray-50 overflow-auto">
                      <div className="prose max-w-none">
                        <div dangerouslySetInnerHTML={{ __html: renderPreview() }} />
                      </div>
                    </div>
                  ) : (
                    <textarea
                      id="template-content"
                      required
                      value={template.html_content}
                      onChange={(e) => handleInputChange('html_content', e.target.value)}
                      className="w-full h-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm resize-none"
                      placeholder="Enter HTML content for your email template..."
                    />
                  )}
                </div>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="bg-white shadow rounded-lg border border-gray-200">
              <div className="p-4">
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => navigate('/campaigns/templates')}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSaving}
                    className="inline-flex items-center px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSaving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        {isEditing ? 'Update' : 'Create'}
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Variables Sidebar - 1 column */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg border border-gray-200 sticky top-4">
              <div className="px-4 py-3 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Variables</h3>
                <p className="text-xs text-gray-600 mt-1">
                  Click to insert into template
                </p>
              </div>
              <div className="p-3">
                <div className="space-y-1 max-h-80 overflow-y-auto">
                  {commonVariables.map((variable) => (
                    <button
                      key={variable}
                      type="button"
                      onClick={() => insertVariable(variable)}
                      className="w-full text-left px-2 py-1.5 text-xs bg-gray-50 hover:bg-blue-50 hover:text-blue-700 rounded transition-colors border border-gray-200 hover:border-blue-200"
                    >
                      <code className="font-mono">{variable}</code>
                    </button>
                  ))}
                </div>
                <div className="mt-3 p-2 bg-blue-50 rounded border border-blue-200">
                  <h4 className="text-xs font-semibold text-blue-800 mb-1">💡 Tips</h4>
                  <ul className="text-xs text-blue-700 space-y-0.5">
                    <li>• Variables personalize templates</li>
                    <li>• Preview shows sample data</li>
                    <li>• HTML is supported</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}

export default TemplateForm