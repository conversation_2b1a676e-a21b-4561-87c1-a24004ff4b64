import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Search,
  Mail,
  Plus,
  RefreshCw,
  Grid3X3,
  List,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Copy,
  Filter,
  FileText
} from 'lucide-react'

// Define types
interface Template {
  template_id: string
  name: string
  description: string
  type: 'Welcome' | 'FollowUp' | 'Reminder' | 'Notification' | 'Marketing' | 'Transactional' | 'Custom'
  subject: string
  html_content: string
  text_content?: string
  variables: string[]
  is_active: boolean
  created_at: string
  updated_at: string
}

// Template type configurations for consistent UI
const typeConfig = {
  Welcome: { label: 'Welcome', color: 'bg-green-100 text-green-800', emoji: '👋' },
  FollowUp: { label: 'Follow Up', color: 'bg-blue-100 text-blue-800', emoji: '📞' },
  Reminder: { label: 'Reminder', color: 'bg-yellow-100 text-yellow-800', emoji: '⏰' },
  Notification: { label: 'Notification', color: 'bg-purple-100 text-purple-800', emoji: '🔔' },
  Marketing: { label: 'Marketing', color: 'bg-pink-100 text-pink-800', emoji: '📢' },
  Transactional: { label: 'Transactional', color: 'bg-indigo-100 text-indigo-800', emoji: '💳' },
  Custom: { label: 'Custom', color: 'bg-gray-100 text-gray-800', emoji: '⚙️' }
}

export function TemplateList() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [templates, setTemplates] = useState<Template[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  
  // View mode state (card, list)
  const [viewMode, setViewMode] = useState<'card' | 'list'>(
    user?.role === 'admin' ? 'list' : 'card'
  )
  
  // Action menu state
  const [showActionMenu, setShowActionMenu] = useState<string | null>(null)
  const [showDeleteModal, setShowDeleteModal] = useState<Template | null>(null)
  const [showPreviewModal, setShowPreviewModal] = useState<Template | null>(null)

  // Load templates from API
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        // Build query parameters
        const params = new URLSearchParams({
          page: page.toString(),
          limit: '12'
        })
        if (searchTerm) params.append('search', searchTerm)
        if (typeFilter !== 'all') params.append('type', typeFilter)
        if (statusFilter !== 'all') params.append('is_active', statusFilter === 'active' ? 'true' : 'false')
        
        // @ts-ignore - apiClient.request is private but used throughout the codebase
        const response = await (apiClient as any).request(`/crm/campaigns/templates?${params.toString()}`) as any
        
        setTemplates(response.data || [])
        setTotalPages(response.pagination?.pages || 1)
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load templates:', error)
        setError('Failed to load templates. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    loadTemplates()
  }, [typeFilter, statusFilter, searchTerm, page])

  // Handle template deletion
  const handleDeleteTemplate = async (template: Template) => {
    try {
      // @ts-ignore
      await (apiClient as any).request(`/crm/campaigns/templates/${template.template_id}`, {
        method: 'DELETE'
      })
      
      // Refresh templates list
      setTemplates(templates.filter(t => t.template_id !== template.template_id))
      setShowDeleteModal(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to delete template:', error)
      setError('Failed to delete template. Please try again.')
    }
  }

  // Handle template duplication
  const handleDuplicateTemplate = async (template: Template) => {
    try {
      const duplicatedTemplate = {
        name: `${template.name} (Copy)`,
        description: template.description,
        type: template.type,
        subject: template.subject,
        html_content: template.html_content,
        text_content: template.text_content,
        variables: template.variables,
        is_active: false // Start as inactive
      }

      // @ts-ignore
      await (apiClient as any).request('/crm/campaigns/templates', {
        method: 'POST',
        body: JSON.stringify(duplicatedTemplate)
      })
      
      // Refresh templates list
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12'
      })
      if (searchTerm) params.append('search', searchTerm)
      if (typeFilter !== 'all') params.append('type', typeFilter)
      if (statusFilter !== 'all') params.append('is_active', statusFilter === 'active' ? 'true' : 'false')
      
      // @ts-ignore
      const response = await (apiClient as any).request(`/crm/campaigns/templates?${params.toString()}`) as any
      setTemplates(response.data || [])
      setShowActionMenu(null)
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Failed to duplicate template:', error)
      setError('Failed to duplicate template. Please try again.')
    }
  }

  // Filter templates based on search and filters
  const filteredTemplates = templates.filter(template => {
    // Search filter
    const matchesSearch = searchTerm
      ? template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.description?.toLowerCase().includes(searchTerm.toLowerCase())
      : true
    
    // Type filter
    const matchesType = typeFilter === 'all' ? true : template.type === typeFilter
    
    // Status filter
    const matchesStatus = statusFilter === 'all' ? true : 
      (statusFilter === 'active' ? template.is_active : !template.is_active)
    
    return matchesSearch && matchesType && matchesStatus
  })

  // Template Card Component
  const TemplateCard = ({ template }: { template: Template }) => (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow duration-200">
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{template.name}</h3>
              {!template.is_active && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                  Inactive
                </span>
              )}
            </div>
            {template.description && (
              <p className="text-sm text-gray-600 mb-3">{template.description}</p>
            )}
            <p className="text-sm font-medium text-gray-800 mb-2">Subject: {template.subject}</p>
          </div>
          <div className="relative">
            <button
              onClick={() => setShowActionMenu(showActionMenu === template.template_id ? null : template.template_id)}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <MoreVertical className="h-5 w-5 text-gray-500" />
            </button>
            
            {showActionMenu === template.template_id && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button
                    onClick={() => setShowPreviewModal(template)}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </button>
                  <button
                    onClick={() => navigate(`/campaigns/templates/${template.template_id}`)}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </button>
                  <button
                    onClick={() => handleDuplicateTemplate(template)}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </button>
                  <button
                    onClick={() => setShowDeleteModal(template)}
                    className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2 mb-4">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeConfig[template.type].color}`}>
            {typeConfig[template.type].emoji} {typeConfig[template.type].label}
          </span>
          {template.variables.length > 0 && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {template.variables.length} variables
            </span>
          )}
        </div>
        
        <div className="text-sm text-gray-500 space-y-1">
          <div>Created: {new Date(template.created_at).toLocaleDateString()}</div>
          {template.variables.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {template.variables.slice(0, 3).map((variable) => (
                <span key={variable} className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-600">
                  {variable}
                </span>
              ))}
              {template.variables.length > 3 && (
                <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-600">
                  +{template.variables.length - 3} more
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )

  // Template List Row Component
  const TemplateRow = ({ template }: { template: Template }) => (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        <div>
          <div className="flex items-center space-x-2">
            <div className="text-sm font-medium text-gray-900">{template.name}</div>
            {!template.is_active && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                Inactive
              </span>
            )}
          </div>
          {template.description && (
            <div className="text-sm text-gray-500">{template.description}</div>
          )}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeConfig[template.type].color}`}>
          {typeConfig[template.type].emoji} {typeConfig[template.type].label}
        </span>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-gray-900 max-w-xs truncate">{template.subject}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {template.variables.length}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {new Date(template.created_at).toLocaleDateString()}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="relative">
          <button
            onClick={() => setShowActionMenu(showActionMenu === template.template_id ? null : template.template_id)}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <MoreVertical className="h-5 w-5 text-gray-500" />
          </button>
          
          {showActionMenu === template.template_id && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <div className="py-1">
                <button
                  onClick={() => setShowPreviewModal(template)}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </button>
                <button
                  onClick={() => navigate(`/campaigns/templates/${template.template_id}`)}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </button>
                <button
                  onClick={() => handleDuplicateTemplate(template)}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </button>
                <button
                  onClick={() => setShowDeleteModal(template)}
                  className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </button>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="px-6 py-8 sm:p-8">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-3">
                    📧 Email Templates
                  </h1>
                  <p className="text-xl text-gray-600 leading-relaxed">
                    Create and manage reusable email templates
                  </p>
                </div>
                
                <div className="mt-6 md:mt-0">
                  <button
                    onClick={() => navigate('/campaigns/templates/create')}
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Create Template
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-white shadow-lg rounded-xl border border-gray-100">
            <div className="px-6 py-6">
              <div className="flex flex-col lg:flex-row gap-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="text"
                      placeholder="🔍 Search templates..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-12 pr-4 py-3 w-full text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4">
                  <select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    className="px-4 py-3 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                  >
                    <option value="all">📋 All Types</option>
                    <option value="Welcome">👋 Welcome</option>
                    <option value="FollowUp">📞 Follow Up</option>
                    <option value="Reminder">⏰ Reminder</option>
                    <option value="Notification">🔔 Notification</option>
                    <option value="Marketing">📢 Marketing</option>
                    <option value="Transactional">💳 Transactional</option>
                    <option value="Custom">⚙️ Custom</option>
                  </select>
                  
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-4 py-3 text-lg border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                  >
                    <option value="all">📊 All Status</option>
                    <option value="active">✅ Active</option>
                    <option value="inactive">⏸️ Inactive</option>
                  </select>
                  
                  {/* View Mode Toggle */}
                  <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-2">
                    <button
                      onClick={() => setViewMode('card')}
                      className={`p-3 rounded-lg transition-all duration-200 ${
                        viewMode === 'card'
                          ? 'bg-white text-blue-600 shadow-md transform scale-105'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                      title="Card View"
                    >
                      <Grid3X3 className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-3 rounded-lg transition-all duration-200 ${
                        viewMode === 'list'
                          ? 'bg-white text-blue-600 shadow-md transform scale-105'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      }`}
                      title="List View"
                    >
                      <List className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="flex justify-center items-center py-16">
                <div className="text-center">
                  <RefreshCw className="animate-spin h-12 w-12 text-blue-500 mx-auto mb-4" />
                  <p className="text-xl text-gray-600 font-medium">Loading templates...</p>
                </div>
              </div>
            </div>
          )}
          
          {/* Error State */}
          {error && (
            <div className="bg-red-50 border-2 border-red-200 rounded-xl p-6">
              <div className="flex items-start space-x-3">
                <div className="text-red-500 text-2xl">⚠️</div>
                <div>
                  <h3 className="text-lg font-semibold text-red-800 mb-1">Something went wrong</h3>
                  <p className="text-red-700 text-base leading-relaxed">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !error && filteredTemplates.length === 0 && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              <div className="text-center py-16 px-8">
                <div className="mb-6">
                  <FileText className="h-20 w-20 text-blue-300 mx-auto mb-4" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  📧 No templates found
                </h3>
                <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
                  {searchTerm || typeFilter !== 'all' || statusFilter !== 'all'
                    ? 'Try adjusting your filters or search term to find what you\'re looking for'
                    : 'Create your first email template to get started with campaigns'}
                </p>
                <button
                  onClick={() => navigate('/campaigns/templates/create')}
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-xl rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
                >
                  <Plus className="h-6 w-6 mr-3" />
                  Create Template
                </button>
              </div>
            </div>
          )}

          {/* Templates Content */}
          {!isLoading && !error && filteredTemplates.length > 0 && (
            <div className="bg-white shadow-lg rounded-xl border border-gray-100">
              {viewMode === 'card' ? (
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredTemplates.map((template) => (
                      <TemplateCard key={template.template_id} template={template} />
                    ))}
                  </div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Template
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Subject
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Variables
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Created
                        </th>
                        <th className="relative px-6 py-3">
                          <span className="sr-only">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredTemplates.map((template) => (
                        <TemplateRow key={template.template_id} template={template} />
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      Page {page} of {totalPages}
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setPage(Math.max(1, page - 1))}
                        disabled={page === 1}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>
                      <button
                        onClick={() => setPage(Math.min(totalPages, page + 1))}
                        disabled={page === totalPages}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <Trash2 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete Template</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete "{showDeleteModal.name}"? This action cannot be undone.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowDeleteModal(null)}
                    className="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => handleDeleteTemplate(showDeleteModal)}
                    className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreviewModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Template Preview</h3>
                <button
                  onClick={() => setShowPreviewModal(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Subject</label>
                  <div className="mt-1 p-3 bg-gray-50 rounded-md">
                    {showPreviewModal.subject}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">HTML Content</label>
                  <div className="mt-1 p-3 bg-gray-50 rounded-md max-h-96 overflow-y-auto">
                    <div dangerouslySetInnerHTML={{ __html: showPreviewModal.html_content }} />
                  </div>
                </div>
                {showPreviewModal.variables.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Variables</label>
                    <div className="mt-1 flex flex-wrap gap-2">
                      {showPreviewModal.variables.map((variable) => (
                        <span key={variable} className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-600">
                          {variable}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setShowPreviewModal(null)}
                  className="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close action menu */}
      {showActionMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowActionMenu(null)}
        />
      )}
    </div>
  )
}

export default TemplateList