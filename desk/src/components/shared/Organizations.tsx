import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { apiClient } from '@/services/api'
import {
  Search,
  Building,
  Plus,
  RefreshCw,
  Grid3X3,
  List,
  X
} from 'lucide-react'
import { OrganizationDetail } from './OrganizationDetail'
import { OrganizationsListView } from './OrganizationsListView'
import { OrganizationsCardView } from './OrganizationsCardView'

// Define types
interface Organization {
  id: string
  name: string
  type: 'hospital' | 'clinic' | 'private_practice' | 'insurance' | 'pharmacy' | 'other'
  address?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  notes?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
}

// Organization type configuration for consistent UI
const typeConfig = {
  hospital: { label: 'Hospital', color: 'bg-blue-100 text-blue-800' },
  clinic: { label: 'Clinic', color: 'bg-green-100 text-green-800' },
  private_practice: { label: 'Private Practice', color: 'bg-purple-100 text-purple-800' },
  insurance: { label: 'Insurance', color: 'bg-yellow-100 text-yellow-800' },
  pharmacy: { label: 'Pharmacy', color: 'bg-orange-100 text-orange-800' },
  other: { label: 'Other', color: 'bg-gray-100 text-gray-800' }
}

export function Organizations() {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // View mode state (card, list)
  const [viewMode, setViewMode] = useState<'card' | 'list'>(
    user?.role === 'admin' ? 'list' : 'card'
  )
  
  // Organization detail modal state
  const [showOrganizationDetailModal, setShowOrganizationDetailModal] = useState(false)
  const [selectedOrganizationForDetail, setSelectedOrganizationForDetail] = useState<string | null>(null)

  // Load organizations from API
  useEffect(() => {
    const loadOrganizations = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        // Build query parameters
        const params = new URLSearchParams()
        if (searchTerm) params.append('search', searchTerm)
        if (typeFilter !== 'all') params.append('type', typeFilter)
        
        // @ts-ignore - apiClient.request is private but used throughout the codebase
        const response = await (apiClient as any).request(`/crm/organizations?${params.toString()}`) as any
        
        setOrganizations(response.organizations || [])
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Failed to load organizations:', error)
        setError('Failed to load organizations. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    loadOrganizations()
  }, [typeFilter, searchTerm])

  // Handle organization detail view
  const handleOrganizationClick = (organizationId: string) => {
    setSelectedOrganizationForDetail(organizationId)
    setShowOrganizationDetailModal(true)
  }

  // Filter organizations based on search and type filter
  const filteredOrganizations = organizations.filter(org => {
    // Type filter
    if (typeFilter !== 'all' && org.type !== typeFilter) {
      return false
    }
    
    // Search filter (case insensitive)
    if (searchTerm && !org.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false
    }
    
    return true
  })

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4 md:mb-0">Organizations</h1>
        
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full md:w-auto">
          {/* Search bar */}
          <div className="relative flex-grow max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search organizations..."
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {/* Type filter */}
          <div className="relative inline-block">
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="all">All Types</option>
              <option value="hospital">Hospital</option>
              <option value="clinic">Clinic</option>
              <option value="private_practice">Private Practice</option>
              <option value="insurance">Insurance</option>
              <option value="pharmacy">Pharmacy</option>
              <option value="other">Other</option>
            </select>
          </div>
          
          {/* View mode toggle */}
          <div className="flex space-x-1 border border-gray-300 rounded-md">
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
              title="List view"
            >
              <List className="h-5 w-5 text-gray-600" />
            </button>
            <button
              onClick={() => setViewMode('card')}
              className={`p-2 ${viewMode === 'card' ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
              title="Card view"
            >
              <Grid3X3 className="h-5 w-5 text-gray-600" />
            </button>
          </div>
          
          {/* Refresh button */}
          <button
            onClick={() => {
              setIsLoading(true)
              setTypeFilter('all')
              setSearchTerm('')
            }}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          
          {/* Create new organization button (admin and doctor only) */}
          {(user?.role === 'admin' || user?.role === 'doctor') && (
            <button
              onClick={() => navigate('/crm/organizations/new')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Organization
            </button>
          )}
        </div>
      </div>
      
      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
        </div>
      )}
      
      {/* Error state */}
      {!isLoading && error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}
      
      {/* Empty state */}
      {!isLoading && !error && filteredOrganizations.length === 0 && (
        <div className="text-center py-12 bg-white rounded-lg shadow">
          <Building className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">No organizations found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || typeFilter !== 'all'
              ? 'Try adjusting your filters or search term'
              : 'Create your first organization to get started'}
          </p>
          {(user?.role === 'admin' || user?.role === 'doctor') && (
            <div className="mt-6">
              <button
                onClick={() => navigate('/crm/organizations/new')}
                className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Organization
              </button>
            </div>
          )}
        </div>
      )}
      
      {/* Organizations list */}
      {!isLoading && !error && filteredOrganizations.length > 0 && (
        <>
          {viewMode === 'list' && (
            <OrganizationsListView
              organizations={filteredOrganizations}
              typeConfig={typeConfig}
              onOrganizationClick={handleOrganizationClick}
              userRole={user?.role || ''}
            />
          )}
          
          {viewMode === 'card' && (
            <OrganizationsCardView
              organizations={filteredOrganizations}
              typeConfig={typeConfig}
              onOrganizationClick={handleOrganizationClick}
              userRole={user?.role || ''}
            />
          )}
        </>
      )}
      
      {/* Organization Detail Modal */}
      {showOrganizationDetailModal && selectedOrganizationForDetail && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-0 mx-auto p-0 w-full max-w-7xl h-full">
            <div className="bg-white rounded-lg shadow-xl h-full overflow-hidden flex flex-col">
              <div className="p-4 border-b flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Organization Details</h3>
                <button
                  onClick={() => setShowOrganizationDetailModal(false)}
                  className="inline-flex items-center justify-center p-1 rounded-full hover:bg-gray-200"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>
              <div className="flex-grow overflow-auto">
                <OrganizationDetail 
                  organizationId={selectedOrganizationForDetail} 
                  onClose={() => setShowOrganizationDetailModal(false)}
                  showBackButton={false}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
