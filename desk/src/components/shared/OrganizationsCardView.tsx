
import {
  Building,
  Globe,
  MapPin,
  Phone,
  Mail,
  Calendar,
  ChevronRight
} from 'lucide-react'

// Define types
interface Organization {
  id: string
  name: string
  type: 'hospital' | 'clinic' | 'private_practice' | 'insurance' | 'pharmacy' | 'other'
  address?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  notes?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
}

interface TypeConfig {
  [key: string]: {
    label: string
    color: string
  }
}

interface OrganizationsCardViewProps {
  organizations: Organization[]
  typeConfig: TypeConfig
  onOrganizationClick: (organizationId: string) => void
  userRole: string
}

export function OrganizationsCardView({
  organizations,
  typeConfig,
  onOrganizationClick
}: OrganizationsCardViewProps) {
  // Format date to readable format
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  // Format address for display
  const formatAddress = (org: Organization) => {
    const parts = []
    if (org.address) parts.push(org.address)
    if (org.city) parts.push(org.city)
    if (org.state) parts.push(org.state)
    if (org.zipCode) parts.push(org.zipCode)
    if (org.country) parts.push(org.country)
    
    return parts.join(', ') || 'No address'
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {organizations.map((org) => (
        <div
          key={org.id}
          className="bg-white rounded-lg shadow overflow-hidden hover:shadow-md cursor-pointer transition-shadow duration-200"
          onClick={() => onOrganizationClick(org.id)}
        >
          <div className="p-5">
            <div className="flex justify-between items-start">
              <div className="flex items-center">
                <Building className="h-5 w-5 text-gray-500" />
                <h3 className="ml-2 text-lg font-medium text-gray-900 truncate">{org.name}</h3>
              </div>
              <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${typeConfig[org.type]?.color}`}>
                {typeConfig[org.type]?.label}
              </span>
            </div>
            
            <div className="mt-4 space-y-3">
              {/* Location */}
              <div className="flex items-start">
                <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                <p className="ml-2 text-sm text-gray-600 break-words">
                  {formatAddress(org)}
                </p>
              </div>
              
              {/* Contact info */}
              {org.phone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <p className="ml-2 text-sm text-gray-600">{org.phone}</p>
                </div>
              )}
              
              {org.email && (
                <div className="flex items-center">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <a 
                    href={`mailto:${org.email}`}
                    onClick={(e) => e.stopPropagation()}
                    className="ml-2 text-sm text-gray-600 hover:text-indigo-600 truncate"
                  >
                    {org.email}
                  </a>
                </div>
              )}
              
              {org.website && (
                <div className="flex items-center">
                  <Globe className="h-4 w-4 text-gray-400" />
                  <a 
                    href={org.website.startsWith('http') ? org.website : `https://${org.website}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={(e) => e.stopPropagation()}
                    className="ml-2 text-sm text-gray-600 hover:text-indigo-600 truncate"
                  >
                    {org.website.replace(/^https?:\/\//i, '')}
                  </a>
                </div>
              )}
              
              {/* Created date */}
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-gray-400" />
                <p className="ml-2 text-sm text-gray-500">
                  Created {formatDate(org.createdAt)}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 px-5 py-3 flex justify-end">
            <button className="text-sm text-indigo-600 hover:text-indigo-900 font-medium flex items-center">
              View details
              <ChevronRight className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>
      ))}
    </div>
  )
}
