
import {
  Building,
  Globe,
  MapPin,
  Phone,
  Mail,
  Calendar,
  ChevronRight
} from 'lucide-react'

// Define types
interface Organization {
  id: string
  name: string
  type: 'hospital' | 'clinic' | 'private_practice' | 'insurance' | 'pharmacy' | 'other'
  address?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  notes?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
}

interface TypeConfig {
  [key: string]: {
    label: string
    color: string
  }
}

interface OrganizationsListViewProps {
  organizations: Organization[]
  typeConfig: TypeConfig
  onOrganizationClick: (organizationId: string) => void
  userRole: string
}

export function OrganizationsListView({
  organizations,
  typeConfig,
  onOrganizationClick
}: OrganizationsListViewProps) {
  // Format date to readable format
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  // Format address for display
  const formatAddress = (org: Organization) => {
    const parts = []
    if (org.city) parts.push(org.city)
    if (org.state) parts.push(org.state)
    if (org.country) parts.push(org.country)
    
    return parts.join(', ') || 'No address'
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Organization
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Type
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Location
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Contact
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th scope="col" className="relative px-6 py-3">
              <span className="sr-only">View</span>
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {organizations.map((org) => (
            <tr 
              key={org.id}
              onClick={() => onOrganizationClick(org.id)}
              className="hover:bg-gray-50 cursor-pointer"
            >
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Building className="h-5 w-5 text-gray-500" />
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900">{org.name}</div>
                    {org.website && (
                      <div className="text-xs text-gray-500 flex items-center">
                        <Globe className="h-3 w-3 mr-1" />
                        <a 
                          href={org.website.startsWith('http') ? org.website : `https://${org.website}`} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          onClick={(e) => e.stopPropagation()}
                          className="hover:text-indigo-600"
                        >
                          {org.website.replace(/^https?:\/\//i, '')}
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${typeConfig[org.type]?.color}`}>
                  {typeConfig[org.type]?.label}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center text-sm text-gray-500">
                  <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                  {formatAddress(org)}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-500">
                  {org.phone && (
                    <div className="flex items-center mb-1">
                      <Phone className="h-3 w-3 mr-1" />
                      {org.phone}
                    </div>
                  )}
                  {org.email && (
                    <div className="flex items-center">
                      <Mail className="h-3 w-3 mr-1" />
                      <a 
                        href={`mailto:${org.email}`} 
                        onClick={(e) => e.stopPropagation()}
                        className="hover:text-indigo-600"
                      >
                        {org.email}
                      </a>
                    </div>
                  )}
                  {!org.phone && !org.email && (
                    <span className="text-gray-400">No contact info</span>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                  {formatDate(org.createdAt)}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button className="text-indigo-600 hover:text-indigo-900 flex items-center">
                  View
                  <ChevronRight className="h-4 w-4 ml-1" />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
