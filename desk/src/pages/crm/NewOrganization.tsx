import { useAuth } from '@/hooks/useAuth'
import { OrganizationForm } from '@/components/shared/OrganizationForm'
import { Navigate } from 'react-router-dom'

export function NewOrganization() {
  const { user } = useAuth()
  
  // Only admin and doctor roles can create organizations
  if (user?.role !== 'admin' && user?.role !== 'doctor') {
    return <Navigate to="/crm/organizations" replace />
  }
  
  // Handle successful organization creation
  const handleSuccess = (organizationId: string) => {
    console.log('Organization created successfully, redirecting to details:', organizationId)
    window.location.href = `/crm/organizations/${organizationId}`
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Create New Organization</h1>
        <p className="text-gray-600 mt-1">
          Add a new organization to your CRM database.
        </p>
      </div>
      
      <div className="bg-white rounded-lg shadow">
        <OrganizationForm onSuccess={handleSuccess} />
      </div>
    </div>
  )
}
