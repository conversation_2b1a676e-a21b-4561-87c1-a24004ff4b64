import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { apiClient } from '@/services/api';
// TODO: Replace with actual notification system when available
const useNotification = () => ({ showNotification: () => {} });
import { 
  ArrowLeft,
  Building,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  ExternalLink,
  FileText,
  Filter,
  Grid3X3,
  Info,
  List,
  Mail,
  MoreHorizontal,
  Phone,
  Plus,
  Save,
  Tag,
  Trash2,
  User,
  UserCheck,
  X,
  Kanban
} from 'lucide-react';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';

// Types
interface Contact {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  position?: string;
  organizationId?: string;
  organization?: {
    id: string;
    name: string;
  };
  status: 'lead' | 'prospect' | 'customer' | 'inactive';
  source?: string;
  assignedToId?: string;
  assignedTo?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  tags?: string[];
  notes?: string;
  lastContactedAt?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface Touchpoint {
  id: string;
  contactId: string;
  organizationId?: string;
  type: 'email' | 'call' | 'meeting' | 'referral' | 'social' | 'other';
  title: string;
  description?: string;
  date: string;
  outcome?: string;
  followUpRequired: boolean;
  followUpDate?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface Organization {
  id: string;
  name: string;
  type: string;
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

// Status configuration
const statusConfig = {
  lead: { label: 'Lead', color: 'bg-blue-100 text-blue-800' },
  prospect: { label: 'Prospect', color: 'bg-purple-100 text-purple-800' },
  customer: { label: 'Customer', color: 'bg-green-100 text-green-800' },
  inactive: { label: 'Inactive', color: 'bg-gray-100 text-gray-800' }
};

// Touchpoint type configuration
const touchpointTypeConfig = {
  email: { label: 'Email', color: 'bg-blue-100 text-blue-800', icon: Mail },
  call: { label: 'Call', color: 'bg-green-100 text-green-800', icon: Phone },
  meeting: { label: 'Meeting', color: 'bg-purple-100 text-purple-800', icon: Calendar },
  referral: { label: 'Referral', color: 'bg-yellow-100 text-yellow-800', icon: UserCheck },
  social: { label: 'Social', color: 'bg-pink-100 text-pink-800', icon: ExternalLink },
  other: { label: 'Other', color: 'bg-gray-100 text-gray-800', icon: FileText }
};

const ContactDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  // State
  const [contact, setContact] = useState<Contact | null>(null);
  const [touchpoints, setTouchpoints] = useState<Touchpoint[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('details');
  const [viewMode, setViewMode] = useState<'kanban' | 'card' | 'list'>('card');
  
  // Edit states
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editedContact, setEditedContact] = useState<Partial<Contact>>({});
  const [isSaving, setIsSaving] = useState<boolean>(false);
  
  // Delete confirmation
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);
  
  // New touchpoint
  const [showNewTouchpoint, setShowNewTouchpoint] = useState<boolean>(false);
  const [newTouchpoint, setNewTouchpoint] = useState<Partial<Touchpoint>>({
    type: 'meeting',
    date: new Date().toISOString().split('T')[0],
    followUpRequired: false
  });
  
  // Fetch contact data
  useEffect(() => {
    const fetchContactData = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        
        // Fetch contact details
        const contactResponse = await apiClient.getCrmContact(id);
        const contactData = contactResponse.contact || contactResponse.data?.contact;
        setContact(contactData);
        setEditedContact(contactData);
        
        // Fetch touchpoints
        const touchpointsResponse = await apiClient.getCrmTouchpoints({ contactId: id });
        const touchpointsData = touchpointsResponse.touchpoints || touchpointsResponse.data?.touchpoints || [];
        setTouchpoints(touchpointsData);
        
        // Fetch organizations for dropdown
        const organizationsResponse = await apiClient.getCrmOrganizations({ limit: 100 });
        const organizationsData = organizationsResponse.organizations || organizationsResponse.data?.organizations || [];
        setOrganizations(organizationsData);
        
        // Fetch users for assignment dropdown
        const usersResponse = await apiClient.getUsers({ role: 'doctor', limit: 100 });
        const usersData = usersResponse.data || usersResponse || [];
        setUsers(usersData);
        
      } catch (error) {
        // TODO: Replace with proper error reporting
  console.error('Error fetching contact data:', error);
        showNotification({
          title: 'Error',
          message: 'Failed to load contact details',
          type: 'error'
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchContactData();
  }, [id]);
  
  // Format date helper
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Format time helper
  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Handle save contact
  const handleSaveContact = async () => {
    if (!contact || !editedContact) return;
    
    try {
      setIsSaving(true);
      
      const response = await apiClient.updateCrmContact(id!, editedContact);
      
      const updatedContact = response.contact || response.data?.contact;
      setContact(updatedContact);
      setIsEditing(false);
      
      showNotification({
        title: 'Success',
        message: 'Contact updated successfully',
        type: 'success'
      });
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error updating contact:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to update contact',
        type: 'error'
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  // Handle delete contact
  const handleDeleteContact = async () => {
    if (!id) return;
    
    try {
      setIsSaving(true);
      
      await apiClient.deleteCrmContact(id!);
      
      showNotification({
        title: 'Success',
        message: 'Contact deleted successfully',
        type: 'success'
      });
      
      // Navigate back to contacts list
      navigate('/crm/contacts');
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error deleting contact:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to delete contact',
        type: 'error'
      });
      setIsSaving(false);
    }
  };
  
  // Handle create touchpoint
  const handleCreateTouchpoint = async () => {
    if (!id || !newTouchpoint.title || !newTouchpoint.type) return;
    
    try {
      const touchpointData = {
        ...newTouchpoint,
        contactId: id
      };
      
      const response = await apiClient.createCrmTouchpoint(touchpointData);
      
      // Add new touchpoint to list
      const newTouchpointData = response.touchpoint || response.data?.touchpoint;
      setTouchpoints(prev => [newTouchpointData, ...prev]);
      
      // Reset form and close modal
      setNewTouchpoint({
        type: 'meeting',
        date: new Date().toISOString().split('T')[0],
        followUpRequired: false
      });
      setShowNewTouchpoint(false);
      
      showNotification({
        title: 'Success',
        message: 'Touchpoint created successfully',
        type: 'success'
      });
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error creating touchpoint:', error);
      showNotification({
        title: 'Error',
        message: 'Failed to create touchpoint',
        type: 'error'
      });
    }
  };
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditedContact(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle touchpoint input change
  const handleTouchpointChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setNewTouchpoint(prev => ({ ...prev, [name]: checked }));
    } else {
      setNewTouchpoint(prev => ({ ...prev, [name]: value }));
    }
  };
  
  // Render status badge
  const renderStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig] || { 
      label: status, 
      color: 'bg-gray-100 text-gray-800' 
    };
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };
  
  // Render touchpoint type badge
  const renderTouchpointTypeBadge = (type: string) => {
    const config = touchpointTypeConfig[type as keyof typeof touchpointTypeConfig] || {
      label: type,
      color: 'bg-gray-100 text-gray-800',
      icon: FileText
    };
    
    const Icon = config.icon;
    
    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  // Component JSX would go here - placeholder for now
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <Button
          variant="outline"
          onClick={() => navigate('/crm/contacts')}
          className="flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Contacts
        </Button>
      </div>
      
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold mb-2">Contact Detail</h2>
        <p className="text-gray-600">Contact detail implementation in progress</p>
      </div>
    </div>
  );
};

export default ContactDetail;
