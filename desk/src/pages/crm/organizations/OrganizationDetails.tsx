import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { 
  Building, 
  ArrowLeft, 
  Edit, 
  Trash2, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Clock, 
  User, 
  ExternalLink,
  Loader2
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { apiClient } from '@/services/api';
// TODO: Replace with actual notification system when available
const useNotification = () => ({ showNotification: () => {} });

// UI Components
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';

// Types
interface Organization {
  id: string;
  name: string;
  type: 'hospital' | 'clinic' | 'private_practice' | 'insurance' | 'pharmacy' | 'other';
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  createdByUser?: {
    firstName: string;
    lastName: string;
  };
}

interface Contact {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  title?: string;
  organizationId: string;
}

const OrganizationDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  
  // State
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [contactsLoading, setContactsLoading] = useState<boolean>(true);
  
  // Fetch organization details
  const fetchOrganizationDetails = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getCrmOrganization(id!);
      // Handle different response formats
      if (response.organization) {
        setOrganization(response.organization);
      } else if (response.data?.organization) {
        setOrganization(response.data.organization);
      }
    } catch (error) {
      showNotification({
        title: 'Error',
        message: 'Failed to fetch organization details',
        type: 'error',
      });
      // TODO: Replace with proper error reporting
  console.error('Error fetching organization details:', error);
      navigate('/crm/organizations');
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch related contacts
  const fetchRelatedContacts = async () => {
    try {
      setContactsLoading(true);
      const response = await apiClient.getCrmContacts({ organizationId: id });
      // Handle different response formats
      if (response.contacts) {
        setContacts(response.contacts);
      } else if (response.data?.contacts) {
        setContacts(response.data.contacts);
      }
    } catch (error) {
      // TODO: Replace with proper error reporting
  console.error('Error fetching related contacts:', error);
    } finally {
      setContactsLoading(false);
    }
  };
  
  // Initial fetch
  useEffect(() => {
    if (id) {
      fetchOrganizationDetails();
      fetchRelatedContacts();
    }
  }, [id]);
  
  // Handle edit
  const handleEdit = () => {
    navigate(`/crm/organizations/${id}/edit`);
  };
  
  // Handle delete
  const handleDelete = async () => {
    if (!organization) return;
    
    if (window.confirm(`Are you sure you want to delete ${organization.name}?`)) {
      try {
        await apiClient.deleteCrmOrganization(id!);
        showNotification({
          title: 'Success',
          message: 'Organization deleted successfully',
          type: 'success',
        });
        navigate('/crm/organizations');
      } catch (error) {
        showNotification({
          title: 'Error',
          message: 'Failed to delete organization',
          type: 'error',
        });
        // TODO: Replace with proper error reporting
  console.error('Error deleting organization:', error);
      }
    }
  };
  
  // Handle view contact
  const handleViewContact = (contactId: string) => {
    navigate(`/crm/contacts/${contactId}`);
  };
  
  // Handle add contact
  const handleAddContact = () => {
    navigate('/crm/contacts/new', { state: { organizationId: id } });
  };
  
  // Render organization type badge
  const renderTypeBadge = (type: string) => {
    const typeColors: Record<string, string> = {
      hospital: 'bg-blue-100 text-blue-800',
      clinic: 'bg-green-100 text-green-800',
      private_practice: 'bg-purple-100 text-purple-800',
      insurance: 'bg-yellow-100 text-yellow-800',
      pharmacy: 'bg-pink-100 text-pink-800',
      other: 'bg-gray-100 text-gray-800',
    };
    
    const typeLabels: Record<string, string> = {
      hospital: 'Hospital',
      clinic: 'Clinic',
      private_practice: 'Private Practice',
      insurance: 'Insurance',
      pharmacy: 'Pharmacy',
      other: 'Other',
    };
    
    return (
      <Badge className={typeColors[type] || 'bg-gray-100 text-gray-800'}>
        {typeLabels[type] || 'Unknown'}
      </Badge>
    );
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };
  
  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={() => navigate('/crm/organizations')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organizations
          </Button>
        </div>
        
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-8 w-64" />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  
  if (!organization) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <Building className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900">Organization not found</h3>
          <p className="text-gray-500 mt-1">
            The organization you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Button onClick={() => navigate('/crm/organizations')} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organizations
          </Button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" onClick={() => navigate('/crm/organizations')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Organizations
        </Button>
      </div>
      
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center space-x-3">
          <Building className="h-8 w-8 text-primary-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{organization.name}</h1>
            <div className="flex items-center mt-1">
              {renderTypeBadge(organization.type)}
              {organization.isActive ? (
                <Badge variant="outline" className="ml-2 bg-green-50 text-green-700 border-green-200">
                  Active
                </Badge>
              ) : (
                <Badge variant="outline" className="ml-2 bg-gray-50 text-gray-700 border-gray-200">
                  Inactive
                </Badge>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Tabs defaultValue="details">
            <TabsList>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="contacts">Contacts</TabsTrigger>
            </TabsList>
            
            <TabsContent value="details" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Organization Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Address */}
                  {(organization.address || organization.city || organization.state || organization.zipCode) && (
                    <div className="flex">
                      <MapPin className="h-5 w-5 text-gray-400 mr-3 flex-shrink-0" />
                      <div>
                        {organization.address && <p>{organization.address}</p>}
                        <p>
                          {[organization.city, organization.state, organization.zipCode]
                            .filter(Boolean)
                            .join(', ')}
                        </p>
                        {organization.country && <p>{organization.country}</p>}
                      </div>
                    </div>
                  )}
                  
                  {/* Phone */}
                  {organization.phone && (
                    <div className="flex">
                      <Phone className="h-5 w-5 text-gray-400 mr-3 flex-shrink-0" />
                      <p>{organization.phone}</p>
                    </div>
                  )}
                  
                  {/* Email */}
                  {organization.email && (
                    <div className="flex">
                      <Mail className="h-5 w-5 text-gray-400 mr-3 flex-shrink-0" />
                      <a href={`mailto:${organization.email}`} className="hover:underline">
                        {organization.email}
                      </a>
                    </div>
                  )}
                  
                  {/* Website */}
                  {organization.website && (
                    <div className="flex">
                      <Globe className="h-5 w-5 text-gray-400 mr-3 flex-shrink-0" />
                      <a 
                        href={organization.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="hover:underline flex items-center"
                      >
                        {organization.website}
                        <ExternalLink className="h-3 w-3 ml-1 inline" />
                      </a>
                    </div>
                  )}
                </CardContent>
              </Card>
              
              {/* Notes */}
              {organization.notes && (
                <Card>
                  <CardHeader>
                    <CardTitle>Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="whitespace-pre-wrap">{organization.notes}</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
            
            <TabsContent value="contacts" className="space-y-6 mt-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Contacts</h3>
                <Button onClick={handleAddContact}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Contact
                </Button>
              </div>
              
              {contactsLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <Card key={index}>
                      <CardContent className="py-4">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-3">
                            <Skeleton className="h-10 w-10 rounded-full" />
                            <div>
                              <Skeleton className="h-5 w-32" />
                              <Skeleton className="h-4 w-24 mt-1" />
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : contacts.length === 0 ? (
                <Card>
                  <CardContent className="py-12 text-center">
                    <User className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900">No contacts found</h3>
                    <p className="text-gray-500 mt-1">
                      Add contacts to this organization to get started
                    </p>
                    <Button onClick={handleAddContact} className="mt-4">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Contact
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {contacts.map((contact) => (
                    <Card 
                      key={contact.id}
                      className="hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => handleViewContact(contact.id)}
                    >
                      <CardContent className="py-4">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-3">
                            <div className="h-10 w-10 rounded-full bg-primary-100 text-primary-700 flex items-center justify-center">
                              <span className="font-medium">
                                {contact.firstName.charAt(0)}
                                {contact.lastName.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <h4 className="font-medium">
                                {contact.firstName} {contact.lastName}
                              </h4>
                              {contact.title && (
                                <p className="text-sm text-gray-500">{contact.title}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex space-x-4">
                            {contact.email && (
                              <a 
                                href={`mailto:${contact.email}`}
                                onClick={(e) => e.stopPropagation()}
                                className="text-gray-500 hover:text-primary-600"
                              >
                                <Mail className="h-5 w-5" />
                              </a>
                            )}
                            {contact.phone && (
                              <a 
                                href={`tel:${contact.phone}`}
                                onClick={(e) => e.stopPropagation()}
                                className="text-gray-500 hover:text-primary-600"
                              >
                                <Phone className="h-5 w-5" />
                              </a>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
        
        <div className="space-y-6">
          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Created</span>
                <span className="font-medium flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDate(organization.createdAt)}
                </span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Last Updated</span>
                <span className="font-medium flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDate(organization.updatedAt)}
                </span>
              </div>
              
              {organization.createdByUser && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Created By</span>
                  <span className="font-medium flex items-center">
                    <User className="h-3 w-3 mr-1" />
                    {organization.createdByUser.firstName} {organization.createdByUser.lastName}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start" onClick={handleAddContact}>
                <Plus className="h-4 w-4 mr-2" />
                Add Contact
              </Button>
              <Button variant="outline" className="w-full justify-start" onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Organization
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default OrganizationDetails;
