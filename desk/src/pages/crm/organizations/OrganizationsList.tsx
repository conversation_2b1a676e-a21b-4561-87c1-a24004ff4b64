import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Building, 
  Search, 
  Plus, 
  Filter, 
  ChevronLeft, 
  ChevronRight, 
  ExternalLink,
  Trash2,
  Edit
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { apiClient } from '@/services/api';
// TODO: Replace with actual notification system when available
const useNotification = () => ({
  showNotification: (notification: { title: string; message: string; type: string }) => {
    console.log(`${notification.type.toUpperCase()}: ${notification.title} - ${notification.message}`);
  }
});

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// Using native select for now - can be upgraded to Radix UI Select later
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

// Types
interface Organization {
  id: string;
  name: string;
  type: 'hospital' | 'clinic' | 'private_practice' | 'insurance' | 'pharmacy' | 'other';
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const OrganizationsList: React.FC = () => {
  const navigate = useNavigate();
  // Removed useAxios dependency - using apiClient directly
  const { showNotification } = useNotification();
  
  // State
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  
  // Filters
  const [search, setSearch] = useState<string>('');
  const [type, setType] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<string>('asc');
  
  // Fetch organizations
  const fetchOrganizations = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getCrmOrganizations({
        page: pagination.page,
        limit: pagination.limit,
        search: search || undefined,
        status: type || undefined,
      });
      
      // Handle different response formats
      if (response.organizations) {
        setOrganizations(response.organizations);
        setPagination(response.pagination || {
          page: pagination.page,
          limit: pagination.limit,
          total: response.organizations.length,
          totalPages: Math.ceil(response.organizations.length / pagination.limit),
        });
      } else if (response.data?.organizations) {
        setOrganizations(response.data.organizations);
        setPagination(response.data.pagination || {
          page: pagination.page,
          limit: pagination.limit,
          total: response.data.organizations.length,
          totalPages: Math.ceil(response.data.organizations.length / pagination.limit),
        });
      }
    } catch (error) {
      showNotification({
        title: 'Error',
        message: 'Failed to fetch organizations',
        type: 'error',
      });
      // TODO: Replace with proper error reporting
  console.error('Error fetching organizations:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Initial fetch
  useEffect(() => {
    fetchOrganizations();
  }, [pagination.page, pagination.limit, sortBy, sortOrder]);
  
  // Handle search
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchOrganizations();
  };
  
  // Handle filter change
  const handleFilterChange = (value: string) => {
    setType(value);
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchOrganizations();
  };
  
  // Handle sort change
  const handleSortChange = (value: string) => {
    const [newSortBy, newSortOrder] = value.split('-');
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
  };
  
  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };
  
  // Handle create new
  const handleCreateNew = () => {
    navigate('/crm/organizations/new');
  };
  
  // Handle view details
  const handleViewDetails = (id: string) => {
    navigate(`/crm/organizations/${id}`);
  };
  
  // Handle edit
  const handleEdit = (id: string) => {
    navigate(`/crm/organizations/${id}/edit`);
  };
  
  // Handle delete
  const handleDelete = async (id: string, name: string) => {
    if (window.confirm(`Are you sure you want to delete ${name}?`)) {
      try {
        await apiClient.deleteCrmOrganization(id);
        showNotification({
          title: 'Success',
          message: 'Organization deleted successfully',
          type: 'success',
        });
        fetchOrganizations();
      } catch (error) {
        showNotification({
          title: 'Error',
          message: 'Failed to delete organization',
          type: 'error',
        });
        // TODO: Replace with proper error reporting
  console.error('Error deleting organization:', error);
      }
    }
  };
  
  // Render organization type badge
  const renderTypeBadge = (type: string) => {
    const typeColors: Record<string, string> = {
      hospital: 'bg-blue-100 text-blue-800',
      clinic: 'bg-green-100 text-green-800',
      private_practice: 'bg-purple-100 text-purple-800',
      insurance: 'bg-yellow-100 text-yellow-800',
      pharmacy: 'bg-pink-100 text-pink-800',
      other: 'bg-gray-100 text-gray-800',
    };
    
    const typeLabels: Record<string, string> = {
      hospital: 'Hospital',
      clinic: 'Clinic',
      private_practice: 'Private Practice',
      insurance: 'Insurance',
      pharmacy: 'Pharmacy',
      other: 'Other',
    };
    
    return (
      <Badge className={typeColors[type] || 'bg-gray-100 text-gray-800'}>
        {typeLabels[type] || 'Unknown'}
      </Badge>
    );
  };
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Building className="h-6 w-6 text-primary-600" />
          <h1 className="text-2xl font-bold text-gray-900">Organizations</h1>
        </div>
        <Button onClick={handleCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          New Organization
        </Button>
      </div>
      
      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search organizations..."
                  className="pl-8"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            
            <div className="w-full md:w-48">
              <select
                value={type}
                onChange={(e) => handleFilterChange(e.target.value)}
                className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">All Types</option>
                <option value="hospital">Hospital</option>
                <option value="clinic">Clinic</option>
                <option value="private_practice">Private Practice</option>
                <option value="insurance">Insurance</option>
                <option value="pharmacy">Pharmacy</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div className="w-full md:w-48">
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => handleSortChange(e.target.value)}
                className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="name-asc">Name (A-Z)</option>
                <option value="name-desc">Name (Z-A)</option>
                <option value="createdAt-desc">Newest First</option>
                <option value="createdAt-asc">Oldest First</option>
              </select>
            </div>
            
            <Button variant="outline" onClick={handleSearch}>
              <Filter className="h-4 w-4 mr-2" />
              Apply Filters
            </Button>
          </div>
        </CardContent>
      </Card>
      
      {/* Organizations List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          // Loading skeletons
          Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <CardHeader className="pb-2">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : organizations.length === 0 ? (
          <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
            <Building className="h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900">No organizations found</h3>
            <p className="text-gray-500 mt-1">
              {search || type ? 'Try adjusting your filters' : 'Create your first organization to get started'}
            </p>
            {!search && !type && (
              <Button onClick={handleCreateNew} className="mt-4">
                <Plus className="h-4 w-4 mr-2" />
                New Organization
              </Button>
            )}
          </div>
        ) : (
          organizations.map((org) => (
            <Card key={org.id} className="overflow-hidden hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg font-bold">{org.name}</CardTitle>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <svg
                          className="h-4 w-4"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                        </svg>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleViewDetails(org.id)}>
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEdit(org.id)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => handleDelete(org.id, org.name)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex items-center mt-1">
                  {renderTypeBadge(org.type)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-1 text-sm">
                  {org.address && (
                    <p className="text-gray-600">
                      {[org.address, org.city, org.state, org.zipCode]
                        .filter(Boolean)
                        .join(', ')}
                    </p>
                  )}
                  {org.phone && <p className="text-gray-600">{org.phone}</p>}
                  {org.email && (
                    <p className="text-gray-600 truncate">
                      <a href={`mailto:${org.email}`} className="hover:underline">
                        {org.email}
                      </a>
                    </p>
                  )}
                  {org.website && (
                    <p className="text-gray-600 truncate">
                      <a 
                        href={org.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="hover:underline flex items-center"
                      >
                        {org.website.replace(/^https?:\/\//, '')}
                        <ExternalLink className="h-3 w-3 ml-1 inline" />
                      </a>
                    </p>
                  )}
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => handleViewDetails(org.id)}
                >
                  View Details
                </Button>
              </CardFooter>
            </Card>
          ))
        )}
      </div>
      
      {/* Pagination */}
      {!loading && organizations.length > 0 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-500">
            Showing {Math.min((pagination.page - 1) * pagination.limit + 1, pagination.total)} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} organizations
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous page</span>
            </Button>
            <div className="text-sm">
              Page {pagination.page} of {pagination.totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next page</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrganizationsList;
