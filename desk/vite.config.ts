import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      // Support both Docker and local development paths for shared folder
      '@/shared': process.env.NODE_ENV === 'production' || process.env.DOCKER_ENV
        ? path.resolve(__dirname, '../shared')
        : path.resolve(__dirname, '../shared'),
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/pages': path.resolve(__dirname, './src/pages'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/assets': path.resolve(__dirname, './src/assets'),
    },
  },
  server: {
    port: 3000,
    host: '0.0.0.0',
    cors: true,
    strictPort: false,

    hmr: false, // Disabled for production
    proxy: {
      '/api': {
        target: 'http://api:3001',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false, // ✅ disable in prod unless you need it
    minify: 'esbuild', // or 'terser' for heavier compression
    target: 'es2017', // 🎯 balance of compatibility + modern JS
    cssCodeSplit: true, // ✅ default, just make sure it’s on
    chunkSizeWarningLimit: 1000, // 📦 adjust if you're bundling large components
  },
})
