services:
  # Traefik Reverse Proxy
  continuia_rp:
    image: traefik:v3.0
    container_name: continuia-traefik
    restart: always
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/etc/traefik/traefik.yml:ro
      - ./traefik-dynamic.yml:/etc/traefik/traefik-dynamic.yml:ro
    networks:
      - continuia
      - ui
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # API Backend Service
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: continuia-api
    restart: always
    environment:
      - NODE_ENV=${NODE_ENV}
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      - CORS_ORIGIN=${CORS_ORIGIN}
      - MINIO_ROOT_USER=${MINIO_ROOT_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_PORT=${MINIO_PORT}
      - MINIO_USE_SSL=${MINIO_USE_SSL}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-continuia}
      - POSTGRES_HOST=${POSTGRES_HOST:-postgres}
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      # Email configuration for Mailpit
      - EMAIL_PROVIDER=smtp
      - SMTP_HOST=mailpit
      - SMTP_PORT=1025
      - SMTP_SECURE=false
      - SMTP_USER=
      - SMTP_PASS=
      - SMTP_FROM_NAME=Continuia Healthcare
      - SMTP_FROM_EMAIL=<EMAIL>
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`api.continuia.ai`) || Host(`api.continuia.health`)"
      - "traefik.http.routers.api.entrypoints=web"
      - "traefik.http.services.api.loadbalancer.server.port=3001"
    depends_on:
      postgres:
        condition: service_healthy
      mailpit:
        condition: service_healthy
    networks:
      - continuia

  # Patient-facing Care App
  care:
    build:
      context: .
      dockerfile: ./care/Dockerfile
    container_name: continuia-care
    restart: always
    environment:
      - NODE_ENV=${NODE_ENV}
      - VITE_API_URL=${VITE_API_URL}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.care.rule=Host(`care.continuia.ai`) || Host(`care.continuia.health`)"
      - "traefik.http.routers.care.entrypoints=web"
      - "traefik.http.services.care.loadbalancer.server.port=3000"
    depends_on:
      - api
    networks:
      - continuia

  # Professional Desk App (Doctor/Admin/Agent)
  desk:
    build:
      context: .
      dockerfile: ./desk/Dockerfile
    container_name: continuia-desk
    restart: always
    environment:
      - NODE_ENV=${NODE_ENV}
      - VITE_API_URL=${VITE_API_URL}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.desk.rule=Host(`desk.continuia.ai`) || Host(`desk.continuia.health`)"
      - "traefik.http.routers.desk.entrypoints=web"
      - "traefik.http.services.desk.loadbalancer.server.port=3000"
    depends_on:
      - api
    networks:
      - continuia

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: continuia-postgres
    restart: always
    environment:
      # Parse DATABASE_URL to extract individual components
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-continuia}
      PGDATA: /var/lib/postgresql/data/pgdata
    networks:
      - continuia
    volumes:
      - ./.data/postgres-data:/var/lib/postgresql/data/pgdata
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d continuia"]
      interval: 10s
      timeout: 5s
      retries: 5
      
  # Mailpit Email Testing Service
  mailpit:
    image: axllent/mailpit:latest
    container_name: continuia-mailpit
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    environment:
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mailpit.rule=Host(`mailpit.continuia.ai`) || Host(`mailpit.continuia.health`)"
      - "traefik.http.routers.mailpit.entrypoints=web"
      - "traefik.http.services.mailpit.loadbalancer.server.port=8025"
    networks:
      - continuia

  minio:
    image: minio/minio:RELEASE.2025-07-23T15-54-02Z
    container_name: continuia-minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.minio.rule=Host(`minio.continuia.ai`) || Host(`minio.continuia.health`)"
      - "traefik.http.routers.minio.entrypoints=web"
      - "traefik.http.services.minio.loadbalancer.server.port=9000"
    volumes:
      - ./.data/minio-data:/data
    networks:
      - continuia
    command: server /data

networks:
  ui:
    external: true
  continuia:
    external: true
