#!/bin/bash

# Docker Migration Consolidation Script
# Run from project root: docker-compose exec api bash /app/docker-consolidate-migrations.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔄 Docker Migration Consolidation Process${NC}"
echo ""

# Verify we're in the right environment
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ ERROR: Not in the API directory${NC}"
    echo "This script should be run from inside the API container"
    exit 1
fi

echo -e "${YELLOW}📊 Current Migration Folders:${NC}"
ls -la | grep -E "(migrations|drizzle)" || echo "No migration folders found"
echo ""

echo -e "${YELLOW}📋 Consolidation Plan:${NC}"
echo "1. Apply migrations-clean to fresh database"
echo "2. Seed permissions and initial data"
echo "3. Generate new Drizzle schema"
echo "4. Remove ALL old migration folders"
echo "5. Clean up temporary scripts"
echo ""

# Auto-proceed for Docker execution
echo "Auto-proceeding with consolidation..."

echo ""
echo -e "${BLUE}=== Phase 1: Fresh Database Setup ===${NC}"

# Check database connection
echo -e "${YELLOW}🔌 Testing database connection...${NC}"
if npm run db:validate > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Database connection OK${NC}"
else
    echo -e "${RED}❌ Database connection failed${NC}"
    echo "Please ensure the database is running and DATABASE_URL is correct"
    exit 1
fi

# Apply migrations-clean
echo -e "${YELLOW}📋 Applying migrations-clean...${NC}"
cd migrations-clean
if ./migrate.sh reset && ./migrate.sh up; then
    echo -e "${GREEN}✅ migrations-clean applied successfully${NC}"
else
    echo -e "${RED}❌ Failed to apply migrations-clean${NC}"
    exit 1
fi
cd ..

echo ""
echo -e "${BLUE}=== Phase 2: Seed Data ===${NC}"

# Seed permissions
echo -e "${YELLOW}🔐 Seeding permissions...${NC}"
if npm run db:seed-permissions; then
    echo -e "${GREEN}✅ Permissions seeded${NC}"
else
    echo -e "${RED}❌ Failed to seed permissions${NC}"
    exit 1
fi

# Seed note types
echo -e "${YELLOW}📝 Seeding note types...${NC}"
if npm run db:seed; then
    echo -e "${GREEN}✅ Note types seeded${NC}"
else
    echo -e "${RED}❌ Failed to seed note types${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}=== Phase 3: Generate New Schema ===${NC}"

# Clear existing drizzle folder
echo -e "${YELLOW}🗑️ Clearing old Drizzle migrations...${NC}"
rm -rf drizzle/*
echo -e "${GREEN}✅ Old Drizzle migrations cleared${NC}"

# Generate new schema
echo -e "${YELLOW}🔧 Generating new Drizzle schema...${NC}"
if npm run db:generate; then
    echo -e "${GREEN}✅ New Drizzle schema generated${NC}"
else
    echo -e "${RED}❌ Failed to generate new schema${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}=== Phase 4: Cleanup Migration Folders ===${NC}"

# Create archive directory
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
ARCHIVE_DIR="migration-archive-$TIMESTAMP"
mkdir -p "$ARCHIVE_DIR"

echo -e "${YELLOW}📦 Archiving old migration systems...${NC}"

# Archive and remove old migration folders
if [ -d "src/db/migrations" ]; then
    mv src/db/migrations "$ARCHIVE_DIR/src-db-migrations"
    echo -e "${GREEN}✅ Archived src/db/migrations${NC}"
fi

if [ -d "src/migrations" ]; then
    mv src/migrations "$ARCHIVE_DIR/src-migrations"
    echo -e "${GREEN}✅ Archived src/migrations${NC}"
fi

# Archive migration scripts
if [ -f "run-migrations.cjs" ]; then
    mv run-migrations.cjs "$ARCHIVE_DIR/"
    echo -e "${GREEN}✅ Archived run-migrations.cjs${NC}"
fi

if [ -f "src/run-migrations.ts" ]; then
    mv src/run-migrations.ts "$ARCHIVE_DIR/"
    echo -e "${GREEN}✅ Archived src/run-migrations.ts${NC}"
fi

# Archive docker migration compose
if [ -f "docker-compose.migrations.yml" ]; then
    mv docker-compose.migrations.yml "$ARCHIVE_DIR/"
    echo -e "${GREEN}✅ Archived docker-compose.migrations.yml${NC}"
fi

echo ""
echo -e "${BLUE}=== Phase 5: Clean Temporary Scripts ===${NC}"

# Remove temporary/utility scripts that are no longer needed
echo -e "${YELLOW}🧹 Cleaning temporary scripts...${NC}"

# Archive scripts that were used for migration but not needed anymore
TEMP_SCRIPTS=(
    "add-appointments.ts"
    "create_test_user.js"
    "check_users.js"
)

for script in "${TEMP_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        mv "$script" "$ARCHIVE_DIR/"
        echo -e "${GREEN}✅ Archived $script${NC}"
    fi
done

# Clean up script directories
if [ -d "scripts" ]; then
    mv scripts "$ARCHIVE_DIR/root-scripts"
    echo -e "${GREEN}✅ Archived root scripts folder${NC}"
fi

echo ""
echo -e "${BLUE}=== Phase 6: Final Validation ===${NC}"

# Test the consolidated system
echo -e "${YELLOW}🧪 Testing consolidated system...${NC}"
if npm run db:validate; then
    echo -e "${GREEN}✅ Database validation passed${NC}"
else
    echo -e "${RED}❌ Database validation failed${NC}"
    exit 1
fi

# Show final structure
echo -e "${YELLOW}📁 Final migration structure:${NC}"
echo "✅ drizzle/ - Single source of truth"
echo "✅ migrations-clean/ - Reference (can be removed later)"
echo "✅ src/scripts/ - Seed scripts"
echo "📦 $ARCHIVE_DIR/ - Archived old systems"

echo ""
echo -e "${GREEN}🎉 Migration Consolidation Complete!${NC}"
echo ""
echo -e "${BLUE}=== Summary ===${NC}"
echo "✅ Fresh database from migrations-clean (18 migrations)"
echo "✅ Permissions and roles seeded"
echo "✅ Note types seeded"
echo "✅ New Drizzle schema generated"
echo "✅ All old migration systems archived"
echo "✅ Temporary scripts cleaned up"
echo ""
echo -e "${BLUE}=== Login Credentials ===${NC}"
echo "Admin: <EMAIL> / Continuia"
echo "Doctor: <EMAIL> / Continuia"
echo "Patient: <EMAIL> / Continuia"
echo ""
echo -e "${YELLOW}⚠️  Change passwords in production!${NC}"
echo ""
echo -e "${BLUE}=== Next Steps ===${NC}"
echo "1. Test your application thoroughly"
echo "2. Remove migrations-clean/ when confident: rm -rf migrations-clean"
echo "3. Remove archive when no longer needed: rm -rf $ARCHIVE_DIR"
echo "4. Commit the clean migration system to git"
