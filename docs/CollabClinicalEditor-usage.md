# CollabClinicalEditor Usage Guide

## Overview
The `CollabClinicalEditor` is a React component that provides a Notion-style collaborative clinical note editor with real-time collaboration, medical terminology autocompletion, and CRDT conflict resolution.

## Basic Usage

```tsx
import { CollabClinicalEditor } from '../components/shared/CollabClinicalEditor';

function CaseNotes() {
  return (
    <CollabClinicalEditor
      caseId="case-123"
      noteType="medication"
      placeholder="Enter medication notes..."
      height={500}
      onSave={(content) => console.log('Saved:', content)}
      onError={(error) => console.error('Error:', error)}
    />
  );
}
```

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `caseId` | `string` | ✅ | - | Unique identifier for the case |
| `noteType` | `string` | ✅ | - | Type of note (e.g., 'medication', 'diagnosis', 'clinical_notes') |
| `className` | `string` | ❌ | `''` | Additional CSS classes |
| `placeholder` | `string` | ❌ | `'Start typing...'` | Placeholder text for empty editor |
| `disabled` | `boolean` | ❌ | `false` | Whether the editor is read-only |
| `height` | `number` | ❌ | `400` | Height of the editor in pixels |
| `onSave` | `(content: string) => void` | ❌ | - | Callback when content is saved |
| `onError` | `(error: string) => void` | ❌ | - | Callback when an error occurs |

## Features

### 1. Real-time Collaboration
- Multiple users can edit simultaneously
- User presence indicators show who's online
- Cursor tracking shows where others are typing
- CRDT conflict resolution prevents data loss

### 2. Medical Terminology Autocompletion
- **SNOMED CT**: Type `@snomed:diabetes` to search clinical terms
- **RxNAV**: Type `@med:aspirin` to search medications
- Autocomplete dropdown with keyboard navigation
- Automatic code insertion with proper formatting

### 3. Notion-style Canvas
- Block-based content structure
- Drag & drop functionality (coming soon)
- Multiple block types:
  - Text blocks (paragraphs, headings)
  - Medical blocks (diagnosis, medication, vitals)
  - List blocks (bullet, numbered)
  - Quote blocks
  - Table blocks

### 4. Medical Blocks
Insert specialized medical blocks using keyboard shortcuts:
- `Cmd+Shift+D`: Diagnosis block
- `Cmd+Shift+M`: Medication block
- `Cmd+Shift+V`: Vital signs block
- `Cmd+Shift+A`: Assessment block
- `Cmd+Shift+P`: Plan block

### 5. Rich Text Formatting
- **Bold**, *Italic*, <u>Underline</u>
- Headings (H1, H2, H3)
- Bullet and numbered lists
- Blockquotes
- Tables with resizable columns

## Usage Examples

### Basic Clinical Notes
```tsx
<CollabClinicalEditor
  caseId="case-456"
  noteType="clinical_notes"
  placeholder="Document your clinical observations..."
/>
```

### Medication Notes with Custom Height
```tsx
<CollabClinicalEditor
  caseId="case-789"
  noteType="medication"
  height={600}
  placeholder="Enter medication details, dosages, and instructions..."
  onSave={(content) => {
    // Custom save logic
    saveMedicationNotes(content);
  }}
/>
```

### Read-only Progress Notes
```tsx
<CollabClinicalEditor
  caseId="case-101"
  noteType="progress_note"
  disabled={true}
  placeholder="Progress notes are read-only"
/>
```

### With Error Handling
```tsx
<CollabClinicalEditor
  caseId="case-202"
  noteType="assessment"
  onError={(error) => {
    toast.error(`Editor error: ${error}`);
  }}
  onSave={(content) => {
    toast.success('Assessment saved successfully');
  }}
/>
```

## Medical Terminology Usage

### SNOMED CT Clinical Terms
1. Type `@snomed:` followed by your search term
2. Use arrow keys to navigate suggestions
3. Press Enter or Tab to insert the selected term
4. Example: `@snomed:diabetes` → "Type 2 diabetes mellitus (SNOMED: 44054006)"

### RxNAV Medications
1. Type `@med:` followed by medication name
2. Navigate and select from the dropdown
3. Example: `@med:aspirin` → "Aspirin 81 MG Oral Tablet (RxCUI: 243670)"

## Medical Blocks

### Vital Signs Block
Provides structured input fields for:
- Blood Pressure (mmHg)
- Heart Rate (bpm)
- Temperature (°F)
- Oxygen Saturation (%)

### Medication Block
Includes fields for:
- Dosage (e.g., "10mg")
- Frequency (e.g., "BID", "TID")
- Route (PO, IV, IM, SC, SL, TOP)

### Diagnosis Block
Free-form text with medical code integration

### Assessment & Plan Blocks
Structured areas for clinical assessment and treatment plans

## Collaboration Features

### User Presence
- Colored avatars show active collaborators
- Real-time cursor tracking
- User names and status indicators

### Connection Status
- Green WiFi icon: Connected and syncing
- Red WiFi icon: Offline mode (changes saved locally)
- Auto-reconnection when network is restored

### Conflict Resolution
- Automatic CRDT-based conflict resolution
- No data loss during simultaneous edits
- Seamless merging of changes

## Environment Setup

### Required Environment Variables
```env
REACT_APP_WS_URL=ws://localhost:3001  # WebSocket server URL
REACT_APP_API_URL=http://localhost:3000  # API server URL
```

### Backend Requirements
- WebSocket server for real-time collaboration
- Medical terminology API endpoints
- PostgreSQL database with collaboration tables

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check WebSocket server is running
   - Verify REACT_APP_WS_URL is correct
   - Check network connectivity

2. **Medical Search Not Working**
   - Ensure API server is running
   - Check medical terminology endpoints
   - Verify authentication token

3. **Collaboration Not Syncing**
   - Check WebSocket connection
   - Verify user authentication
   - Check browser console for errors

### Debug Mode
Enable debug logging by setting:
```javascript
localStorage.setItem('debug', 'collab-editor:*');
```

## Performance Considerations

- Editor initializes with lazy loading
- Medical terminology results are cached
- Offline support with IndexedDB persistence
- Debounced auto-save (2 second delay)
- Rate limiting on medical API calls (100/minute)

## Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Security
- All medical data encrypted in transit
- User authentication required
- Role-based access control
- Audit logging for all changes
