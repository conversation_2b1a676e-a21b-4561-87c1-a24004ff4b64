# Unified Note System Implementation Plan

## Overview
Transform the current basic textarea-based note system into a sophisticated Notion-style canvas with real-time collaboration, CRDT conflict resolution, and medical terminology autocompletion.

## Current State Analysis
- **Editor**: Basic `SimpleTextEditor` (textarea)
- **Storage**: Plain text in `structuredContent` JSONB field
- **Collaboration**: None (auto-save only)
- **Medical Features**: None
- **Architecture**: Multiple separate editor components

## Target State
- **Editor**: Tiptap-based canvas with blocks
- **Storage**: yJS CRDT documents + block metadata
- **Collaboration**: Real-time with user presence
- **Medical Features**: SNOMED/RxNAV autocompletion
- **Architecture**: Single unified editor component

## Implementation Phases

### Phase 1: Foundation (Dependencies & Core Setup)
1. Install all required dependencies
2. Set up yJS document structure
3. Create basic Tiptap collaborative editor
4. Implement WebSocket server for real-time sync

### Phase 2: Canvas Interface
1. Implement block-based content structure
2. Add drag & drop functionality
3. Create block templates and types
4. Build nested content support

### Phase 3: Medical Extensions
1. Create medical terminology API abstraction
2. Build SNOMED CT autocompletion extension
3. Build RxNAV medication extension
4. Implement medical block types

### Phase 4: Database & Backend
1. Update database schema for CRDT storage
2. Modify API endpoints for collaboration
3. Add medical API proxy with caching
4. Implement permission system for blocks

### Phase 5: Integration & Migration
1. Replace existing editor components
2. Create migration script for existing notes
3. Update all frontend components
4. Add comprehensive testing

## Key Components to Build

### Frontend Components
```
NotionStyleNoteEditor/
├── TiptapCollaborativeEditor.tsx
├── blocks/
│   ├── TextBlock.tsx
│   ├── MedicalBlock.tsx
│   ├── DiagnosisBlock.tsx
│   ├── MedicationBlock.tsx
│   └── VitalSignsBlock.tsx
├── extensions/
│   ├── SnomedExtension.ts
│   ├── RxNavExtension.ts
│   ├── MedicalAutocomplete.ts
│   └── CollaborationExtension.ts
└── providers/
    ├── YjsProvider.ts
    ├── WebSocketProvider.ts
    └── IndexedDBProvider.ts
```

### Backend Services
```
medical-api/
├── SnomedService.ts
├── RxNavService.ts
├── MedicalApiProxy.ts
└── CachingService.ts

collaboration/
├── WebSocketServer.ts
├── YjsDocumentStore.ts
├── CollaborationMiddleware.ts
└── PermissionService.ts
```

## Database Schema Updates

### Enhanced case_notes Table
```sql
ALTER TABLE case_notes ADD COLUMN yjs_document BYTEA;
ALTER TABLE case_notes ADD COLUMN canvas_blocks JSONB DEFAULT '[]';
ALTER TABLE case_notes ADD COLUMN medical_codes JSONB DEFAULT '{}';
ALTER TABLE case_notes ADD COLUMN active_editors JSONB DEFAULT '[]';
ALTER TABLE case_notes ADD COLUMN document_version INTEGER DEFAULT 1;
```

### New Tables
```sql
-- Medical terminology cache
CREATE TABLE medical_terminology_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  terminology_type VARCHAR(20) NOT NULL, -- 'snomed' or 'rxnav'
  search_term VARCHAR(255) NOT NULL,
  results JSONB NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Collaboration sessions
CREATE TABLE collaboration_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  case_id UUID NOT NULL REFERENCES cases(id),
  note_type_id UUID NOT NULL REFERENCES note_types(id),
  user_id UUID NOT NULL REFERENCES users(id),
  session_start TIMESTAMP DEFAULT NOW(),
  session_end TIMESTAMP,
  is_active BOOLEAN DEFAULT true
);
```

## API Endpoints to Create/Modify

### Medical Terminology APIs
- `GET /api/medical/snomed/search?term={term}` - Search SNOMED CT
- `GET /api/medical/rxnav/drugs?name={name}` - Search medications
- `GET /api/medical/codes/{code}` - Get code details

### Collaboration APIs
- `WebSocket /ws/notes/{caseId}/{noteType}` - Real-time collaboration
- `POST /api/notes/{caseId}/{noteType}/collaborate` - Join collaboration
- `DELETE /api/notes/{caseId}/{noteType}/collaborate` - Leave collaboration

### Enhanced Note APIs
- `GET /api/notes/{caseId}/{noteType}/blocks` - Get block structure
- `POST /api/notes/{caseId}/{noteType}/blocks` - Update blocks
- `GET /api/notes/{caseId}/{noteType}/history` - Version history

## Migration Strategy

### Data Migration
1. **Preserve Existing Data**: Convert plain text to Tiptap JSON format
2. **Block Structure**: Create single text block for existing content
3. **Medical Codes**: Extract and tag existing medical terms
4. **Versioning**: Maintain version history during migration

### Rollout Strategy
1. **Feature Flag**: Enable new editor per note type
2. **Gradual Migration**: Start with new notes, migrate old ones
3. **Fallback**: Keep old editor as backup during transition
4. **User Training**: Provide documentation and tutorials

## Success Metrics
- **Performance**: Sub-100ms collaboration sync
- **Reliability**: 99.9% uptime for real-time features
- **Usability**: Reduced note creation time by 30%
- **Medical Accuracy**: 95% accuracy in terminology suggestions
- **Adoption**: 80% user adoption within 3 months
