// Example: How to integrate CollabClinicalEditor into existing components

import React from 'react';
import { CollabClinicalEditor } from '../components/shared/CollabClinicalEditor';
import toast from 'react-hot-toast';

// Example 1: Replace existing UnifiedNoteEditor
function MedicalOpinionSection({ caseData }: { caseData: any }) {
  return (
    <div className="medical-opinion-section">
      <h2 className="text-xl font-semibold mb-4">Medical Opinion</h2>
      
      {/* Replace the old UnifiedNoteEditor with CollabClinicalEditor */}
      <CollabClinicalEditor
        caseId={caseData.id}
        noteType="medical_opinion"
        placeholder="📋 COMPREHENSIVE MEDICAL OPINION..."
        height={800}
        className="w-full"
        onSave={(content) => {
          toast.success('Medical opinion saved');
        }}
        onError={(error) => {
          toast.error(`Error: ${error}`);
        }}
      />
    </div>
  );
}

// Example 2: Clinical Notes with Medical Blocks
function ClinicalNotesSection({ caseId }: { caseId: string }) {
  return (
    <div className="clinical-notes-section">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Clinical Notes</h3>
        <div className="text-sm text-gray-500">
          Use medical blocks for structured documentation
        </div>
      </div>
      
      <CollabClinicalEditor
        caseId={caseId}
        noteType="clinical_notes"
        placeholder="Start documenting clinical findings... Use Cmd+Shift+D for diagnosis, Cmd+Shift+M for medications"
        height={600}
        onSave={(content) => {
          // Custom save logic if needed
          console.log('Clinical notes saved:', content);
        }}
      />
    </div>
  );
}

// Example 3: Medication Management
function MedicationSection({ caseId }: { caseId: string }) {
  return (
    <div className="medication-section bg-green-50 p-4 rounded-lg">
      <h3 className="text-lg font-medium text-green-800 mb-4">
        💊 Medication Management
      </h3>
      
      <CollabClinicalEditor
        caseId={caseId}
        noteType="medication"
        placeholder="Document medications... Type @med: to search for drugs"
        height={400}
        className="bg-white rounded border"
      />
      
      <div className="mt-2 text-xs text-green-700">
        💡 Tip: Use @med: to search medications and Cmd+Shift+M for structured medication blocks
      </div>
    </div>
  );
}

// Example 4: Assessment & Plan
function AssessmentPlanSection({ caseId }: { caseId: string }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Assessment */}
      <div className="assessment-section">
        <h3 className="text-lg font-medium mb-3">Assessment</h3>
        <CollabClinicalEditor
          caseId={caseId}
          noteType="assessment"
          placeholder="Clinical assessment... Use @snomed: for medical terms"
          height={350}
        />
      </div>
      
      {/* Plan */}
      <div className="plan-section">
        <h3 className="text-lg font-medium mb-3">Treatment Plan</h3>
        <CollabClinicalEditor
          caseId={caseId}
          noteType="plan"
          placeholder="Treatment plan and next steps..."
          height={350}
        />
      </div>
    </div>
  );
}

// Example 5: Vital Signs with Structured Data
function VitalSignsSection({ caseId }: { caseId: string }) {
  return (
    <div className="vitals-section bg-red-50 p-4 rounded-lg">
      <h3 className="text-lg font-medium text-red-800 mb-4">
        📊 Vital Signs
      </h3>
      
      <CollabClinicalEditor
        caseId={caseId}
        noteType="vitals"
        placeholder="Record vital signs... Use Cmd+Shift+V for structured vital signs block"
        height={300}
        className="bg-white rounded border"
      />
    </div>
  );
}

// Example 6: Complete Case Detail Integration
function CaseDetailPage({ caseId }: { caseId: string }) {
  return (
    <div className="case-detail-page max-w-6xl mx-auto p-6">
      <div className="space-y-8">
        {/* Header */}
        <div className="case-header">
          <h1 className="text-2xl font-bold">Case Details</h1>
          <p className="text-gray-600">Collaborative clinical documentation</p>
        </div>
        
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Left Column - Primary Notes */}
          <div className="xl:col-span-2 space-y-6">
            <MedicalOpinionSection caseData={{ id: caseId }} />
            <ClinicalNotesSection caseId={caseId} />
            <AssessmentPlanSection caseId={caseId} />
          </div>
          
          {/* Right Column - Structured Data */}
          <div className="space-y-6">
            <VitalSignsSection caseId={caseId} />
            <MedicationSection caseId={caseId} />
            
            {/* Progress Notes */}
            <div className="progress-notes">
              <h3 className="text-lg font-medium mb-3">Progress Notes</h3>
              <CollabClinicalEditor
                caseId={caseId}
                noteType="progress_note"
                placeholder="Track patient progress..."
                height={250}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Example 7: Read-only View for Patients
function PatientViewNotes({ caseId, noteType }: { caseId: string; noteType: string }) {
  return (
    <div className="patient-notes-view">
      <div className="bg-blue-50 p-3 rounded-lg mb-4">
        <p className="text-sm text-blue-800">
          📖 This is a read-only view of your clinical notes
        </p>
      </div>
      
      <CollabClinicalEditor
        caseId={caseId}
        noteType={noteType}
        disabled={true}
        placeholder="No notes available"
        height={400}
        className="opacity-90"
      />
    </div>
  );
}

export {
  MedicalOpinionSection,
  ClinicalNotesSection,
  MedicationSection,
  AssessmentPlanSection,
  VitalSignsSection,
  CaseDetailPage,
  PatientViewNotes,
};
