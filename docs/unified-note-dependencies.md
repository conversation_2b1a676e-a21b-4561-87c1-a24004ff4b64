# Unified Note System Dependencies

## Core Collaboration & CRDT
```bash
npm install yjs y-websocket y-indexeddb
npm install @tiptap/extension-collaboration @tiptap/extension-collaboration-cursor
npm install @hocuspocus/server @hocuspocus/extension-database
npm install socket.io socket.io-client
```

## Notion-Style Canvas Interface
```bash
npm install @tiptap/extension-dropcursor @tiptap/extension-gapcursor
npm install @tiptap/extension-block-quote @tiptap/extension-bullet-list
npm install @tiptap/extension-ordered-list @tiptap/extension-list-item
npm install @tiptap/extension-table @tiptap/extension-table-row
npm install @tiptap/extension-table-cell @tiptap/extension-table-header
npm install @dnd-kit/core @dnd-kit/sortable @dnd-kit/utilities
npm install react-beautiful-dnd
```

## Medical Terminology Extensions
```bash
npm install fuse.js  # For fuzzy search in medical terms
npm install debounce # For API call optimization
npm install axios    # Already installed - for API calls
```

## Rich Text & Formatting
```bash
npm install @tiptap/extension-text-style @tiptap/extension-color
npm install @tiptap/extension-highlight @tiptap/extension-underline
npm install @tiptap/extension-subscript @tiptap/extension-superscript
npm install @tiptap/extension-code-block @tiptap/extension-image
```

## Backend Dependencies (Node.js)
```bash
npm install ws  # WebSocket server
npm install node-cache  # For caching medical API responses
npm install rate-limiter-flexible  # Rate limiting
npm install pg  # PostgreSQL client (already installed)
```

## Medical API Endpoints

### SNOMED CT Public API
- **Base URL**: `https://browser.ihtsdotools.org/snowstorm/snomed-ct`
- **Search**: `/browser/MAIN/concepts?term={searchTerm}&activeFilter=true&limit=20`
- **Concept Details**: `/browser/MAIN/concepts/{conceptId}`

### RxNAV Public API  
- **Base URL**: `https://rxnav.nlm.nih.gov/REST`
- **Drug Search**: `/drugs.json?name={drugName}`
- **NDC Search**: `/ndcstatus.json?ndc={ndc}`
- **Drug Interactions**: `/interaction/interaction.json?rxcui={rxcui}`

## Block Types for Canvas Interface

### Standard Blocks
- Text Block (paragraph, heading)
- List Block (bullet, numbered)
- Quote Block
- Code Block
- Table Block
- Image Block

### Medical Blocks
- Diagnosis Block (with SNOMED codes)
- Medication Block (with RxNAV codes)
- Vital Signs Block
- Lab Results Block
- Assessment Block
- Plan Block

## Features to Implement

### Canvas Features
- [x] Block-based editing
- [x] Drag & drop reordering
- [x] Nested content
- [x] Block templates
- [x] Real-time collaboration
- [x] Block comments
- [x] Block-level permissions

### Medical Features
- [x] SNOMED CT autocompletion
- [x] RxNAV drug search
- [x] Medical code insertion
- [x] Terminology validation
- [x] Dosage suggestions
- [x] Drug interaction warnings
- [x] Medical abbreviation expansion

### Collaboration Features
- [x] Real-time editing
- [x] User presence indicators
- [x] Cursor tracking
- [x] Conflict resolution (CRDT)
- [x] Version history
- [x] Offline support
- [x] Auto-save
