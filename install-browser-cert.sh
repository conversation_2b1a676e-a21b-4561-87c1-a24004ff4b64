#!/bin/bash

# Browser-Specific SSL Certificate Installation Script
# This script helps install the Continuia SSL certificate in different browsers

set -e

CERT_FILE="./certs/continuia.health.crt"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CERT_PATH="$SCRIPT_DIR/$CERT_FILE"

echo "🔐 Browser SSL Certificate Installation for Continuia Healthcare Platform"
echo "========================================================================"

# Check if certificate file exists
if [ ! -f "$CERT_PATH" ]; then
    echo "❌ Certificate file not found: $CERT_PATH"
    echo "Please ensure you're running this script from the my.continuia directory"
    exit 1
fi

echo "📋 Available installation methods:"
echo "1. System-wide installation (recommended - works for Chrome, Edge, Safari)"
echo "2. Firefox-specific installation"
echo "3. Manual browser instructions"
echo "4. Show certificate information"
echo ""

read -p "Choose an option (1-4): " choice

case $choice in
    1)
        echo "🔧 Installing certificate system-wide..."
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            if [ "$EUID" -ne 0 ]; then
                echo "⚠️  System installation requires sudo privileges"
                echo "Running: sudo cp \"$CERT_PATH\" /usr/local/share/ca-certificates/continuia.health.crt"
                sudo cp "$CERT_PATH" /usr/local/share/ca-certificates/continuia.health.crt
                sudo update-ca-certificates
            else
                cp "$CERT_PATH" /usr/local/share/ca-certificates/continuia.health.crt
                update-ca-certificates
            fi
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            echo "🍎 Installing on macOS..."
            sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain "$CERT_PATH"
        else
            echo "❓ Unsupported OS for automatic installation"
            echo "Please follow manual instructions in BROWSER_SSL_SETUP.md"
            exit 1
        fi
        
        echo "✅ Certificate installed system-wide!"
        echo "🔄 Please restart your browser for changes to take effect"
        echo ""
        echo "🌐 Test these URLs after restarting your browser:"
        echo "   • https://continuia.health"
        echo "   • https://care.continuia.health"
        echo "   • https://desk.continuia.health"
        echo "   • https://api.continuia.health"
        ;;
        
    2)
        echo "🦊 Firefox Certificate Installation Instructions:"
        echo ""
        echo "Firefox uses its own certificate store. Follow these steps:"
        echo ""
        echo "1. Open Firefox and go to: about:preferences#privacy"
        echo "2. Scroll down to 'Certificates' section"
        echo "3. Click 'View Certificates'"
        echo "4. Go to 'Authorities' tab"
        echo "5. Click 'Import...'"
        echo "6. Browse to: $(realpath "$CERT_PATH")"
        echo "7. Check 'Trust this CA to identify websites'"
        echo "8. Click 'OK'"
        echo "9. Restart Firefox"
        echo ""
        echo "📋 Certificate file location: $(realpath "$CERT_PATH")"
        
        # Try to open Firefox certificate manager (if Firefox is installed)
        if command -v firefox &> /dev/null; then
            read -p "🚀 Open Firefox certificate manager now? (y/n): " open_firefox
            if [[ $open_firefox =~ ^[Yy]$ ]]; then
                firefox about:preferences#privacy &
                echo "📖 Firefox opened. Follow the instructions above."
            fi
        fi
        ;;
        
    3)
        echo "📖 Manual Browser Installation Instructions:"
        echo ""
        echo "📁 Certificate location: $(realpath "$CERT_PATH")"
        echo ""
        echo "🌐 Chrome/Edge/Chromium:"
        echo "   1. Go to Settings → Privacy and security → Security → Manage certificates"
        echo "   2. Click 'Import' and select the certificate file"
        echo "   3. Choose 'Trusted Root Certification Authorities' store"
        echo "   4. Restart browser"
        echo ""
        echo "🦊 Firefox:"
        echo "   1. Go to about:preferences#privacy"
        echo "   2. Certificates → View Certificates → Authorities tab"
        echo "   3. Import certificate and trust for websites"
        echo "   4. Restart browser"
        echo ""
        echo "🍎 Safari (macOS):"
        echo "   1. Double-click the certificate file"
        echo "   2. Add to System keychain (requires admin password)"
        echo "   3. In Keychain Access, set trust to 'Always Trust'"
        echo "   4. Restart Safari"
        echo ""
        echo "📚 For detailed instructions, see: BROWSER_SSL_SETUP.md"
        ;;
        
    4)
        echo "🔍 Certificate Information:"
        echo "========================================"
        openssl x509 -in "$CERT_PATH" -text -noout | grep -A 5 "Subject:"
        echo ""
        openssl x509 -in "$CERT_PATH" -text -noout | grep -A 10 "Subject Alternative Name"
        echo ""
        echo "📅 Validity:"
        openssl x509 -in "$CERT_PATH" -dates -noout
        echo ""
        echo "🔐 Fingerprint:"
        openssl x509 -in "$CERT_PATH" -fingerprint -noout
        ;;
        
    *)
        echo "❌ Invalid option. Please choose 1-4."
        exit 1
        ;;
esac

echo ""
echo "🆘 Need help? Check these resources:"
echo "   📚 Detailed guide: BROWSER_SSL_SETUP.md"
echo "   🔧 SSL configuration: SSL_CONFIGURATION.md"
echo "   🌐 Test authentication at: https://care.continuia.health"