#!/bin/bash

# Parse DATABASE_URL and export individual components
# This script extracts POSTGRES_* variables from DATABASE_URL for legacy compatibility

set -e

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is not set"
    echo "Please set DATABASE_URL in the format: postgresql://user:password@host:port/database"
    exit 1
fi

echo "🔍 Parsing DATABASE_URL..."
echo "DATABASE_URL: $DATABASE_URL"

# Parse the URL using a more robust method
# Format: postgresql://user:password@host:port/database

# Remove the protocol
URL_WITHOUT_PROTOCOL=${DATABASE_URL#postgresql://}
URL_WITHOUT_PROTOCOL=${URL_WITHOUT_PROTOCOL#postgres://}

# Extract user and password (before @)
USER_PASS=${URL_WITHOUT_PROTOCOL%%@*}
POSTGRES_USER=${USER_PASS%%:*}
POSTGRES_PASSWORD=${USER_PASS#*:}

# Extract host, port, and database (after @)
HOST_PORT_DB=${URL_WITHOUT_PROTOCOL#*@}
HOST_PORT=${HOST_PORT_DB%%/*}
POSTGRES_HOST=${HOST_PORT%%:*}
POSTGRES_PORT=${HOST_PORT#*:}

# If port is same as host, default to 5432
if [ "$POSTGRES_PORT" = "$POSTGRES_HOST" ]; then
    POSTGRES_PORT=5432
fi

# Extract database name
POSTGRES_DB=${HOST_PORT_DB#*/}

# Remove any query parameters from database name
POSTGRES_DB=${POSTGRES_DB%%\?*}

# Export the variables
export POSTGRES_USER
export POSTGRES_PASSWORD
export POSTGRES_HOST
export POSTGRES_PORT
export POSTGRES_DB

echo "✅ Parsed DATABASE_URL successfully:"
echo "   POSTGRES_USER: $POSTGRES_USER"
echo "   POSTGRES_PASSWORD: [HIDDEN]"
echo "   POSTGRES_HOST: $POSTGRES_HOST"
echo "   POSTGRES_PORT: $POSTGRES_PORT"
echo "   POSTGRES_DB: $POSTGRES_DB"

# If this script is sourced, the variables will be available in the parent shell
# If this script is executed, it will just show the parsed values

# Optional: Write to a .env file for Docker Compose
if [ "$1" = "--write-env" ]; then
    ENV_FILE=".env.parsed"
    echo "📝 Writing parsed variables to $ENV_FILE..."
    
    cat > "$ENV_FILE" << EOF
# Auto-generated from DATABASE_URL
# Generated on: $(date)
DATABASE_URL=$DATABASE_URL
POSTGRES_USER=$POSTGRES_USER
POSTGRES_PASSWORD=$POSTGRES_PASSWORD
POSTGRES_HOST=$POSTGRES_HOST
POSTGRES_PORT=$POSTGRES_PORT
POSTGRES_DB=$POSTGRES_DB
EOF
    
    echo "✅ Variables written to $ENV_FILE"
    echo "You can now use: docker-compose --env-file $ENV_FILE up"
fi

# Test the connection if psql is available
if command -v psql >/dev/null 2>&1; then
    if [ "$1" = "--test" ]; then
        echo "🔌 Testing database connection..."
        if PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -p "$POSTGRES_PORT" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "SELECT version();" >/dev/null 2>&1; then
            echo "✅ Database connection successful!"
        else
            echo "❌ Database connection failed!"
            exit 1
        fi
    fi
else
    if [ "$1" = "--test" ]; then
        echo "⚠️  psql not available, skipping connection test"
    fi
fi
