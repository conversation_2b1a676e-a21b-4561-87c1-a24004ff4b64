#!/bin/bash

# Script to run migration consolidation inside Docker container

echo "🐳 Running Migration Consolidation in Docker..."
echo ""

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose not found. Please install Docker Compose."
    exit 1
fi

# Check if containers are running
if ! docker-compose ps | grep -q "continuia-api.*running"; then
    echo "❌ API container is not running. Please start with:"
    echo "   docker-compose up -d"
    exit 1
fi

# Copy the consolidation script to the container
echo "📋 Copying consolidation script to container..."
docker cp docker-consolidate-migrations.sh continuia-api:/app/

# Make it executable
docker-compose exec api chmod +x /app/docker-consolidate-migrations.sh

# Run the consolidation
echo "🚀 Running consolidation inside container..."
docker-compose exec api bash /app/docker-consolidate-migrations.sh

echo ""
echo "✅ Consolidation complete! Check the output above for results."
