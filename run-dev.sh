#!/bin/bash

# Development Docker Compose Runner for My.Continuia
# This script runs the development environment with proper API connectivity

echo "🚀 Starting My.Continuia Development Environment..."

# Check if .env.docker exists
if [ ! -f ".env.docker" ]; then
    echo "❌ .env.docker file not found. Please create it first."
    exit 1
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.dev.yml down

# Build and start services
echo "🔨 Building and starting services..."
docker-compose -f docker-compose.dev.yml up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🔍 Checking service health..."
echo "API Health: $(curl -s http://localhost:3001/health | jq -r '.status' 2>/dev/null || echo 'Not ready')"
echo "Care App: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:3000 2>/dev/null || echo 'Not ready')"
echo "Desk App: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:3002 2>/dev/null || echo 'Not ready')"

echo ""
echo "✅ Development environment started!"
echo ""
echo "📱 Care App (Patient): http://care.continuia.ai:3000 (or http://localhost:3000)"
echo "💼 Desk App (Doctor/Admin): http://desk.continuia.ai:3002 (or http://localhost:3002)"
echo "🔗 API Server: http://api.continuia.ai:3001 (or http://localhost:3001)"
echo "📧 Mailpit (Email testing): http://localhost:8025"
echo "📦 MinIO Console: http://localhost:9001"
echo ""
echo "⚠️  Note: Make sure your DNS/hosts file points the *.continuia.ai domains to 127.0.0.1"
echo ""
echo "To view logs: docker-compose -f docker-compose.dev.yml logs -f"
echo "To stop: docker-compose -f docker-compose.dev.yml down"
