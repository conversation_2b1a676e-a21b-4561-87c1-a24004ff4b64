#!/bin/bash

# Update package.json to remove old migration commands and keep only Drizzle Kit

echo "🔧 Updating package.json scripts..."

# Backup original package.json
cp api/package.json api/package.json.backup

# Create updated scripts section
cat > temp_scripts.json << 'EOF'
{
  "scripts": {
    "dev": "tsx watch -r tsconfig-paths/register src/index.ts",
    "build": "tsc",
    "start": "node dist/index.js",
    "db:generate": "drizzle-kit generate",
    "db:migrate": "drizzle-kit push:pg",
    "db:studio": "drizzle-kit studio",
    "db:seed": "tsx src/scripts/seed.ts",
    "db:seed-permissions": "tsx src/scripts/seed-permissions.ts",
    "db:seed-test-data": "tsx src/scripts/wipe-and-seed-test-data.ts",
    "db:validate": "tsx src/scripts/validate-db-config.ts",
    "lint": "eslint src --ext .ts",
    "type-check": "tsc --noEmit"
  }
}
EOF

# Use Node.js to merge the scripts
node -e "
const fs = require('fs');
const pkg = JSON.parse(fs.readFileSync('api/package.json', 'utf8'));
const newScripts = JSON.parse(fs.readFileSync('temp_scripts.json', 'utf8')).scripts;
pkg.scripts = newScripts;
fs.writeFileSync('api/package.json', JSON.stringify(pkg, null, 2) + '\n');
"

# Clean up temp file
rm temp_scripts.json

echo "✅ package.json updated with clean migration scripts"
echo ""
echo "📋 Available commands after consolidation:"
echo "  npm run db:generate     - Generate migrations from schema changes"
echo "  npm run db:migrate      - Apply migrations to database"
echo "  npm run db:studio       - Open Drizzle Studio"
echo "  npm run db:seed         - Seed note types"
echo "  npm run db:seed-permissions - Seed roles and permissions"
echo "  npm run db:seed-test-data   - Seed test data (120 cases, users)"
echo "  npm run db:validate     - Validate database connection"
